package com.app.messej

import android.app.Application
import android.content.Context
import com.app.messej.data.Constants
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase

class MainApplication : Application() {

    init {
        instance = this
    }

    companion object {
        private var instance: MainApplication? = null

        fun applicationContext() : Context {
            return instance!!.applicationContext
        }

        fun applicationInstance(): Application {
            return instance!!
        }
    }

    override fun onCreate() {
        super.onCreate()
        // initialize for any
        Firebase.crashlytics.isCrashlyticsCollectionEnabled = listOf(Constants.BuildType.RELEASE.value, Constants.BuildType.QA.value).contains(BuildConfig.BUILD_TYPE)

        // example: SharedPreferences etc...
        //val context: Context = MainApplication.applicationContext()
    }
}