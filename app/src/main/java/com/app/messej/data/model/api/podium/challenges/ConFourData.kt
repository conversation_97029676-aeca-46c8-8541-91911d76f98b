package com.app.messej.data.model.api.podium.challenges

import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard.Companion.parseBoard
import com.app.messej.ui.home.publictab.podiums.challenges.confour.ConnectFourBoard.Companion.unParseBoard
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.annotations.SerializedName

data class ConFourData (
    @SerializedName("current_player"     ) override var currentPlayerUserId    : Int? = null,
    @SerializedName("turn_start_time"    ) override val turnStartTime          : Double? = null,
    @SerializedName("matrix"             ) private var _board                  : List<List<Int>>? = null,
    ): BoardData() {

    companion object {
        const val TURN_DURATION_SECONDS = 15L
        const val TURN_DURATION_MILLIS = TURN_DURATION_SECONDS*1000L
    }

    override val turnDurationSeconds: Long
        get() = TURN_DURATION_SECONDS

    var board: List<List<SlotState>>
        get() {
            try {
                return _board?.parseBoard() ?: List(ConnectFourBoard.ROWS) {
                    List(ConnectFourBoard.COLS) {
                        SlotState.EMPTY
                    }
                }
            } catch (e: Exception) {
                Firebase.crashlytics.log("Crashed while trying to parse board: $_board")
                Firebase.crashlytics.recordException(e)
                return List(ConnectFourBoard.ROWS) {
                    List(ConnectFourBoard.COLS) {
                        SlotState.EMPTY
                    }
                }
            }
        }
        set(value) {
            _board = value.unParseBoard()
        }
}
