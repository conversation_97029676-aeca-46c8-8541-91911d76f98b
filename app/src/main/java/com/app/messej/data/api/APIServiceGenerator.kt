package com.app.messej.data.api

import android.util.Log
import com.app.messej.BuildConfig
import com.app.messej.MainApplication
import com.app.messej.data.Constants
import com.app.messej.data.model.OAuthTokens
import com.app.messej.data.model.api.ErrorResponse
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.ui.utils.LocaleUtil
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.Authenticator
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object APIServiceGenerator {

    private const val HEADER_AUTHORIZATION = "Authorization"
    private const val HEADER_AUTHORIZATION_PREFIX = "Bearer"
    private const val HEADER_LANGUAGE = "language"
    private const val TIME_OUT = 30L

    private fun addAuthPrefix(token: String): String = "$HEADER_AUTHORIZATION_PREFIX $token"

    private var client: Retrofit? = null

    private var authClient: Retrofit? = null

    private var logoutClient: Retrofit? = null

    private fun getBaseHttpClientBuilder(addLogger: Boolean = true): OkHttpClient.Builder {
        val httpClient = OkHttpClient.Builder()
        if(BuildConfig.BUILD_TYPE != Constants.BuildType.RELEASE.value && addLogger) {
            val logging = HttpLoggingInterceptor()
            logging.setLevel(HttpLoggingInterceptor.Level.BODY)
            httpClient.addInterceptor(logging)
        }
        return httpClient
    }

    private fun getRetrofitInstance(client: OkHttpClient.Builder): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .client(client
                        .connectTimeout(TIME_OUT, TimeUnit.SECONDS)
                        .writeTimeout(TIME_OUT, TimeUnit.SECONDS)
                        .readTimeout(TIME_OUT, TimeUnit.SECONDS)
                        .build())
            .build()
    }

    private fun getClient(): Retrofit {
        client?.let { return it }
        val httpClient = getBaseHttpClientBuilder()
        httpClient.addInterceptor { chain ->
            val originalRequest = chain.request()
            val modifiedRequest = originalRequest.newBuilder()
                .addHeader(HEADER_LANGUAGE, LocaleUtil.getAppLocale().apiCode)
                .build()
            chain.proceed(modifiedRequest)
        }
        val retrofit = getRetrofitInstance(httpClient)
        client = retrofit
        return retrofit
    }

    /**
     * Should be called when a token is refreshed or expires. Otherwise the APIService may keep using the old token
     */
    fun invalidateAuthApiService() {
        authClient = null
    }

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())

    private fun getAuthClient(): Retrofit {

        authClient?.let { return it }

        val authenticator = object: Authenticator {

            @Synchronized // annotate with @Synchronized to force parallel threads/coroutines to block and wait in an ordered manner when accessing authenticate()
            override fun authenticate(route: Route?, response: Response): Request? {
                try {
                    Firebase.crashlytics.log("authenticator: ${response.request.url} failed due to token. regenerating")
                    val tokens = accountRepo.tokens ?: throw Exception("Cannot call API without an access token.")
                    Firebase.crashlytics.log("authenticator: ${response.request.url} current token is ${tokens.accessToken}")

                    val alreadyRefreshed = response.request.header(HEADER_AUTHORIZATION)?.contains(tokens.accessToken, true) == false
                    if (alreadyRefreshed) {
                        Firebase.crashlytics.log("authenticator: token seems to be already refreshed: using new token: ${tokens.accessToken}")
                        return response.request.newBuilder().header(HEADER_AUTHORIZATION, addAuthPrefix(tokens.accessToken)).build()
                    }
                    Log.e("HTTPAUTH", "getAuthClient: token ${tokens.accessToken} has failed for ${response.request.url}. regenerating")
                    Firebase.crashlytics.log("authenticator: ${response.request.url} refresh token is ${tokens.refreshToken}")
                    val tokenResponse = createService(NoAuthAPIService::class.java, false).refreshToken(addAuthPrefix(tokens.refreshToken)).execute()
                    Firebase.crashlytics.log("authenticator: ${response.request.url} refresh response: ${tokenResponse.code()}: ${tokenResponse.body()}")
                    if (tokenResponse.code() == 401 && accountRepo.loggedIn) {
                        Firebase.crashlytics.log("authenticator: token refresh failed. logging out user")
                        Firebase.crashlytics.recordException(Exception(ErrorResponse.parseError(response = tokenResponse.errorBody()).message))
                        CoroutineScope(Dispatchers.IO).launch {
                            AuthenticationRepository(MainApplication.applicationInstance()).logoutUser(true)
                        }
                        return null
                    }
                    val newAccessToken = tokenResponse.body()?.result?.accessToken ?: return null
                    Firebase.crashlytics.log("authenticator: ${response.request.url} got new token: $newAccessToken")
                    accountRepo.updateTokens(OAuthTokens(newAccessToken, tokens.refreshToken))
                    invalidateAuthApiService()
                    return response.request.newBuilder().header(HEADER_AUTHORIZATION, addAuthPrefix(newAccessToken)).build()
                } catch (e: Exception) {
                    return null
                }
            }
        }

        val httpClient = getBaseHttpClientBuilder()
        httpClient
            .addInterceptor(Interceptor { chain ->
                // Firebase.crashlytics.log("entered interceptor")
                if (!accountRepo.loggedIn) {
                    Firebase.crashlytics.log("accessed ${chain.request().url} without an access token")
                    // set up the request to fail
                    return@Interceptor chain.proceed(chain.request())
                }
                // Firebase.crashlytics.log("checking tokens")
                val tokens = accountRepo.tokens ?: throw Exception("Cannot call API without an access token.")
                val accessToken = tokens.accessToken
                 Firebase.crashlytics.log("calling API: ${chain.request().url}")
                val builder =
                    chain.request().newBuilder().addHeader(HEADER_AUTHORIZATION, addAuthPrefix(accessToken)).addHeader(HEADER_LANGUAGE, LocaleUtil.getAppLocale().apiCode)
                // Firebase.crashlytics.log("continuing request")
                val newRequest = builder.build()
                try {
                    chain.proceed(newRequest)
                } catch (e: Exception) {
                    Firebase.crashlytics.log("${chain.request().url}: error: ${e.message}")
                    throw e
                }
            })
            .authenticator(authenticator)


        val retrofit = getRetrofitInstance(httpClient)
        authClient = retrofit
        return retrofit
    }

    private fun getLogoutClient(): Retrofit {

        logoutClient?.let { return it }

        val httpClient = getBaseHttpClientBuilder()
        httpClient
            .addInterceptor(object: Interceptor {
                override fun intercept(chain: Interceptor.Chain): Response {
                    Firebase.crashlytics.log("entered interceptor")
                    if (!accountRepo.loggedIn) {
                        Firebase.crashlytics.log("accessed ${chain.request().url} without an access token")
                        throw Exception("Cannot access API without logging in")
                    }
                    Firebase.crashlytics.log("checking tokens")
                    val tokens = accountRepo.tokens ?: throw Exception("Cannot call API without an access token.")
                    val refreshToken = tokens.refreshToken

                    Firebase.crashlytics.log("inserting headers")
                    val builder =
                        chain.request().newBuilder()
                            .addHeader(HEADER_AUTHORIZATION, addAuthPrefix(refreshToken))
                            .addHeader(HEADER_LANGUAGE, LocaleUtil.getAppLocale().apiCode)
                    Firebase.crashlytics.log("continuing request")
                    val newRequest = builder.build()
                    return chain.proceed(newRequest)
                }

            })


        val retrofit = getRetrofitInstance(httpClient)
        logoutClient = retrofit
        return retrofit
    }

    fun <S> createService(serviceClass: Class<S>, requiresAuth: Boolean = true): S {
        val client = if(requiresAuth) getAuthClient() else getClient()
        return client.create(serviceClass)
    }

    fun <S> logoutService(serviceClass: Class<S>): S {
        return getLogoutClient().create(serviceClass)
    }
}