package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PodiumAPIService
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.enums.UserCitizenship
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PodiumLiveUsersListDataSource(private val api: PodiumAPIService, private val podiumId: String, val searchTerm: String? = null, private val exclude : UserCitizenship? = null): PagingSource<Int, PodiumParticipant>() {
    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, PodiumParticipant> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                Log.d("PLULDS", "loading live users")
                val response = api.getLiveUsersList(podiumId, page = currentPage, searchTerm = searchTerm, exclude = exclude)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (!data.hasNext) null else currentPage.inc()

                LoadResult.Page(
                    data = data.users, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            Log.e("PLULDS", "load error",e )
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, PodiumParticipant>): Int? {
        return null
    }
}