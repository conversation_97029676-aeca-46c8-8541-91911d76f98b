package com.app.messej.data.model.api.podium.challenges

import androidx.annotation.ColorRes
import com.app.messej.R
import com.app.messej.data.model.AbstractUser
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.ui.utils.DataFormatHelper
import com.google.gson.annotations.SerializedName
import kotlin.math.roundToInt

typealias PenaltyScoreBoard = List<List<PodiumChallengeScore.PenaltyTurnStatus>>

data class PodiumChallengeScore(
    @SerializedName(value="id", alternate = ["user_id"]) override val id           : Int,
    @SerializedName("score"                  ) var score            : Double,

    @SerializedName("color"                  ) val _color            : String? = null,
    @SerializedName("winner"                 ) private val _winner           : Int? = 0,
    @SerializedName("coins_won"              ) val coinsWon         : Double? = 0.0,
    @SerializedName("participant_token_number") val participantTokenNumber: Int? = null,
    @SerializedName("player_level"            ) val playerLevel: Int? = null,
    @SerializedName("total_losses"            ) val totalLosses: Int? = 0,
    @SerializedName("total_wins"              ) val totalWins: Int? = 0,

    @SerializedName("name"       ) override val name        : String,
    @SerializedName("username"   ) override val username    : String,
    @SerializedName("thumbnail"  ) override val thumbnail   : String?,
    @SerializedName("is_premium" ) val isPremium  : Boolean,
    @SerializedName("citizenship") override val citizenship : UserCitizenship?,

    //For Penalty
    @SerializedName("player_role"             ) var playerRole: PenaltyPlayerRole? = null,
    @SerializedName("player_status"           ) val playerStatus: PenaltyPlayerStatus? = null,
    @SerializedName("score_board"             ) var _scoreBoard: List<List<Int>>? = null,

    //For Knowledge Race
    @SerializedName("zero_zone_consecutive_skips_count" ) val consecutiveSkips: Int? = null,
    @SerializedName("answer_id"                         ) val knowledgeAnswerId: Int? = null,
    ): AbstractUser() {

    override val membership: UserType
        get() = if (isPremium) UserType.PREMIUM else UserType.FREE

    override val verified: Boolean
        get() = false

    val userId: Int
        get() = id

    val color: String
        get() = _color.orEmpty()

    val winner: Boolean
        get() = (_winner?:0)>0

    val coinsWonFormatted: String?
        get() = coinsWon?.toInt()?.let { DataFormatHelper.numberToK(it) }

    private fun emptyScoreBoard(): List<PenaltyTurnStatus> = List(5) { PenaltyTurnStatus.NONE }

    enum class PlayerColor {
        @SerializedName("purple") PURPLE,
        @SerializedName("yellow") YELLOW;

        val colorRes: Int
            @ColorRes
            get() = when(this) {
                PURPLE -> R.color.colorPrimary
                YELLOW -> R.color.colorSecondary
            }

        val swapped: PlayerColor
            get() = when(this) {
                PURPLE -> YELLOW
                YELLOW -> PURPLE
            }
    }

    var penaltyScoreBoard: PenaltyScoreBoard
        get() = _scoreBoard?.map {
            it.map { turnStatus ->
                PenaltyTurnStatus.entries.find { turnStatus == it.key }?: PenaltyTurnStatus.NONE
            }
        }?: List(2) { emptyScoreBoard() }
        private set(value) {
            _scoreBoard = value.map {
                it.map { turnStatus ->
                    turnStatus.key
                }
            }
        }

    var roundOneScores: List<PenaltyTurnStatus>
        get() = penaltyScoreBoard.getOrElse(0) { emptyScoreBoard() }
        private set(value) {
            penaltyScoreBoard = penaltyScoreBoard.mapIndexed { index, penaltyTurnStatuses ->
                if (index==0) value else penaltyTurnStatuses
            }
        }

    var roundTwoScores: List<PenaltyTurnStatus>
        get() = penaltyScoreBoard.getOrElse(1) { emptyScoreBoard() }
        private set(value) {
            penaltyScoreBoard = penaltyScoreBoard.mapIndexed { index, penaltyTurnStatuses ->
                if (index==1) value else penaltyTurnStatuses
            }
        }

    fun scoresForRound(round: PenaltyData.PenaltyRound): List<PenaltyTurnStatus> = when(round) {
        PenaltyData.PenaltyRound.ROUND_ONE -> roundOneScores
        PenaltyData.PenaltyRound.ROUND_TWO -> roundTwoScores
    }

    fun setScoresForRound(round: PenaltyData.PenaltyRound, scores: List<PenaltyTurnStatus>) = when(round) {
        PenaltyData.PenaltyRound.ROUND_ONE -> roundOneScores = scores
        PenaltyData.PenaltyRound.ROUND_TWO -> roundTwoScores = scores
    }

    fun winsForRound(round: PenaltyData.PenaltyRound): Int {
        val scores = when(round) {
            PenaltyData.PenaltyRound.ROUND_ONE -> roundOneScores
            PenaltyData.PenaltyRound.ROUND_TWO -> roundTwoScores
        }
        return scores.count { it == PenaltyTurnStatus.WON }
    }

    fun updateScoreBoard(turn: PenaltyData.ActiveTurn, result: PenaltyKickResult) {
        scoresForRound(turn.round).toMutableList().let { sc ->
            sc[turn.turn-1] = when(result) {
                PenaltyKickResult.GOAL -> if (playerRole==PenaltyPlayerRole.KICKER) PenaltyTurnStatus.WON else PenaltyTurnStatus.LOST
                PenaltyKickResult.SAVE -> if (playerRole==PenaltyPlayerRole.KICKER) PenaltyTurnStatus.LOST else PenaltyTurnStatus.WON
                PenaltyKickResult.VOID -> PenaltyTurnStatus.VOID
            }
            setScoresForRound(turn.round,sc)
        }
    }

    val scoreInt: Int
        get() = score.roundToInt()

    val formattedScore: String
        get() = scoreInt.toString()

    enum class PenaltyTurnStatus(val key: Int) {
        NONE(0), WON(1), LOST(2), VOID(3)
    }

    enum class PenaltyKickResult {
        @SerializedName("goal") GOAL,
        @SerializedName("save") SAVE,
        @SerializedName("void") VOID
    }

    val player: ChallengePlayer?
        get() = when (participantTokenNumber) {
            1 -> ChallengePlayer.PLAYER1
            2 -> ChallengePlayer.PLAYER2
            else -> null
        }

    val playerColor: PlayerColor
        get() = when(color) {
            "purple" -> PlayerColor.PURPLE
            "yellow" -> PlayerColor.YELLOW
            else -> PlayerColor.PURPLE
        }

    enum class PenaltyPlayerRole {
        @SerializedName("kicker") KICKER,
        @SerializedName("keeper") GOALIE;

        val opposite: PenaltyPlayerRole
            get() = when(this) {
                KICKER -> GOALIE
                GOALIE -> KICKER
            }
    }

    enum class PenaltyPlayerStatus {
        @SerializedName("waiting") WAITING,
        @SerializedName("ready") READY,
        @SerializedName("playing") PLAYING
    }
}
