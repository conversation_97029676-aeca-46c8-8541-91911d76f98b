package com.app.messej.data.utils

import android.os.Build
import com.app.messej.data.api.external.ExternalServiceGenerator
import com.app.messej.data.model.DeviceInfo
import com.app.messej.data.repository.FirebaseRepository

object DeviceInfoUtil {

    private val IPAPI_FALLBACK_VALUE = "IPAPI Failed"

    suspend fun getAllDeviceInfo(firebaseRepo: FirebaseRepository): DeviceInfo {
        val deviceInfo = DeviceInfo()
        deviceInfo.deviceName = Build.MANUFACTURER
        deviceInfo.deviceModel = Build.MODEL
        deviceInfo.deviceUID = firebaseRepo.getFirebaseInstallationID()

        val response = ExternalServiceGenerator.createIPApiService().getIpApiData()
        if (response.isSuccessful) {
            val ipApiResponse = response.body()!!

            deviceInfo.userIP = ipApiResponse.ip
            deviceInfo.city = ipApiResponse.city
            deviceInfo.country = ipApiResponse.country
            deviceInfo.region = ipApiResponse.region
        } else {
            deviceInfo.userIP = IPAPI_FALLBACK_VALUE
            deviceInfo.city = IPAPI_FALLBACK_VALUE
            deviceInfo.country = IPAPI_FALLBACK_VALUE
            deviceInfo.region = IPAPI_FALLBACK_VALUE
//            throw Exception("Error getting IPAPI info")
        }
        return deviceInfo
    }
}