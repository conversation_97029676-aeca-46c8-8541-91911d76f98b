package com.app.messej.data.model.api

import com.google.gson.annotations.SerializedName

data class PlacesAutoCompleteResponse(
    @SerializedName("predictions" ) var predictions : ArrayList<Predictions> = arrayListOf(),
    @SerializedName("status"      ) var status      : String?                = null
){
    data class Predictions (

        @SerializedName("description"           ) var description          : String?               = null,
        @SerializedName("place_id"              ) var placeId              : String?               = null,
        @SerializedName("reference"             ) var reference            : String?               = null,
        @SerializedName("structured_formatting" ) var structuredFormatting : StructuredFormatting? = StructuredFormatting()

    ){
        data class StructuredFormatting (

            @SerializedName("main_text"      ) var mainText      : String? = null,
            @SerializedName("secondary_text" ) var secondaryText : String? = null

        )
    }
}
