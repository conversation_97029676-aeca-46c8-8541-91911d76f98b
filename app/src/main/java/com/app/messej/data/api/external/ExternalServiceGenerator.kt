package com.app.messej.data.api.external

import com.app.messej.BuildConfig
import com.app.messej.data.Constants
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

object ExternalServiceGenerator {

    private fun getBaseHttpClientBuilder(addLogger: Boolean = true): OkHttpClient.Builder {
        val httpClient = OkHttpClient.Builder()
        if(BuildConfig.BUILD_TYPE != Constants.BuildType.RELEASE.value && addLogger) {
            val logging = HttpLoggingInterceptor()
            logging.setLevel(HttpLoggingInterceptor.Level.BODY)
            httpClient.addInterceptor(logging)
        }
        return httpClient
    }

    private fun getRetrofitInstance(url: String): Retrofit {
        return Retrofit.Builder()
            .baseUrl(url)
            .addConverterFactory(GsonConverterFactory.create())
            .client(getBaseHttpClientBuilder().build())
            .build()
    }

    private fun <S> createService(serviceClass: Class<S>, baseUrl: String): S {
        val retrofit = getRetrofitInstance(baseUrl)
        return retrofit.create(serviceClass)
    }

    fun createAwsS3Service() = createService(AWSS3Service::class.java,BuildConfig.BASE_URL)

    fun createGoogleAPIService() = createService(GoogleAPIService::class.java,"https://www.googleapis.com")

    fun createIPApiService() = createService(IPApiService::class.java,"https://ipapi.co")

    fun createGoogleMapsAPIService() = createService(GoogleMapsAPIService::class.java,"https://maps.googleapis.com")
}