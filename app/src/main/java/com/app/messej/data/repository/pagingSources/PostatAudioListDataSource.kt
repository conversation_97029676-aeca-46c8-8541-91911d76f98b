package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.PostatAPIService
import com.app.messej.data.model.api.postat.MusicFile
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PostatAudioListDataSource(private val api: PostatAPIService, val searchTerm: String? = null): PagingSource<Int, MusicFile>() {
    companion object {
        private const val STARTING_KEY = 1
    }
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, MusicFile> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage = params.key ?: STARTING_KEY
                val response = api.getPostatAudios(searchKeyword = if(searchTerm.isNullOrBlank()) null else searchTerm, page = currentPage)
                val data = response.body()?.result?:return@withContext LoadResult.Error(Exception("No data received from server"))
                val nextKey = if (!data.hasNextPage) null else currentPage.inc()

                LoadResult.Page(
                    data = data.musicFiles, prevKey = null, nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("PALDS", "load: error",e )
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, MusicFile>): Int? {
        return null
    }
}