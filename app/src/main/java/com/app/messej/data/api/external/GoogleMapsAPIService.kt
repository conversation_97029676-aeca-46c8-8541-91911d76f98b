package com.app.messej.data.api.external

import com.app.messej.BuildConfig
import com.app.messej.data.model.api.NearbyPlacesCompleteResponse
import com.app.messej.data.model.api.PlacesAutoCompleteResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Query

interface GoogleMapsAPIService {

    @GET("/maps/api/place/autocomplete/json")
    @Headers("Accept: application/json")
    suspend fun getPlacesAutoComplete(
        @Query("key") key: String = BuildConfig.GOOGLE_MAPS_API_KEY,
        @Query("input") input: String
    ) : Response<PlacesAutoCompleteResponse>

    @GET("/maps/api/place/nearbysearch/json")
    @Headers("Accept: application/json")
    suspend fun getNearbyPlaces(
        @Query("key") key: String = BuildConfig.GOOGLE_MAPS_API_KEY,
        @Query("location") location: String,
        @Query("radius") radius: String = "5000",
    ) : Response<NearbyPlacesCompleteResponse>
}