package com.app.messej.data.api.external

import com.app.messej.BuildConfig
import com.app.messej.data.model.api.GeoLocationResponse
import retrofit2.Response
import retrofit2.http.Headers
import retrofit2.http.POST

interface GoogleAPIService {
    @POST("/geolocation/v1/geolocate/?key=${BuildConfig.GOOGLE_MAPS_API_KEY}")
    @Headers("Accept: application/json")
    suspend fun getGeoLocation() :Response<GeoLocationResponse>
}