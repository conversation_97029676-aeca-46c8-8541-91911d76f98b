package com.app.messej.data.utils;

import android.net.Uri
import android.util.Log
import androidx.annotation.VisibleForTesting
import androidx.concurrent.futures.await
import androidx.media3.common.C.TRACK_TYPE_AUDIO
import androidx.media3.common.C.TRACK_TYPE_VIDEO
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.effect.BitmapOverlay
import androidx.media3.effect.FrameDropEffect
import androidx.media3.effect.OverlayEffect
import androidx.media3.effect.Presentation
import androidx.media3.exoplayer.MetadataRetriever
import androidx.media3.exoplayer.source.TrackGroupArray
import androidx.media3.transformer.Composition
import androidx.media3.transformer.DefaultEncoderFactory
import androidx.media3.transformer.EditedMediaItem
import androidx.media3.transformer.Effects
import androidx.media3.transformer.ExportException
import androidx.media3.transformer.ExportResult
import androidx.media3.transformer.ProgressHolder
import androidx.media3.transformer.Transformer
import androidx.media3.transformer.VideoEncoderSettings
import com.app.messej.MainApplication
import com.app.messej.data.model.EditableFlashMedia
import com.app.messej.data.model.ReportProofMedia
import com.app.messej.data.model.SocialProofMedia
import com.app.messej.data.model.TempMedia
import com.app.messej.data.model.VideoEditInfo
import com.app.messej.data.model.api.postat.PostatDeviceMedia
import com.app.messej.data.model.enums.MediaType
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.File
import java.time.ZonedDateTime
import kotlin.coroutines.resumeWithException

@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
object VideoEncoderUtil {

    interface MediaProcessingListener {
        fun onProgress(progress: Int)
        fun onProcessingFinished(success: Boolean)
    }

    private fun Number.mbps(): Int {
        return this.kbps()*1000
    }

    private fun Number.kbps(): Int {
        return (this.toDouble()*1000).toInt()
    }

    @VisibleForTesting
    fun getMbps(numb: Number): Int = numb.mbps()

//    suspend fun encodeVideoFFMpeg(uri: Uri, callBack: MediaProcessingListener): File = withContext(Dispatchers.IO) {
//        val c = MainApplication.applicationContext()
//        val inputVideoPath = FFmpegKitConfig.getSafParameterForRead(c, uri)
//
//        val totalDuration = MediaUtils.getDuration(uri,true)
//        return@withContext encodeVideoFFMpeg(inputVideoPath, totalDuration, callBack)
//    }
//
//    suspend fun encodeVideoFFMpeg(file: File, callBack: MediaProcessingListener): File = withContext(Dispatchers.IO) {
//        val totalDuration = MediaUtils.getDuration(file)
//        return@withContext encodeVideoFFMpeg(file.absolutePath,totalDuration,callBack)
//    }
//
//    private suspend fun encodeVideoFFMpeg(input: String, totalDuration: Long, callBack: MediaProcessingListener): File = withContext(Dispatchers.IO) {
//        Log.d("ENCODE", "Encoding video")
//        val c = MainApplication.applicationContext()
////        c.contentResolver.openAssetFileDescriptor(uri, "r")?.apply {
////            Log.d("ENCODE", "Input Size: ${MediaUtils.humanizeBytes(length)}")
////            close()
////        }
//
//        val outputFile = MediaUtils.createTempFile(c, MediaType.VIDEO)
//        Log.d("ENCODE", "Converting: $input to ${outputFile.absolutePath}")
//
//        val command = FFMPEGBuilder(
//            input = input,
//            output = outputFile.absolutePath,
//            videoCodec = FFMPEGBuilder.CODEC_VIDEO_H264,
//            videoBitrate = FFMPEGBuilder.BITRATE_VIDEO_1500K,
//            videoFrameRate = FFMPEGBuilder.FRAME_RATE_30,
//            videoResolution = FFMPEGBuilder.RESOLUTION_720,
//            audioCodec = FFMPEGBuilder.CODEC_AUDIO_AAC,
//            audioBitrate = FFMPEGBuilder.BITRATE_AUDIO_128K,
//            preset = FFMPEGBuilder.Preset.FAST,
//            verbosity = FFMPEGBuilder.Verbosity.WARNING
//        ).build()
//
//        Log.d("ENCODE", "Using command: $command")
//
//        val time = ZonedDateTime.now()
//
//        try {
//            suspendCancellableCoroutine<Unit>{ cont ->
//                val session = FFmpegKit.executeAsync(command, { session ->
//                    val state = session.state
//                    val returnCode = session.returnCode
//
//                    // CALLED WHEN SESSION IS EXECUTED
//                    Log.d("ENCODE", "FFmpeg process exited with state $state and rc $returnCode ${session.failStackTrace}")
//                    if (returnCode.isValueSuccess) {
//                        Log.w("ENCODE", "Output Size: ${MediaUtils.getFileSize(outputFile)}")
//                        callBack.onProcessingFinished(true)
//                        cont.resumeWith(Result.success(Unit))
//                    } else {
//                        callBack.onProcessingFinished(false)
//                        cont.resumeWithException(Exception(session.failStackTrace))
//                    }
//
//                }, { }, { stats ->
////                                                         Log.d("ENCODE", "FFMpeg Stats: $stats")
//                                                         val curTime = stats.time.toLong()
//                                                         if (curTime > 0) {
//                                                             val progress = (curTime * 100) / totalDuration
//                                                             callBack.onProgress(progress.toInt())
//                                                         }
//                                                         // CALLED WHEN SESSION GENERATES STATISTICS
//                                                     })
//                cont.invokeOnCancellation {
//                    Log.w("ENCODE", "Cancelling Session")
//                    session.cancel()
//                }
//            }
//        } catch (e: Exception) {
//            MediaUtils.deleteFile(outputFile)
//            throw e
//        }
//        Log.w("ENCODE", "Encoding completed in ${DateTimeUtils.durationToNow(time)?.let { DateTimeUtils.formatDuration(it) }}")
//        return@withContext outputFile
//    }
//
//    fun probeVideo(uri: Uri) {
//        val c = MainApplication.applicationContext()
//        val inputVideoPath = FFmpegKitConfig.getSafParameterForRead(c, uri)
//        probeVideo(inputVideoPath)
//    }
//
//    fun probeVideo(path: String) {
//        val session = FFprobeKit.execute(path)
//        if (!ReturnCode.isSuccess(session.returnCode)) {
//            Log.d("ENCODE", "Probe failed: ${session.failStackTrace}")
//        }
//        Log.d("ENCODE", "Probe result: ${session}")
//    }

    private fun getVideoFrameRate(metadata: TrackGroupArray): Float {
        var frameRate = 0f
        for(i in 0 until metadata.length) {
            val tg = metadata.get(i)
            Log.w("ENCODE", "tg $i: type: ${tg.type}, format: ${tg.getFormat(0)}")
            if (tg.type == TRACK_TYPE_VIDEO) {
                frameRate = tg.getFormat(0).frameRate
            }
        }
        Log.w("ENCODE", "getVideoFrameRate: frameRate: $frameRate")
        return frameRate
    }

    private fun getSourceBitrate(metadata: TrackGroupArray, duration: Long, size: Long): Int {
        if(size<=0) return -1
        Log.w("ENCODE", "getSourceBitrate: fileSize: $size")
        Log.w("ENCODE", "getSourceBitrate: duration: $duration")
        val bits: Long = size*8
        var audioBitrate: Int? = null
        var videoBitrate: Int? = null
        for(i in 0 until metadata.length) {
            val tg = metadata.get(i)
            Log.w("ENCODE", "tg $i: type: ${tg.type}, format: ${tg.getFormat(0)}")
            if (tg.type == TRACK_TYPE_VIDEO) {
                videoBitrate = tg.getFormat(0).bitrate
            } else if(tg.type == TRACK_TYPE_AUDIO) {
                audioBitrate = tg.getFormat(0).bitrate
            }
        }
        Log.w("ENCODE", "getSourceBitrate: videoBitrate: $videoBitrate")
        Log.w("ENCODE", "getSourceBitrate: audioBitrate: $audioBitrate")
        return if (videoBitrate!= null && videoBitrate>0) videoBitrate
        else if(audioBitrate!=null && audioBitrate>0) {
            val adjustedSize = bits-(audioBitrate*duration)
            Log.w("ENCODE", "getSourceBitrate: adjustedSize: $adjustedSize")
            (adjustedSize/duration).toInt()
        } else (bits/duration).toInt()
    }

    private fun getScaledBitrate(baseRes: MediaResolution,targetRes: MediaResolution, baseBitrate: Int): Int {
        val basePixels = baseRes.width * baseRes.height
        val targetPixels = targetRes.width * targetRes.height
        Log.w("ENCODE", "getScaledBitrate: res factor: ${targetPixels.toFloat()/basePixels}")
        val factor = ((targetPixels.toFloat()/basePixels) * 1.4).coerceAtMost(1.0)
        Log.w("ENCODE", "getScaledBitrate: res factor adjusted: $factor")
        return (baseBitrate * factor).toInt()
    }

    suspend fun encodeVideoMedia3(med: EditableFlashMedia, callBack: MediaProcessingListener): File {
        val uri = med.sourceUri?:Uri.fromFile(med.file)
        val mediaItem = med.mediaItem
        return encodeVideoMedia3(uri, mediaItem, med.edits, callBack, if (med.isFromGallery) MediaResolution(720,1280) else null)
    }

    suspend fun encodeVideoMedia3(med: PostatDeviceMedia, callBack: MediaProcessingListener, useOriginalAudio: Boolean = true): File {
        val uri = med.uri
        val mediaItem = med.mediaItem
        return encodeVideoMedia3(uri, mediaItem, edits = if(useOriginalAudio) null else VideoEditInfo(mute = true), callBack)
    }

    suspend fun encodeVideoMedia3(med: ReportProofMedia, callBack: MediaProcessingListener): File {
        val uri = med.uri
        val mediaItem = med.mediaItem
        return encodeVideoMedia3(uri, mediaItem, null, callBack)
    }

    suspend fun encodeVideoMedia3(med: SocialProofMedia, callBack: MediaProcessingListener): File {
        val uri = med.uri
        val mediaItem = med.mediaItem
        return encodeVideoMedia3(uri, mediaItem, null, callBack)
    }

    suspend fun encodeVideoMedia3(med: TempMedia, callBack: MediaProcessingListener): File {
        val uri = med.sourceUri?:Uri.fromFile(med.file)
        val mediaItem = med.mediaItem
        return encodeVideoMedia3(uri, mediaItem, med.videoEditInfo, callBack)
    }

    private suspend fun encodeVideoMedia3(uri: Uri, mediaItem: MediaItem, edits: VideoEditInfo?, callBack: MediaProcessingListener, forceResolution: MediaResolution? = null): File = withContext(Dispatchers.IO) {
        Log.d("ENCODE", "Encoding video")
        val c = MainApplication.applicationContext()
        val fileDesc = c.contentResolver.openAssetFileDescriptor(uri, "r")
        Log.d("ENCODE", "Input Size: ${MediaUtils.humanizeBytes(fileDesc?.length?:0)}")
        val fileSize = fileDesc?.length?:0
        fileDesc?.close()

        val metadataResult = MetadataRetriever.retrieveMetadata(c, mediaItem).await()
        val duration = if(edits?.trim!=null) edits.trim.trimDurationMs/1000 else MediaUtils.getDuration(uri)
        Log.w("ENCODE", "encodeVideoMedia3: duration: $duration")

        val srcBitrate = getSourceBitrate(metadataResult, duration, fileSize)
        Log.w("ENCODE", "encodeVideoMedia3: source bitrate: $srcBitrate")

        val frameRate = getVideoFrameRate(metadataResult)

        val maxResolution = forceResolution?:MediaResolution(1280,720)
        val resolution = MediaUtils.getVideoResolution(uri).run {
            if (forceResolution != null) fillInsideAndCrop(maxResolution)
            else fitInside(maxResolution.longEdge)
        }
        Log.w("ENCODE", "encodeVideoMedia3: resulution: $resolution")

        val maxBitrate = getScaledBitrate(maxResolution,resolution,1800.kbps())
        Log.w("ENCODE", "encodeVideoMedia3: max bitrate: $maxBitrate")

        val overlayEffects = mutableListOf<OverlayEffect>()

        edits?.overlay?.bitmap?.let {
            val bitmapOverlay = BitmapOverlay.createStaticBitmapOverlay(it)
            overlayEffects.add(OverlayEffect(listOf(bitmapOverlay)))
        }

        val editableMedia = EditedMediaItem.Builder(mediaItem)
            .setRemoveAudio(edits?.mute==true)
            .setEffects(
                Effects(
                /* audioProcessors= */ listOf(),
                /* videoEffects= */ listOf(*overlayEffects.toTypedArray(),
                        Presentation.createForWidthAndHeight(resolution.width,resolution.height,Presentation.LAYOUT_SCALE_TO_FIT_WITH_CROP),
                        FrameDropEffect.createDefaultFrameDropEffect(30f)
                )
            )
            )
            .build()

        val outputFile = MediaUtils.createTempFile(c, MediaType.VIDEO)

        val time = ZonedDateTime.now()

        try {
            suspendCancellableCoroutine<Unit> { cont ->

                // Create a Transformer
                val builder =  Transformer.Builder(c)
                    .setVideoMimeType(MimeTypes.VIDEO_H264)
                    .setAudioMimeType(MimeTypes.AUDIO_AAC)
                    .addListener(object : Transformer.Listener {
                        override fun onCompleted(composition: Composition, exportResult: ExportResult) {
                            super.onCompleted(composition, exportResult)
                            Log.d("ENCODE", "Encoding Complete")
                            Firebase.crashlytics.log("Encoding Complete")
                            Log.d("ENCODE", "Output Size: ${MediaUtils.getFileSize(outputFile)}")
                            callBack.onProcessingFinished(true)
                            cont.resumeWith(Result.success(Unit))
                        }

                        override fun onError(composition: Composition, exportResult: ExportResult, exportException: ExportException) {
                            super.onError(composition, exportResult, exportException)
                            callBack.onProcessingFinished(false)
                            if (isActive) {
                                cont.resumeWithException(exportException)
                            }
                        }
                    }) // transformerListener is an implementation of Transformer.Listener

                Log.w("ENCODE", "encodeVideoMedia3: src bitrate: $srcBitrate | max bitrate: $maxBitrate")
                if (srcBitrate>maxBitrate) {
                    Log.w("ENCODE", "encodeVideoMedia3: src bitrate: $srcBitrate | max bitrate: $maxBitrate")
                    val settings = VideoEncoderSettings.Builder()
                        .setBitrate(maxBitrate)
                        .build()
                    val factory = DefaultEncoderFactory.Builder(c)
                        .setRequestedVideoEncoderSettings(settings)
                        .build()
                    builder.setEncoderFactory(factory)
                }
                val transformer = builder.build()

                // Start the transformation
                val job = launch(Dispatchers.Main) {
                    transformer.start(editableMedia, outputFile.absolutePath)
                    try {
                        val progressHolder = ProgressHolder()
                        while (isActive && transformer.getProgress(progressHolder) != Transformer.PROGRESS_STATE_NOT_STARTED) {
                            Log.d("ENCODE", "Encoding progress: ${progressHolder.progress}")
                            callBack.onProgress(progressHolder.progress)
                            delay(1000)
                        }
                    } finally {
                        Log.w("ENCODE", "Stopping progress Listener")
                    }
                }
                cont.invokeOnCancellation {
                    Log.w("ENCODE", "Cancelling Session")
                    launch(Dispatchers.Main) {
                        transformer.cancel()
                        job.cancel()
                    }
                }
            }
        } catch (e: Exception) {
            Firebase.crashlytics.log("encode resumed with exception: ${e.message}")
            Firebase.crashlytics.recordException(e)
            Log.e("ENCODE", "encode resumed with exception: ${e.message}")
            MediaUtils.deleteFile(outputFile)
            throw e
        }
        Log.w("ENCODE", "Encoding completed in ${DateTimeUtils.durationToNowFromPast(time)?.let { DateTimeUtils.formatDuration(it) }}")
        Firebase.crashlytics.log("Encoding completed in ${DateTimeUtils.durationToNowFromPast(time)?.let { DateTimeUtils.formatDuration(it) }}")
        return@withContext outputFile
    }
}
