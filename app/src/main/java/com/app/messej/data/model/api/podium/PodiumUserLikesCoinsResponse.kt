package com.app.messej.data.model.api.podium

import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.ui.utils.DataFormatHelper.numberToKWithFractions
import com.google.gson.annotations.SerializedName

data class PodiumUserLikesCoinsResponse(
    @SerializedName("total_likes" ) val totalLikes: Int,
    @SerializedName("coins_received" ) val coinsReceived: Double,
    @SerializedName("citizenship" ) val userCitizenship: UserCitizenship?=null,
    @SerializedName("is_followed" ) val isFollowed: <PERSON><PERSON><PERSON>,
    @SerializedName("is_superstar") val isSuperstar: Boolean,
    @SerializedName("relation")     val relation: FollowerType?,
    @SerializedName("flix_rate" )   val flixRate: Double
) {
    val likesFormatted: String
        get() = totalLikes.numberToKWithFractions()

    val coinsFormatted: String
        get() = coinsReceived.toInt().numberToKWithFractions()
}
