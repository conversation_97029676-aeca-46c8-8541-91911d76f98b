package com.app.messej.data.api

import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.EmpoweredBlockedUser
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.UnHideRequest
import com.app.messej.data.model.api.UpdateUserNameRequest
import com.app.messej.data.model.api.UserNickNameRequest
import com.app.messej.data.model.api.UserNickNameResponse
import com.app.messej.data.model.api.auth.HomeAddress
import com.app.messej.data.model.api.auth.VerifyEmailRequest
import com.app.messej.data.model.api.auth.VerifyOTPRequest
import com.app.messej.data.model.api.auth.VerifyOTPResponse
import com.app.messej.data.model.api.auth.VerifyPhoneRequest
import com.app.messej.data.model.api.auth.VerifyResponse
import com.app.messej.data.model.api.huddles.BlockedBroadcastResponse
import com.app.messej.data.model.api.huddles.BlockedPrivateMessageResponse
import com.app.messej.data.model.api.huddles.HuddleMentionableUsersResponse
import com.app.messej.data.model.api.huddles.HuddleParticipantsResponse
import com.app.messej.data.model.api.huddles.UserBlockRequest
import com.app.messej.data.model.api.huddles.UserRelativeListResponse
import com.app.messej.data.model.api.huddles.UserRelativeSearchListResponse
import com.app.messej.data.model.api.huddles.UserUnblockRequest
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.profile.LikersHideRequest
import com.app.messej.data.model.api.profile.NotificationsResponse
import com.app.messej.data.model.api.profile.PasswordChangeRequest
import com.app.messej.data.model.api.profile.RegisterUsernameRequest
import com.app.messej.data.model.api.profile.TempUserBlockRequest
import com.app.messej.data.model.api.profile.UpdateUserLocation
import com.app.messej.data.model.api.profile.VerifyAccountResponse
import com.app.messej.data.model.api.stars.FollowStarRequest
import com.app.messej.data.model.api.stars.StarSearchSuggestionResponse
import com.app.messej.data.model.api.stars.StarSuggestionResponse
import com.app.messej.data.model.api.stars.StarsListResponse
import com.app.messej.data.model.api.stars.StarsSearchListResponse
import com.app.messej.data.model.api.stars.UnfollowStarRequest
import com.app.messej.data.model.api.subscription.PremiumResponse
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.SubscriptionPurchaseRequest
import com.app.messej.data.model.entity.UserStatsResult
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.model.enums.UserType
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query

interface ProfileAPIService {

    @GET("/user/account")
    @Headers("Accept: application/json")
    suspend fun getAccountDetails(): Response<APIResponse<AccountDetailsResponse>>

    @GET("/user/account/verify")
    @Headers("Accept: application/json")
    suspend fun getVerifyAccountDetails(): Response<APIResponse<VerifyAccountResponse>>

    @PUT("/user/account")
    @Headers("Accept: application/json")
    suspend fun verifyEmail(@Body request: VerifyEmailRequest): Response<APIResponse<VerifyResponse>>

    @PUT("/user/account")
    @Headers("Accept: application/json")
    suspend fun verifyPhone(@Body request: VerifyPhoneRequest): Response<APIResponse<VerifyResponse>>

    @GET("/user/account/stars")
    @Headers("Accept: application/json")
    suspend fun getStarsList(
        @Query("next_page") page: String?
    ): Response<APIResponse<StarsListResponse>>

    @GET("/user/account/stars")
    @Headers("Accept: application/json")
    suspend fun getSearchStarsList(
        @Query("next_page") page: Int?,
        @Query("keyword") keyword: String
    ): Response<APIResponse<StarsSearchListResponse>>

    @GET("/user/account/fans")
    @Headers("Accept: application/json")
    suspend fun getFansList(
        @Query("next_page") page: Int
    ): Response<APIResponse<UserRelativeListResponse>>

    @GET("/user/account/likers")
    @Headers("Accept: application/json")
    suspend fun getLikersList(
        @Query("next_page") page: Int
    ): Response<APIResponse<UserRelativeListResponse>>

    @GET("/user/account/dears")
    @Headers("Accept: application/json")
    suspend fun getDearsList(
        @Query("next_page") page: Int
    ): Response<APIResponse<UserRelativeListResponse>>

    @GET("/user/account/fans")
    @Headers("Accept: application/json")
    suspend fun getFansSearchList(
        @Query("next_page") page: Int,
        @Query("keyword") keyword: String?
    ): Response<APIResponse<UserRelativeSearchListResponse>>

    @GET("/user/account/likers")
    @Headers("Accept: application/json")
    suspend fun getLikersSearchList(
        @Query("next_page") page: Int,
        @Query("keyword") keyword: String?
    ): Response<APIResponse<UserRelativeSearchListResponse>>

    @GET("/user/account/dears")
    @Headers("Accept: application/json")
    suspend fun getDearsSearchList(
        @Query("next_page") page: Int,
        @Query("keyword") keyword: String?
    ): Response<APIResponse<UserRelativeSearchListResponse>>

    @GET("/user/stars/suggestions")
    @Headers("Accept: application/json")
    suspend fun getStarsSuggestion(
        @Query("user_type") userType: UserType,
        @Query("page") page: Int,
        @Query("country") country: String = "IN",
        @Query("country_offset") countryOffset: Int = 0,
        @Query("no_country_offset") noCountryOffset: Int = 0
    ): Response<APIResponse<StarSuggestionResponse>>

    @GET("/user/stars")
    @Headers("Accept: application/json")
    suspend fun getStarsSearchSuggestion(
        @Query("user_type") userType: UserType,
        @Query("page") page: Int,
        @Query("keyword") keyword: String,
        @Query("search_type") searchType: SearchType,
        @Query("free_offset") freeOffset: Int = 0,
        @Query("premium_offset") premiumOffset: Int = 0
    ): Response<APIResponse<StarSearchSuggestionResponse>>

    @POST("/user/stars/follow")
    @Headers("Accept: application/json")
    suspend fun followStar(@Body request: FollowStarRequest): Response<APIResponse<Unit>>

    @POST("/user/stars/unfollow")
    @Headers("Accept: application/json")
    suspend fun unfollowStar(@Body request: UnfollowStarRequest): Response<APIResponse<Unit>>

    @GET("/user/users/{id}")
    @Headers("Accept: application/json")
    suspend fun getPublicUserDetails(
        @Path("id") id: Int,
        @Query("star_type") starType: FollowerType? = null,
    ): Response<APIResponse<OtherUser>>

    @GET("/user/account/nickname")
    @Headers("Accept: application/json")
    suspend fun getUserNickName(): Response<APIResponse<UserNickNameResponse>>

    @PUT("/user/account/nickname")
    @Headers("Accept: application/json")
    suspend fun updateUserNickName(@Body request: UserNickNameRequest): Response<APIResponse<Unit>>

    @POST("/user/users/hide")
    @Headers("Accept: application/json")
    suspend fun hideLiker(@Body request: LikersHideRequest): Response<APIResponse<Unit>>

    @PUT("/user/account")
    suspend fun editAccountDetails(@Body request: UpdateUserNameRequest): Response<APIResponse<String>>

    @POST("/user/username")
    suspend fun checkSetUsername(@Body request: RegisterUsernameRequest): Response<APIResponse<Unit>>

    @GET("/user/username")
    @Headers("Accept: application/json")
    suspend fun checkAvailabilityUsername(@Query("username") username: String): Response<APIResponse<String>>

    @PUT("/user/password")
    @Headers("Accept: application/json")
    suspend fun changePassword(@Body request: PasswordChangeRequest): Response<APIResponse<CurrentUser>>

    @POST("/user/account/verify")
    @Headers("Accept: application/json")
    suspend fun verifyAccount(): Response<APIResponse<Unit>>

    @POST("/user/account/verify/phone")
    @Headers("Accept: application/json")
    suspend fun verifyMobileOTP(@Body request: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>

    @POST("/user/account/verify/email")
    @Headers("Accept: application/json")
    suspend fun verifyEmailOTP(@Body request: VerifyOTPRequest): Response<APIResponse<VerifyOTPResponse>>

    @GET("/user/users/block")
    @Headers("Accept: application/json")
    suspend fun getBlockedUsers(@Query("page") page: Int, @Query("limit") limit: Int = 20): Response<APIResponse<BlockedPrivateMessageResponse>>

    @POST("/user/users/block")
    @Headers("Accept: application/json")
    suspend fun blockUser(@Body req: UserBlockRequest): Response<APIResponse<Unit>>

    @POST("/user/users/unblock")
    @Headers("Accept: application/json")
    suspend fun unblockUser(@Body req: UserUnblockRequest): Response<APIResponse<String>>

    @GET("/user/users/hide")
    @Headers("Accept: application/json")
    suspend fun getBlockedBroadCastList(@Query("page") page: Int, @Query("limit") limit: Int = 20): Response<APIResponse<BlockedBroadcastResponse>>

    @POST("/user/users/hide")
    @Headers("Accept: application/json")
    suspend  fun unBlockBroadcast(@Body req: UnHideRequest): Response<APIResponse<String>>
    @GET("/user/notifications/settings")
    @Headers("Accept: application/json")
    suspend fun getNotificationsSettings(): Response<APIResponse<NotificationsResponse>>

    @PUT("/user/notifications/settings")
    @Headers("Accept: application/json")
    suspend fun updateNotifications(@Body request: NotificationsResponse): Response<APIResponse<NotificationsResponse>>

    @GET("/huddles/{id}/members")
    @Headers("Accept: application/json")
    suspend fun getHuddleParticipantsList(
        @Path("id") id: Int,
        @Query("page") page: Int,
        @Query("keyword") keyword: String = "",
        @Query("search_type") searchType: String = "",
        @Query("tribe_removed") removed: Boolean?=null,
    ): Response<APIResponse<HuddleParticipantsResponse>>

    @GET("/huddles/{id}/premiumusers")
    @Headers("Accept: application/json")
    suspend fun getHuddleMentionableUsers(
        @Path("id") id: Int,
    ): Response<APIResponse<HuddleMentionableUsersResponse>>

    @POST("/user/homeapp/homeaddress")
    @Headers("Accept: application/json")
    suspend fun addHomeAddress(@Body request: HomeAddress): Response<APIResponse<Unit>>

    @GET("/user/homeapp/homeaddress")
    @Headers("Accept: application/json")
    suspend fun getHomeAddress(): Response<APIResponse<HomeAddress>>

    @PUT("/user/homeapp/homeaddress")
    @Headers("Accept: application/json")
    suspend fun editHomeAddress(@Body request: HomeAddress): Response<APIResponse<Unit>>

    @POST("/user/leaderblock/{id}")
    @Headers("Accept: application/json")
    suspend fun blockUserTemporarily(@Path("id") userId: Int, @Body request: TempUserBlockRequest): Response<APIResponse<Unit>>

    @POST("/subscription/buyflax")
    @Headers("Accept: application/json")
    suspend fun updateInAppPurchase(@Body request: SubscriptionPurchaseRequest): Response<APIResponse<PremiumResponse>>

    @POST("/user/country_update")
    @Headers("Accept: application/json")
    suspend fun updateCountry(@Body request:UpdateUserLocation):Response<APIResponse<Unit>>

    @GET("/user/empowerments")
    @Headers("Accept: application/json")
    suspend fun getAllBlockedUsersList(@Query("functionality") functionality:String, @Query("page") page: Int, @Query("limit") limit: Int = 20):Response<APIResponse<EmpoweredBlockedUser>>

    @GET("/user/popularity")
    @Headers("Accept: application/json")
    suspend fun getUserPopularity(): Response<APIResponse<UserStatsResult>>
}
