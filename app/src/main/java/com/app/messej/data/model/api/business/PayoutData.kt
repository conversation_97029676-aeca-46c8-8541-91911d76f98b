package com.app.messej.data.model.api.business


import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.annotations.SerializedName
import java.util.Locale

data class PayoutData(
    @SerializedName("flax_rate") val flaxRate: Double,
    @SerializedName("id") val id: Int,
    @SerializedName("membership") val membership: String,
    @SerializedName("name") val name: String,
    @SerializedName("processing_fee") val processingFee: Double,
    @SerializedName("transfer_fee") val transferFee: Double,
    @SerializedName("profile_image") val profileImage: String,
    @SerializedName("receivable") val receivable: Double,
    @SerializedName("requested_date") val requestedDate: String,
    @SerializedName("mena_fees") val menaFees: Double,
    @SerializedName("requested_points_for_review") val requestedPointsForReview: Double,
    @SerializedName("status") val status: PayoutStatus? = null,
    @SerializedName("status_id") val statusId: Int,
    @SerializedName("time_created") val timeCreated: String,
    @SerializedName("time_updated") val timeUpdated: String,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("verified") val verified: Boolean,
    @SerializedName("payout_cancellable") val payoutCancellable: Boolean
) {

    enum class PayoutStatus {
        @SerializedName("Incomplete")
        INCOMPLETE,
        @SerializedName("New Request")
        NEW_REQUEST,
    }

    val requestedDateFormatted
        get() = DateTimeUtils.format(DateTimeUtils.parseZonedDateTime(requestedDate, DateTimeUtils.FORMAT_ISO_DATE_TIME), DateTimeUtils.FORMAT_DDMMYYYY_SLASHED)

    val receivableRounded : Double
        get() = String.format(Locale.US, "%.2f", receivable).toDouble()

    val transferFeeRounded : Double
        get() = String.format(Locale.US, "%.2f", transferFee).toDouble()

    val menaFeesRounded : Double
        get() = String.format(Locale.US, "%.2f", menaFees).toDouble()

    val payoutStatus: String
        get() = when (status) {
            PayoutStatus.INCOMPLETE -> "Incomplete"
            PayoutStatus.NEW_REQUEST -> "New Request"
            else -> ""
        }

    val isPaymentTypeMENA: Boolean
        get() = menaFees != 0.0



}