package com.app.messej.data.model.entity


import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractUserWithStats
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.UserSoundTrack
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.data.model.socket.UserLastSeen
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(tableName = EntityDescriptions.TABLE_OTHER_USER)
@TypeConverters(OtherUser.UserSoundTrackConverter::class, CurrentUser.UserEmpowerment.UserEmpowermentTypeConverter::class)
data class OtherUser(
    @PrimaryKey
    @SerializedName("id"                            ) @ColumnInfo(name = COLUMN_USER_ID                 ) override val id           : Int,
    @SerializedName("name"                          ) @ColumnInfo(name = "name"                         ) override val name         : String     = "",
    @SerializedName("thumbnail"                     ) @ColumnInfo(name = "thumbnail"                    ) override val thumbnail    : String?    = null,
    @SerializedName("username"                      ) @ColumnInfo(name = "username"                     ) override val username     : String     = "",

    @SerializedName("membership"                    ) @ColumnInfo(name = "membership"                   ) override val membership   : UserType,
    @SerializedName("verified"                      ) @ColumnInfo(name = "verified"                     ) override val verified     : Boolean,

    @SerializedName("stars"                         ) @ColumnInfo(name = "stars"                        ) override val stars        : Int        = 0,
    @SerializedName("likers"                        ) @ColumnInfo(name = "likers"                       ) override val likers       : Int        = 0,
    @SerializedName("dears"                         ) @ColumnInfo(name = "dears"                        ) override val dears        : Int        = 0,
    @SerializedName("fans"                          ) @ColumnInfo(name = "fans"                         ) override val fans         : Int        = 0,
    @SerializedName("user_status", alternate = ["citizenship"]) @ColumnInfo(name = "user_status"        ) override val citizenship  : UserCitizenship? = null,

    @SerializedName("about"                         ) @ColumnInfo(name = "about"                        ) val about                 : String? = null,
    @SerializedName("joinday"                       ) @ColumnInfo(name = "joinday"                      ) val joinDay               : String? = "",

    @SerializedName("isSuperstar"                   ) @ColumnInfo(name = "isSuperstar"                  ) val isSuperstar           : Boolean,
    @SerializedName("followerType"                  ) @ColumnInfo(name = "followerType"                 ) val followerType          : FollowerType?,
    @SerializedName("isStar"                        ) @ColumnInfo(name = "isStar"                       ) val isStar                : Boolean,

    @SerializedName("muted"                         ) @ColumnInfo(name = "muted"                        ) val muted                 : Boolean? = null,

    @SerializedName("broadcast_likers_privacy"      ) @ColumnInfo(name = "broadcast_likers_privacy"     ) val likersPrivacy         : Boolean? = null,
    @SerializedName("total_broadcasts"              ) @ColumnInfo(name = "total_broadcasts"             ) val totalBroadcasts       : Int? = null,
    @SerializedName("broadcast_received"            ) @ColumnInfo(name = "broadcast_received"           ) val broadcastReceived     : Boolean? = null,
    @SerializedName("total_likes"                   ) @ColumnInfo(name = "total_likers"                 ) val totalLikers           : Int? = null,
    @SerializedName("total_huddle_owned"            ) @ColumnInfo(name = "total_huddle_owned"           ) val totalHuddleOwned      : Int? = 0,
    @SerializedName("total_huddles_admin"           ) @ColumnInfo(name = "total_huddles_admin"          ) val totalHuddlesAdmin     : Int? = 0,
    @SerializedName("total_huddles_participant"     ) @ColumnInfo(name = "total_huddles_participant"    ) val totalHuddlesParticipant: Int? = 0,

    @SerializedName("last_seen"                     ) @ColumnInfo(name = "last_seen"                    ) val lastSeen              : String? = null,
    @SerializedName("online"                        ) @ColumnInfo(name = "online"                       ) val online                : Boolean,

    @SerializedName("notification"                  ) @ColumnInfo(name = "notification"                 ) val notification          : String? = null,
    @SerializedName("sound_track"                   ) @ColumnInfo(name = "sound_track"                  ) val soundTrack            : UserSoundTrack? = null,
    @SerializedName("preview"                       ) @ColumnInfo(name = "preview"                      ) val preview               : Boolean? = null,

    @SerializedName("dage"                          ) @ColumnInfo(name = "dage"                         ) val dage                  : Int? = 0,
    @SerializedName("flax_increment"                ) @ColumnInfo(name = "flax_increment"               ) val flaxIncrement         : Boolean? = false,
    @SerializedName("flax_rate"                     ) @ColumnInfo(name = "flax_rate"                    ) val flaxRate              : Double? = 0.0,
    @SerializedName("flax_rate_percentage"          ) @ColumnInfo(name = "flax_rate_percentage"         ) val flaxRatePercentage    : Double? = 0.0,

    @SerializedName("blocked_by_leader"             ) @ColumnInfo(name = "blocked_by_leader"            ) val blockedByLeader       : Boolean? = false,
    @SerializedName("blocked_by_admin"              ) @ColumnInfo(name = "blocked_by_admin"             ) val blockedByAdmin        : Boolean? = false,
    @SerializedName("contributor_level"             ) @ColumnInfo("contributor_level"             ) val contributorLevel      : String?=null,
    @SerializedName("player_level"                  ) @ColumnInfo("player_level"                  ) val playerLevel           : String?=null,
    @SerializedName("subscription_expiry_date"      ) @ColumnInfo("subscription_expiry_date"      ) val subscriptionExpiryDate: String?=null,
    @SerializedName("issue_date"                    ) @ColumnInfo("issue_date"                    ) val issueDate             : String?=null,
    @SerializedName("total_huddle_joined"           ) @ColumnInfo(name = "total_huddle_joined"          ) val totalHuddleJoined     : Int? = 0,
    @SerializedName("total_received_gifts"          ) @ColumnInfo(name = "total_received_gifts"         ) val totalReceivedGifts    : Int? = 0,
    @SerializedName("total_flash_published"         ) @ColumnInfo(name = "total_flash_published"        ) val totalFlashPublished   : Int? = 0,
    @SerializedName("total_public_podiums"          ) @ColumnInfo(name = "total_public_podiums"         ) val totalPublicPodiums    : Int? = 0,
    @SerializedName("user_managed_huddles_participants") @ColumnInfo(name = "userManagedHuddlesParticipants") val userManagedHuddlesParticipants: Int? = 0,
    @SerializedName("tribe_participants_count"      ) @ColumnInfo(name = "tribe_participants_count"     ) val tribeParticipantsCount: Int? = 0,
    @SerializedName("total_postat_posts"            ) @ColumnInfo(name = "total_postat_posts"           ) val totalPostatPosts: Int? = 0,
    @SerializedName("user_empower"                  ) @ColumnInfo(name = "user_empower"                 ) val userEmpowerment       : CurrentUser.UserEmpowerment?=null,
    @SerializedName("country_code"                  ) @ColumnInfo(name = "countryCode"                  ) override var countryCode  : String?  = null,
    @SerializedName("popularity"                    ) @ColumnInfo(name = "popularity") val popularity: Int? = null

    ): AbstractUserWithStats(){

    companion object {
        const val COLUMN_USER_ID = "id"
        const val COLUMN_CHAT_BLOCKED = "chat_blocked"
        const val CROWN_THRESHOLD = 10000
    }

    val hasLastSeenOrOnlineInfo: Boolean
        get() = lastSeen!=null || online

    val lastSeenParsed: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(lastSeen)

    val followers: Int
        get() {
            return fans + likers
        }

    val userLastSeen: UserLastSeen
        get() = UserLastSeen(id, online, lastSeen)

    class UserSoundTrackConverter {
        @TypeConverter
        fun fromJson(json: String?): UserSoundTrack? {
            return Gson().fromJson(json, UserSoundTrack::class.java)
        }

        @TypeConverter
        fun toJson(userSoundTrack: UserSoundTrack?): String? {
            return Gson().toJson(userSoundTrack)
        }
    }


    val lvModified: String
        get() = contributorLevel?.substringAfter("LV")?.trim()?:"0"

    val plModified : String
        get() = playerLevel?.substringAfter("PL")?.trim()?:"0"

    val showCrownForSkill : Boolean
        get() = plModified.toInt() > CROWN_THRESHOLD  /*return true if skill lvl grater than or equal to 10001*/

    val showCrownForGenerosity : Boolean
        get() = lvModified.toInt() > CROWN_THRESHOLD  /*return true if genoricity lvl grater than or equal to 10001*/


    val plIsValid:Boolean
        get()= playerLevel.isNullOrEmpty()

    val showCrownPL : Boolean
        get() = plModified.toInt() >= 10001  /*return true if skill lvl grater than or equal to 10001*/

    val showCrownLV : Boolean
        get() = lvModified.toInt() >= 10001  /*return true if genoricity lvl grater than or equal to 10001*/


}
