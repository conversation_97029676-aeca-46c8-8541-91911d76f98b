package com.app.messej.data.model.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.app.messej.data.model.AbstractChatMessage.SendStatus
import com.app.messej.data.model.AbstractFlashVideo
import com.app.messej.data.model.MediaMeta
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.ShareTo
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.room.EntityDescriptions
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.utils.DataFormatHelper
import com.google.gson.annotations.SerializedName
import java.time.ZonedDateTime

@Entity(
    tableName = EntityDescriptions.TABLE_FLASH,
    indices = []
)
@TypeConverters(
    MediaMeta.Converter::class,
    ShareTo.Converter::class,
    SenderDetails.Converter::class
)
data class FlashVideo (
    @SerializedName("id"   ) @PrimaryKey @ColumnInfo(name = COLUMN_ID          ) override val id              : String,
    @SerializedName("caption"          ) @ColumnInfo(name = "caption"          ) override val caption         : String?       = null,
    @SerializedName("media_meta"       ) @ColumnInfo(name = "media_meta"       ) override val mediaMeta       : MediaMeta,

    @SerializedName("user_id"          ) @ColumnInfo(name = COLUMN_USER_ID     ) override val userId          : Int,
    @SerializedName("sender_detail"    ) @ColumnInfo(name = "sender_detail"    )          val senderDetails   : SenderDetails?,
    @SerializedName("share_to"         ) @ColumnInfo(name = "share_to"         ) override val shareTo         : ShareTo,
    @SerializedName("video_url"        ) @ColumnInfo(name = "video_url"        ) override val videoUrl        : String,
    @SerializedName("thumbnail_url"    ) @ColumnInfo(name = "thumbnail"        ) override val thumbnailUrl    : String,

    @SerializedName("category"         ) @ColumnInfo(name = "category"         )          val category        : String?       = null,
    @SerializedName("category_id"      ) @ColumnInfo(name = "category_id"      ) override val categoryId      : Int?          = null,

    @SerializedName("comment_disabled" ) @ColumnInfo(name = "comment_disabled" ) override var commentDisabled : Boolean      = false,

    @SerializedName("is_liked"         ) @ColumnInfo(name = "isLiked"          )          var isLiked         : Boolean       = false,
    @SerializedName("likes_count"      ) @ColumnInfo(name = "likes_count"      )          var likeCount      : Int           = 0,
    @SerializedName("share_count"      ) @ColumnInfo(name = "share_count"      )          val shareCount      : Int           = 0,
    @SerializedName("views_count"      ) @ColumnInfo(name = "views_count"      )          val viewCount      : Int           = 0,
    @SerializedName("comments_count"   ) @ColumnInfo(name = "comments_count"   )          val commentCount   : Int           = 0,
    @SerializedName("gifts_count"      ) @ColumnInfo(name = "gifts_count", defaultValue = "0")          val giftCount      : Int           = 0,

    @SerializedName("is_reported"      ) @ColumnInfo(name = "is_reported"      )          val isReported      : Boolean       = false,
    @SerializedName("is_blocked"       ) @ColumnInfo(name = "is_blocked", defaultValue = "0")            val isBlocked      : Boolean       = false,
    @SerializedName("is_delete"        ) @ColumnInfo(name = "is_deleted", defaultValue = "0")            val isDeleted      : Boolean       = false,
    @SerializedName("time_created"     ) @ColumnInfo(name = COLUMN_CREATED_TIME)          val timeCreated     : String?       = null,
    @SerializedName("time_updated"     ) @ColumnInfo(name = "time_updated"     )          val timeUpdated     : String?       = null,
    @SerializedName("share_link"       ) @ColumnInfo(name = "share_link"       )        val shareLink     : String?       = null,

    @ColumnInfo(name = COLUMN_FLASH_TYPE                                       )          var flashType       : FlashType?     = FlashType.UNKNOWN
): AbstractFlashVideo() {
    companion object {
        const val COLUMN_ID = "id"
        const val COLUMN_USER_ID = "userId"
        const val COLUMN_SEND_STATUS = "sendStatus"
        const val COLUMN_CREATED_TIME = "time_created"
        const val COLUMN_FLASH_TYPE = "flash_type"
    }

    enum class FlashType {
        UNKNOWN,
        MINE,
        SAVED,
        DRAFT
    }

    /**
     * set to [SendStatus.PENDING] when creating a chat to be sent via socket.
     */
    @ColumnInfo(name = COLUMN_SEND_STATUS, defaultValue = "NONE")
    @Transient
    var sendStatus: SendStatus? = SendStatus.NONE
        get() {
            if (field==null) field = SendStatus.NONE
            return field
        }

    val likeCountFormatted: String
        get() = DataFormatHelper.numberToK(likeCount)

    val shareCountFormatted: String
        get() = DataFormatHelper.numberToK(shareCount)

    val viewCountFormatted: String
        get() = DataFormatHelper.numberToK(viewCount)

    val commentCountFormatted: String
        get() = DataFormatHelper.numberToK(commentCount)

    val giftCountFormatted: String
        get() = DataFormatHelper.numberToK(giftCount)

    open val parsedCreatedTime: ZonedDateTime?
        get() = DateTimeUtils.parseZonedDateTime(timeCreated)

    val isAvailable: Boolean
        get() = !isDeleted && !isBlocked && !isReported

    fun sanitize() {
        mediaMeta.mediaType = MediaType.VIDEO
        mediaMeta.thumbnail = thumbnailUrl
    }
}