package com.app.messej.data.api

import com.app.messej.data.Constants
import com.app.messej.data.model.PostCommentPayload
import com.app.messej.data.model.api.APIResponse
import com.app.messej.data.model.api.EmojiResponse
import com.app.messej.data.model.api.MessageReportResponse
import com.app.messej.data.model.api.ReportCategoryResponse
import com.app.messej.data.model.api.ReportToManagerRequest
import com.app.messej.data.model.api.RestrictUserRequest
import com.app.messej.data.model.api.UploadCredentialsResponse
import com.app.messej.data.model.api.auth.UnreadItemsResponse
import com.app.messej.data.model.api.huddles.AcceptChatRequest
import com.app.messej.data.model.api.huddles.AddParticipantsResponse
import com.app.messej.data.model.api.huddles.AddRemovedUserRequest
import com.app.messej.data.model.api.huddles.AddRemovedUserResponse
import com.app.messej.data.model.api.huddles.AdminInviteRequest
import com.app.messej.data.model.api.huddles.BlockUnblockHuddleUserRequest
import com.app.messej.data.model.api.huddles.BlockUnblockHuddleUserResponse
import com.app.messej.data.model.api.huddles.BlockedHuddleListResponse
import com.app.messej.data.model.api.huddles.BlockedPrivateMessageResponse
import com.app.messej.data.model.api.huddles.HuddleActionRequest
import com.app.messej.data.model.api.huddles.HuddleActionResponse
import com.app.messej.data.model.api.huddles.HuddleBanRequest
import com.app.messej.data.model.api.huddles.HuddleCancelActionRequest
import com.app.messej.data.model.api.huddles.HuddleCancelInvitationRequest
import com.app.messej.data.model.api.huddles.HuddleCategoriesResponse
import com.app.messej.data.model.api.huddles.HuddleChatMessagesResponse
import com.app.messej.data.model.api.huddles.HuddleEligibilityResponse
import com.app.messej.data.model.api.huddles.HuddleInfo
import com.app.messej.data.model.api.huddles.HuddleInnerSearchListResponse
import com.app.messej.data.model.api.huddles.HuddleInvitationsResponse
import com.app.messej.data.model.api.huddles.HuddleLanguage
import com.app.messej.data.model.api.huddles.HuddleListResponse
import com.app.messej.data.model.api.huddles.HuddleMuteRequest
import com.app.messej.data.model.api.huddles.HuddleMyPostSummaryResponse
import com.app.messej.data.model.api.huddles.HuddleMyPostsListResponse
import com.app.messej.data.model.api.huddles.HuddlePinRequest
import com.app.messej.data.model.api.huddles.HuddlePostCommentsResponse
import com.app.messej.data.model.api.huddles.HuddleReportedCommentsResponse
import com.app.messej.data.model.api.huddles.HuddleReportedMessagesResponse
import com.app.messej.data.model.api.huddles.HuddleRequestListResponse
import com.app.messej.data.model.api.huddles.HuddleRequestsAdminActionRequest
import com.app.messej.data.model.api.huddles.HuddleRequestsResponse
import com.app.messej.data.model.api.huddles.HuddleSalesListResponse
import com.app.messej.data.model.api.huddles.HuddleSuggestionResponse
import com.app.messej.data.model.api.huddles.ImageURLResponse
import com.app.messej.data.model.api.huddles.PinHuddlePostReponse
import com.app.messej.data.model.api.huddles.PinHuddlePostRequest
import com.app.messej.data.model.api.huddles.PrivateChatListResponse
import com.app.messej.data.model.api.huddles.PrivateChatMessagesResponse
import com.app.messej.data.model.api.huddles.PrivateChatUserInfo
import com.app.messej.data.model.api.huddles.PrivateMessagesSearchResponse
import com.app.messej.data.model.api.huddles.PrivateMessagesSuggestionResponse
import com.app.messej.data.model.api.huddles.PurchaseHuddleRequest
import com.app.messej.data.model.api.huddles.RemoveUserRequest
import com.app.messej.data.model.api.huddles.ReportedByResponse
import com.app.messej.data.model.api.huddles.SellHuddleRequest
import com.app.messej.data.model.api.huddles.SellHuddleResponse
import com.app.messej.data.model.api.huddles.SendInvitationsRequest
import com.app.messej.data.model.api.huddles.UnblockHuddleRequest
import com.app.messej.data.model.api.huddles.UnblockedHuddleResponse
import com.app.messej.data.model.entity.HuddleChatMessage
import com.app.messej.data.model.entity.HuddlePrivacySettings
import com.app.messej.data.model.entity.PostCommentItem
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatRoomInfo
import com.app.messej.data.model.entity.PublicHuddle
import com.app.messej.data.model.entity.PublicHuddleInterventions
import com.app.messej.data.model.enums.BroadcastMode
import com.app.messej.data.model.enums.HuddleType
import com.app.messej.data.model.enums.PrivateMessageSuggestionType
import com.app.messej.data.model.enums.PrivateMessageUserType
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.model.socket.HuddleChatMessagePayload
import okhttp3.MultipartBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query

interface ChatAPIService {
    @GET("/huddles")
    @Headers("Accept: application/json")
    suspend fun getPublicHuddles(@Query("tab") tab: String, @Query("page") page: Int, @Query("type") type: HuddleType): Response<APIResponse<HuddleListResponse>>

    @GET("/huddles/requests_and_invites")
    @Headers("Accept: application/json")
    suspend fun getHuddleRequestAndInvites(@Query("count") count: Boolean): Response<APIResponse<HuddleRequestListResponse>>

    @GET("/huddles")
    @Headers("Accept: application/json")
    suspend fun huddleSearch(@Query("page") page: Int, @Query("type") type: HuddleType, @Query("keyword") keyword: String): Response<APIResponse<HuddleListResponse>>

    @GET("/huddles/post/search")
    @Headers("Accept: application/json")
    suspend fun huddleInnerSearch(@Query("page") page: Int,@Query("keyword") keyword: String,@Query("huddleId") huddleId: Int): Response<APIResponse<HuddleInnerSearchListResponse>>

    @GET("/huddles")
    @Headers("Accept: application/json")
    suspend fun publicHuddleSearch(@Query("page") page: Int, @Query("type") type: HuddleType, @Query("tab") tab: String, @Query("keyword") keyword: String): Response<APIResponse<HuddleListResponse>>

    @GET("/huddles/{id}")
    @Headers("Accept: application/json")
    suspend fun getHuddleInfo(@Path("id") id: Int): Response<APIResponse<PublicHuddle>>

    @POST("/huddles/{id}/feeds")
    @Headers("Accept: application/json")
    suspend fun sendHuddlePost(@Path("id") id: Int, @Body message: HuddleChatMessagePayload): Response<APIResponse<HuddleChatMessage>>

    @PUT("/huddles/{id}/feeds")
    @Headers("Accept: application/json")
    suspend fun editHuddlePost(@Path("id") id: Int, @Body message: HuddleChatMessagePayload): Response<APIResponse<HuddleChatMessage>>

    @GET("/huddles/{id}")
    @Headers("Accept: application/json")
    suspend fun getFullHuddleInfo(@Path("id") id: Int): Response<APIResponse<HuddleInfo>>

    @GET("/huddles/intervened_users/{id}")
    @Headers("Accept: application/json")
    suspend fun getHuddleInterventions(@Path("id") id: Int): Response<APIResponse<PublicHuddleInterventions>>

    @GET("/huddles/{id}/participation_policy")
    @Headers("Accept: application/json")
    suspend fun getHuddlePrivacySettings(@Path("id") id: Int): Response<APIResponse<HuddlePrivacySettings>>

    @PUT("/huddles/{id}/participation_policy")
    @Headers("Accept: application/json")
    suspend fun updateHuddlePrivacy(@Path("id") id: Int,@Body request:HuddlePrivacySettings): Response<APIResponse<HuddlePrivacySettings>>

    @GET("/huddles/{id}/invitations/search")
    @Headers("Accept: application/json")
    suspend fun getAddParticipantsList(@Path("id") id: Int,
                                       @Query("search_type") type: String,
                                       @Query("page") page: Int,
                                       @Query("limit") limit: Int = 50,
                                       @Query("keyword") keyword: String = ""): Response<APIResponse<AddParticipantsResponse>>

    @POST("huddles/{id}/invitations")
    @Headers("Accept: application/json")
    suspend fun sendInvitations(@Path("id") id: Int, @Body request: SendInvitationsRequest): Response<APIResponse<Unit>>

    @GET("/huddles/search")
    @Headers("Accept: application/json")
    suspend fun getHuddleSuggestion(@Query("keyword") keyword: String = "",
                                    @Query("free_offset") freeOffset: Int = 0,
                                    @Query("premium_offset") premiumOffset: Int = 0): Response<APIResponse<HuddleSuggestionResponse>>

    @GET("/huddles/myposts?huddleList=1")
    @Headers("Accept: application/json")
    suspend fun getMyPostsSummary(): Response<APIResponse<HuddleMyPostSummaryResponse>>

    @GET("/huddles/myposts")
    @Headers("Accept: application/json")
    suspend fun getMyPostsList(@Query("page") page: Int): Response<APIResponse<HuddleMyPostsListResponse>>

    @PUT("/huddles/pin")
    @Headers("Accept: application/json")
    suspend fun pinHuddles(@Body request: HuddlePinRequest): Response<APIResponse<Unit>>

    @PUT("/huddles/mute")
    @Headers("Accept: application/json")
    suspend fun muteHuddles(@Body request: HuddleMuteRequest): Response<APIResponse<Unit>>

    @Multipart
    @POST("/huddles")
    suspend fun createHuddle(@Part file: MultipartBody.Part?, @Part data: MultipartBody.Part): Response<APIResponse<PublicHuddle>>

    @GET("/huddles/categories")
    @Headers("Accept: application/json")
    suspend fun getHuddleCategories(@Query("huddle_type") huddleType: HuddleType?): Response<APIResponse<HuddleCategoriesResponse>>

    @POST("/huddles/{id}/requests")
    @Headers("Accept: application/json")
    suspend fun huddleRequestJoin(@Path("id") id: Int): Response<APIResponse<HuddleActionResponse>>

    @POST("/huddles/{id}/join")
    @Headers("Accept: application/json")
    suspend fun huddleInstantJoin(@Path("id") id: Int): Response<APIResponse<HuddleActionResponse>>

    @PUT("/huddles/{id}/requests")
    @Headers("Accept: application/json")
    suspend fun handleHuddleRequestAction(@Path("id") id: Int, @Body request: HuddleActionRequest): Response<APIResponse<HuddleActionResponse>>

    @PUT("/huddles/{id}/requests")
    @Headers("Accept: application/json")
    suspend fun handleHuddleCancelAction(@Path("id") id: Int, @Body request: HuddleCancelActionRequest): Response<APIResponse<Unit>>

    @PUT("/huddles/{id}/requests")
    @Headers("Accept: application/json")
    suspend fun blockHuddleJoinRequest(@Path("id") id: Int, @Body request: HuddleCancelActionRequest): Response<APIResponse<Unit>>

    @PUT("/huddles/{id}/invitations")
    @Headers("Accept: application/json")
    suspend fun handleHuddleAdminRequestAction(@Path("id") id: Int, @Body request: HuddleActionRequest): Response<APIResponse<Unit>>

    @PUT("/huddles/{id}/invitations")
    @Headers("Accept: application/json")
    suspend fun cancelHuddleInvitation(@Path("id") id: Int, @Body request: HuddleCancelInvitationRequest): Response<APIResponse<Unit>>

    @PUT("huddles/{id}/invitations/admin")
    @Headers("Accept: application/json")
    suspend fun handleHuddleAdminInvite(@Path("id") id: Int, @Body request: HuddleActionRequest): Response<APIResponse<Unit>>

    @GET("/huddles/{id}/messages")
    @Headers("Accept: application/json")
    suspend fun getHuddleMessages(@Path("id") id: Int, @Query("recent") recent: String? = null, @Query("previous") previous: String? = null): Response<APIResponse<HuddleChatMessagesResponse>>

    @GET("/huddles/blocks")
    @Headers("Accept: application/json")
    suspend fun getBlockedHuddles(): Response<APIResponse<BlockedHuddleListResponse>>

    @PUT("/huddles/{id}/block ")
    @Headers("Accept: application/json")
    suspend fun unBlockHuddle(@Path("id") id: Int, @Body request: UnblockHuddleRequest): Response<APIResponse<UnblockedHuddleResponse>>

    @GET("/chat/chats")
    @Headers("Accept: application/json")
    suspend fun getPrivateChats(
        @Query("chatType") type: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE,
        @Query("pageState") state: String? = null,
        @Query("intruders") intruders: Int? = null,
    ): Response<APIResponse<PrivateChatListResponse>>

    @GET("/chat/chats/user/{id}")
    @Headers("Accept: application/json")
    suspend fun getPrivateUserInfo(@Path("id") receiverId: Int): Response<APIResponse<PrivateChatUserInfo>>

    @GET("/chat/chats/room/{id}")
    @Headers("Accept: application/json")
    suspend fun getPrivateChat(@Path("id") receiver: Int): Response<APIResponse<PrivateChat>>

    @GET("/chat/chats/room")
    @Headers("Accept: application/json")
    suspend fun getPrivateChatInfo(@Query("receiver") receiver: Int): Response<APIResponse<PrivateChatRoomInfo>>

    @GET("/chat/chats/history")
    @Headers("Accept: application/json")
    suspend fun getPrivateChatMessages(@Query("roomId") roomId: String,
                                       @Query("recent") recent: String? = null,
                                       @Query("previous") previous: String? = null): Response<APIResponse<PrivateChatMessagesResponse>>

    @DELETE("/chat/chats/messages")
    @Headers("Accept: application/json")
    suspend fun deletePrivateChatMessages(@Query("chatroomId") roomId: String, @Query("chatType") chatType: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE
    ): Response<APIResponse<Unit>>



    @GET("/chat/chats/messages/{id}/signed_url")
    @Headers("Accept: application/json")
    suspend fun getMediaSignedUrl(@Path("id") messageId: String, @Query("room_id") roomId: String? = null): Response<APIResponse<ImageURLResponse>>

    @GET("/chat/broadcasts/messages/{id}/signed_url")
    @Headers("Accept: application/json")
    suspend fun getBroadcastMediaSignedUrl(@Path("id") messageId: String, @Query("broadcast_type") mode: BroadcastMode, @Query("broadcaster") broadcaster: Int): Response<APIResponse<ImageURLResponse>>

    @GET("/chat/androidupload")
    @Headers("Accept: application/json")
    suspend fun getMediaUploadURL(@Query("key") key: String = "", @Header(Constants.HTTP_HEADER_CONTENT_TYPE) type: String): Response<APIResponse<String>>

    @GET("/chat/chats/blocked")
    @Headers("Accept: application/json")
    suspend fun getBlockedPrivateMessageList(): Response<APIResponse<BlockedPrivateMessageResponse>>

    @GET("/chat/chats/unread")
    @Headers("Accept: application/json")
    suspend fun getUnreadCounts(): Response<APIResponse<UnreadItemsResponse>>

    @GET("/huddles/{id}/requests")
    @Headers("Accept: application/json")
    suspend fun getHuddleRequests(
        @Path("id") huddleId: Int, @Query("limit") limit: Int = 50, @Query("page") page: Int
    ): Response<APIResponse<HuddleRequestsResponse>>

    @PUT("/huddles/{id}/requests")
    @Headers("Accept: application/json")
    suspend fun huddleRequestAdminAction(
        @Path("id") huddleId: Int, @Body request: HuddleRequestsAdminActionRequest
    ): Response<APIResponse<Unit>>

    @GET("/huddles/{id}/invitations")
    @Headers("Accept: application/json")
    suspend fun getHuddleInvites(
        @Path("id") huddleId: Int, @Query("limit") limit: Int = 50, @Query("page") page: Int
    ): Response<APIResponse<HuddleInvitationsResponse>>

    @GET("/huddles/{id}/reports")
    @Headers("Accept: application/json")
    suspend fun getHuddleReportedMessages(
        @Path("id") huddleId: Int, @Query("limit") limit: Int = 50, @Query("page") page: Int?
    ): Response<APIResponse<HuddleReportedMessagesResponse>>

    @GET("/huddles/{id}/reports/{reportedId}/users")
    @Headers("Accept: application/json")
    suspend fun getHuddleReportedParticipants(
        @Path("id") huddleId: Int,
        @Path("reportedId") reportedId: Int,
        @Query("page") page: Int
    ): Response<APIResponse<ReportedByResponse>>

    @GET("/huddles/{id}/{postId}/comments")
    @Headers("Accept: application/json")
    suspend fun getHuddlePostComments(
        @Path("id") huddleId: Int,
        @Path("postId") postId: String,
    ): Response<APIResponse<HuddlePostCommentsResponse>>

    @POST("/huddles/{id}/{postId}/comments")
    @Headers("Accept: application/json")
    suspend fun writePostComment(
        @Path("id") huddleId: Int,
        @Path("postId") postId: String,
        @Body request: PostCommentPayload
    ): Response<APIResponse<Unit>>

    @DELETE("/huddles/{id}/{postId}/comments")
    @Headers("Accept: application/json")
    suspend fun deletePostComment(
        @Path("id") huddleId: Int,
        @Path("postId") postId: String,
        @Query("commentId") commentId: String,
        @Query("deleteAction") deleteAction: String,
    ): Response<APIResponse<Unit>>

    @DELETE("/huddles/{id}/members")
    @Headers("Accept: application/json")
    suspend fun leaveHuddle(
        @Path("id") huddleId: Int
    ): Response<APIResponse<Unit>>

    @DELETE("/huddles/{id}")
    @Headers("Accept: application/json")
    suspend fun deleteHuddle(
        @Path("id") huddleId: Int
    ): Response<APIResponse<Unit>>

    @PUT("/huddles/{id}/block")
    @Headers("Accept: application/json")
    suspend fun blockOrUnblockUsers(
        @Path("id") huddleId: Int,
        @Body request: BlockUnblockHuddleUserRequest
    ): Response<APIResponse<BlockUnblockHuddleUserResponse>>

    @PUT("/huddles/{id}/members")
    @Headers("Accept: application/json")
    suspend fun removeUser(
        @Path("id") huddleId: Int,
        @Body request: RemoveUserRequest
    ): Response<APIResponse<Unit>>

    @PUT("/huddles/{id}/admins")
    @Headers("Accept: application/json")
    suspend fun dismissAdmin(
        @Path("id") huddleId: Int,
        @Body request: RemoveUserRequest
    ): Response<APIResponse<Unit>>


    @POST("/huddles/{id}/pin")
    @Headers("Accept: application/json")
    suspend fun pinHuddlePost(
        @Path("id") huddleId: Int,
        @Body request: PinHuddlePostRequest
    ): Response<APIResponse<PinHuddlePostReponse>>

    @Multipart
    @PUT("/huddles/{id}")
    @Headers("Accept: application/json")
    suspend fun updateHuddle(
        @Path("id") huddleId: Int,
        @Part file: MultipartBody.Part?,
        @Part data: MultipartBody.Part
    ): Response<APIResponse<PublicHuddle>>

    @POST("/chat/chats/type")
    @Headers("Accept: application/json")
    suspend fun privateChatRequestAcceptAction(
        @Body request: AcceptChatRequest
    ): Response<APIResponse<Unit>>


    @DELETE("/chat/chats")
    @Headers("Accept: application/json")
    suspend fun privateChatRequestDeletedAction(
        @Query("chatroomId") roomId: String,
        @Query("chatType") chatType: PrivateChat.ChatType = PrivateChat.ChatType.REQUEST
    ): Response<APIResponse<Unit>>

    @DELETE("/chat/chats")
    @Headers("Accept: application/json")
    suspend fun privateChatIntruderDeletedAction(
        @Query("chatroomId") roomId: String,
        @Query("chatType") chatType: PrivateChat.ChatType = PrivateChat.ChatType.PRIVATE,
        @Query("deleteMsg") deleteMsg: Int = 1
    ): Response<APIResponse<Unit>>

    @DELETE("/chat/chats")
    @Headers("Accept: application/json")
    suspend fun deleteRestrictedUserChat(
        @Query("chatroomId") roomId: String,
        @Query("chatType") chatType: PrivateChat.ChatType = PrivateChat.ChatType.REQUEST,
        @Query("deleteMsg") deleteMsg: Int = 1
    ): Response<APIResponse<Unit>>

    @POST("/huddles/{id}/invitations/admin")
    @Headers("Accept: application/json")
    suspend fun adminInvite(
        @Path("id") huddleId: Int,
        @Body request: AdminInviteRequest
    ): Response<APIResponse<BlockUnblockHuddleUserResponse.User>>



    @GET("/chat/users/suggestions")
    @Headers("Accept: application/json")
    suspend fun newMessageSuggestions(
        @Query("page") page: Int = 1,
        @Query("suggestion_type") suggestionType: PrivateMessageSuggestionType
    ): Response<APIResponse<PrivateMessagesSuggestionResponse>>

    @GET("/chat/users")
    @Headers("Accept: application/json")
    suspend fun newPrivateMessageSearch(
        @Query("user_type") userType: PrivateMessageUserType,
        @Query("search_type") searchType: SearchType,
        @Query("page") page: Int = 1,
        @Query("keyword") keyword: String
    ): Response<APIResponse<PrivateMessagesSearchResponse>>

    @GET("/huddles/report_categories")
    @Headers("Accept: application/json")
    suspend fun getMessageReportCategories(): Response<APIResponse<ReportCategoryResponse>>

    @POST("/huddles/{huddleId}/messages/{messageId}/report")
    @Headers("Accept: application/json")
    suspend fun reportMessage(@Path("huddleId") huddleId: Int, @Path("messageId") messageId: String, @Body request: ReportToManagerRequest.MessageReportRequest): Response<APIResponse<MessageReportResponse>>

    @POST("/huddles/{huddleId}/messages/{messageId}/report")
    @Headers("Accept: application/json")
    suspend fun reportComment(@Path("huddleId") huddleId: Int, @Path("messageId") messageId: String, @Query("commentId") commentId: String, @Body request: ReportToManagerRequest.CommentReportRequest): Response<APIResponse<PostCommentItem>>

    @DELETE("/huddles/{huddleId}/messages/{messageId}/report")
    @Headers("Accept: application/json")
    suspend fun cancelMessageReport(@Path("huddleId") huddleId: Int, @Path("messageId") messageId: String): Response<APIResponse<MessageReportResponse>>

    @POST("/huddles/{huddleId}/messages/{messageId}/report")
    @Headers("Accept: application/json")
    suspend fun cancelCommentReport(@Path("huddleId") huddleId: Int, @Path("messageId") messageId: String, @Query("commentId") commentId: String, @Query("action") action: String): Response<APIResponse<PostCommentItem>>

    @POST("/chat/chats/type")
    @Headers("Accept: application/json")
    suspend fun restrictUser(@Body request: RestrictUserRequest): Response<APIResponse<Unit>>

    @GET("/user/video/secrets")
    @Headers("Accept: application/json")
    suspend fun getVideoUploadCredentials(): Response<APIResponse<UploadCredentialsResponse>>

    @PUT("/huddles/{huddleId}/ban")
    @Headers("Accept: application/json")
    suspend fun banUnbanHuddleParticipant(@Path("huddleId") huddleId:Int,@Body request: HuddleBanRequest ): Response<APIResponse<Unit>>
    // Huddle report comment
    @GET("/huddles/{id}/reported_comments")
    @Headers("Accept: application/json")
    suspend fun getHuddleReportedComments(
        @Path("id") huddleId: Int, @Query("limit") limit: Int = 50, @Query("page") page: Int?
    ): Response<APIResponse<HuddleReportedCommentsResponse>>

    @DELETE("/huddles/{id}/{postId}/comments")
    @Headers("Accept: application/json")
    suspend fun deleteReportedComment(
        @Path("id") huddleId: Int,
        @Path("postId") postId: String?,
        @Query("commentId") commentId: String,
        @Query("sender_blocked") senderBlocked: Boolean,
        @Query("action_by") actionBy: Int
    ): Response<APIResponse<Unit>>

    @GET("/huddles/{id}/reported_comments/{reportedId}/users")
    @Headers("Accept: application/json")
    suspend fun getHuddleCommentReportedParticipants(
        @Path("id") huddleId: Int,
        @Path("reportedId") reportedId: Int,
        @Query("page") page: Int
    ): Response<APIResponse<ReportedByResponse>>


    @GET("/user/languages")
    @Headers("Accept: application/json")
    suspend fun getHuddleLanguages(): Response<APIResponse<List<HuddleLanguage>>>

    @GET("/user/stickers")
    @Headers("Accept: application/json")
    suspend fun getEmojis(@Query("page") page: String?, @Query("keyword") keyword: String): Response<APIResponse<EmojiResponse>>

    @GET("/user/stickers")
    @Headers("Accept: application/json")
    suspend fun getSearchEmojis(@Query("page_count") page: String?, @Query("keyword") keyword: String): Response<APIResponse<EmojiResponse>>

    @GET("/huddles/sharelink/{id}")
    @Headers("Accept: application/json")
    suspend fun getShareLink(@Path("id") huddleId: Int, @Query("message_id") page: String): Response<APIResponse<String>>

    @POST("/huddles/tribe/adduser")
    @Headers("Accept: application/json")
    suspend fun addRemovedUsers(@Body request: AddRemovedUserRequest): Response<APIResponse<AddRemovedUserResponse>>

    @POST("huddles/{id}/sale")
    @Headers("Accept: application/json")
    suspend fun sellHuddle(@Path("id") id: Int, @Body request: SellHuddleRequest): Response<APIResponse<SellHuddleResponse>>

    @PUT("huddles/{id}/sale")
    @Headers("Accept: application/json")
    suspend fun editSellHuddle(@Path("id") id: Int, @Body request: SellHuddleRequest): Response<APIResponse<SellHuddleResponse>>

    @GET("/huddles/forsale")
    @Headers("Accept: application/json")
    suspend fun getSellHuddleList(@Query("page") page: Int, @Query("limit") limit: Int): Response<APIResponse<HuddleSalesListResponse>>

    @POST("/huddles/{id}/buyhuddle")
    @Headers("Accept: application/json")
    suspend fun buyHuddle(@Path("id") id: Int): Response<APIResponse<String>>

    @POST("/huddles/huddle-limit-purchase")
    @Headers("Accept: application/json")
    suspend fun purchaseCreateHuddle(@Body request: PurchaseHuddleRequest): Response<APIResponse<String>>

    @GET("/huddles/eligibility")
    @Headers("Accept: application/json")
    suspend fun getHuddleCreateEligibility(): Response<APIResponse<HuddleEligibilityResponse>>

}
