package com.app.messej.data.socket.repository

import android.util.Log
import androidx.room.withTransaction
import com.app.messej.MainApplication
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.PrivateChat
import com.app.messej.data.model.entity.PrivateChatMessage
import com.app.messej.data.model.enums.StarType
import com.app.messej.data.model.socket.BroadCastLastSeenPayLoad
import com.app.messej.data.model.socket.ChatDeleteEvent
import com.app.messej.data.model.socket.ChatIgnoreEvent
import com.app.messej.data.model.socket.ChatReadAllMessagesPayload
import com.app.messej.data.model.socket.ChatReadDeliverEvent
import com.app.messej.data.model.socket.ChatReadStatusPayload
import com.app.messej.data.model.socket.ChatRoomUpdateEvent
import com.app.messej.data.model.socket.ChatThreadDeleteEvent
import com.app.messej.data.model.socket.HuddleVoicePlayBackPayload
import com.app.messej.data.model.socket.PrivateChatDeletePayload
import com.app.messej.data.model.socket.PrivateChatEnterPayload
import com.app.messej.data.model.socket.PrivateChatIgnorePayload
import com.app.messej.data.model.socket.PrivateChatMessagePayload
import com.app.messej.data.model.socket.UserBlockEvent
import com.app.messej.data.model.socket.UserBlockPayload
import com.app.messej.data.model.socket.UserLastSeen
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.ChatRepository
import com.app.messej.data.socket.ChatSocketEvent
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.json.JSONObject

object PrivateChatEventRepository: BaseEventRepository<ChatSocketEvent>(ChatSocketRepository) {

    private val accountRepo = AccountRepository(MainApplication.applicationInstance())

    private val dao = db.getHuddleDao()

    override fun handleEvent(event: ChatSocketEvent, data: JSONObject): Boolean {
        when (event) {
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_MESSAGE -> onNewChatMessage(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_MESSAGE_RESTRICTED -> onNewChatMessage(data, true)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_ONLINE_STATUS -> onUserOnline(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_DELETE -> onDeletePrivateChatMessage(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_BLOCK -> onNewBlockedStatus(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_READ_STATUS -> onChatReadStatus(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_READ_ALL -> onChatReadAll(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_DELIVERY_STATUS -> onChatDeliveredStatus(data)
            ChatSocketEvent.RX_PRIVATE_CHAT_DELIVERED_ALL -> onChatDeliveredAllStatus(data)
            ChatSocketEvent.RX_PRIVATE_CHAT_THREAD_DELETE -> onChatThreadDelete(data)
            ChatSocketEvent.RX_TX_PRIVATE_CHAT_IGNORE -> onChatIgnore(data)
            ChatSocketEvent.RX_PRIVATE_CHAT_ROOM_UPDATE -> onChatRoomUpdate(data)
            ChatSocketEvent.RX_TX_HUDDLE_VOICE_CHAT-> onVoiceChat(data)
            else -> return false
        }
        return true
    }
    private val _lastSeenInfo = MutableStateFlow<Map<Int, UserLastSeen>>(mapOf())
    val lastSeenInfo: StateFlow<Map<Int, UserLastSeen>> = _lastSeenInfo

    private fun onUserOnline(data: JSONObject) {
        val info = Gson().fromJson<UserLastSeen>(data.toString())
        val map = _lastSeenInfo.value?.toMutableMap() ?: mutableMapOf()
        if (info._lastSeen == null && !info.online) {
            map.remove(info.userId)
        } else {
            map[info.userId] = info
        }
        _lastSeenInfo.value =map
    }

    val user: CurrentUser get() = accountRepo.user

    private val _onReadAction = MutableSharedFlow<PrivateChatMessage>(replay = 0)
    val onReadAction: SharedFlow<PrivateChatMessage> = _onReadAction

    private fun onNewChatMessage(data: JSONObject, isRequest: Boolean = false) = runBlocking {
        val tempChatId = if (data.has("private_chat_id")) data.getString("private_chat_id") else null
        val msg = Gson().fromJson<PrivateChatMessage>(data.toString())
        val existing = db.getChatMessageDao().getPrivateChatMessageWithMedia(msg.messageId)
        if (existing != null && existing.message.isActivity) {
            return@runBlocking
        }
        if (msg.sender!= user.id && msg.receiver!=user.id) {
            Firebase.crashlytics.recordException(Exception("Wrong event received for private chat: $data"))
            return@runBlocking
        }
        if (db.getPrivateChatDao().getPrivateChat(msg.roomId) == null) {
            val senderDetails = if (data.has("sender_profile")) Gson().fromJson<SenderDetails>(data.getJSONObject("sender_profile").toString()) else null
            val followedByEach = if (data.has("followed_by_each")) data.getBoolean("followed_by_each") else null
            if (senderDetails!=null) {
                db.getPrivateChatDao().insert(PrivateChat(
                    id = msg.roomId,
                    sender = msg.receiver,
                    receiver = msg.sender,
                    receiverDetails = senderDetails,
                    roomId = msg.roomId,
                    unreadCount = 0,
                    followedByMe = followedByEach ?: false,
                    lastMessageInternal = msg,
                    type = if(isRequest) PrivateChat.ChatType.REQUEST else PrivateChat.ChatType.PRIVATE,
                    privateMessageTab = if (followedByEach == true) PrivateChat.PrivateMessageTabType.BUDDIES else PrivateChat.PrivateMessageTabType.INTRUDER
                ))
            }
        }
        ChatRepository(MainApplication.applicationInstance()).insertNewChatMessage(msg, tempChatId)
        val unread = if (data.has("unread")) data.getInt("unread") else null
        unread?.let {

            db.getPrivateChatDao().updateUnreadCount(msg.roomId, unread)
        }
        if(msg.sender!= user.id) {
            setDeliveredStatus(msg.roomId, msg.messageId)
            _onReadAction.emit(msg)
        }
    }


    private fun onChatReadStatus(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatReadDeliverEvent>(data.toString())
        var msg = db.getChatMessageDao().getPrivateChatMessage(evt.messageId)
        msg ?: return@runBlocking
        msg = msg.copy(
            read = evt.read,
            deliveredTime = evt.delivered
        )
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateChat(msg)
        }
    }

    private fun onChatDeliveredAllStatus(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatReadDeliverEvent>(data.toString())
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateDeliveredAllMessages(evt.created, evt.roomId, evt.created)
        }
    }

    private fun onChatThreadDelete(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatThreadDeleteEvent>(data.toString())
        withContext(Dispatchers.IO) {
            if (user.id == evt.sender) {
                db.getPrivateChatDao().delete(evt.roomId)
                db.getChatMessageDao().deleteAllPrivateChatMessages(evt.roomId)
            }
        }
    }

    private fun onChatIgnore(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatIgnoreEvent>(data.toString())
        withContext(Dispatchers.IO) {
            db.getPrivateChatDao().apply {
                val chat = getPrivateChat(evt.roomId)
                chat?.let {
                    insert(chat.copy(isIgnored = true).apply {
                        privateMessageTab = PrivateChat.PrivateMessageTabType.INTRUDER
                    })
                }
            }
        }
    }

    fun ignoreChat(userId: Int, chatType: PrivateChat.ChatType, roomId: String): Boolean{
        val payload = PrivateChatIgnorePayload(userId, chatType.toString().uppercase(), roomId)
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_IGNORE, payload)
    }

    private fun onChatDeliveredStatus(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatReadDeliverEvent>(data.toString())
        var msg = db.getChatMessageDao().getPrivateChatMessage(evt.messageId)
        msg ?: return@runBlocking
        msg = msg.copy(
            deliveredTime = evt.delivered
        )
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateChat(msg)
        }
    }

    private fun onChatReadAll(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatReadDeliverEvent>(data.toString())
        withContext(Dispatchers.IO) {
            db.getChatMessageDao().updateReadAllMessages(evt.read!!, evt.roomId, evt.created)
            db.getPrivateChatDao().getPrivateChat(evt.roomId)?.let { privateChat ->
                privateChat.lastMessage?.let {
                    db.getPrivateChatDao().update(
                        privateChat.copy(
                            lastMessageInternal = it.copy(read = evt.read)
                        )
                    )
                }
            }
        }
    }

    private fun onDeletePrivateChatMessage(data: JSONObject) = runBlocking {
        val evt = Gson().fromJson<ChatDeleteEvent>(data.toString())
        withContext(Dispatchers.IO) {
            if (evt.deleteForAll) {
                var msg = db.getChatMessageDao().getPrivateChatMessage(evt.messageId)
                msg ?: return@withContext
                msg = msg.copy(
                    deleted = true
                )
                db.getChatMessageDao().updateChat(msg)
            }
            else db.getChatMessageDao().deletePrivateChatMessage(evt.messageId)
        }
    }

    private val _onRoomUpdate = MutableSharedFlow<ChatRoomUpdateEvent>(replay = 0)
    val onRoomUpdate: SharedFlow<ChatRoomUpdateEvent> = _onRoomUpdate

    private fun onChatRoomUpdate(data: JSONObject) = runBlocking {
        //TODO Update room info also. Currently only used to update the followedByEach field of chat.
        val event = Gson().fromJson<ChatRoomUpdateEvent>(data.toString())
        _onRoomUpdate.emit(event)
        withContext(Dispatchers.IO){
            db.getPrivateChatDao().apply {
                val chatWithRoomInfo = getPrivateChatWithRoomInfo(event.roomId)
                Log.w("PCVB", "onChatRoomUpdate: ${Gson().toJson(event)}")
                Log.w("PCVB", "onChatRoomUpdate: ${Gson().toJson(chatWithRoomInfo)}")
                chatWithRoomInfo?: return@withContext
                val userID = accountRepo.user.id
                val currentUserType = event.chatRoomInfo.threadType?.get(userID.toString())
                //managing type based on thread type of current user
                val type = if (currentUserType == PrivateChat.ChatType.PRIVATE.name) {
                    PrivateChat.ChatType.PRIVATE
                } else {
                    PrivateChat.ChatType.REQUEST
                }
                val chat = chatWithRoomInfo.chat.copy(
                    followedByMe = event.followedByMe,
                    isIgnored = false,
                    type = type
                ).apply {
                    privateMessageTab = if (event.followedByMe == true) PrivateChat.PrivateMessageTabType.BUDDIES else PrivateChat.PrivateMessageTabType.INTRUDER
                }
                Log.w("PCVB", "onChatRoomUpdate: ${Gson().toJson(chat)}")

                val roomInfo = chatWithRoomInfo.roomInfo?.copy(
                    chatRoom = event.chatRoomInfo
                )
                Log.w("PCVB", "onChatRoomUpdate: ${Gson().toJson(roomInfo)}")
                db.withTransaction {
                    update(chat)
                    roomInfo?.let {
                        updatePrivateChatRoomInfo(it)
                    }
                }
            }
        }
    }
    private fun onVoiceChat(data: JSONObject)= runBlocking {
        Log.d("privateChatVoice", data.toString())
        try {
            val event = Gson().fromJson<PrivateChatMessage>(data.toString())
            withContext(Dispatchers.IO) {
                var msg = db.getChatMessageDao().getPrivateChatMessage(event.messageId)
                msg ?: return@withContext
                    msg = msg.copy(
                        mediaMeta = event.mediaMeta
                    )
                    db.getChatMessageDao().updateChat(msg)
            }


        } catch (e: Exception) {
            Log.e("PER", "privateChatVoice: ", e)
        }
    }


    fun sendChatMessage(msg: PrivateChatMessagePayload) = ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_MESSAGE, msg)

    fun announceOnlineStatus(userId: Int, online: Boolean) {
        val payload = PrivateChatEnterPayload(userId, online)
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_ONLINE_STATUS, payload)
    }

    fun announceBroadcastOnlineStatus(userId: Int) {
        val payload = BroadCastLastSeenPayLoad(userId, true, StarType.STAR)
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_ONLINE_STATUS, payload)
    }

    fun deletePrivateChatMessage(messages: List<PrivateChatMessage>, deleteForEveryone: Boolean) {
        val payload = PrivateChatDeletePayload(
            messages = messages.map {
                PrivateChatDeletePayload.MessagesToDelete(
                    messageId = it.messageId, roomId = it.roomId
                )
            }, deleteForEveryone = deleteForEveryone
        )
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_DELETE, payload)
    }

    private fun onNewBlockedStatus(data: JSONObject) = runBlocking {
        val event = Gson().fromJson<UserBlockEvent>(data.toString())
        //TODO update relevant DB row
    }

    fun toggleChatBlock(userId: Int): Boolean {
        return ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_BLOCK, UserBlockPayload(userId))
    }

    fun setReadStatus(roomId: String, messageId: String) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_READ_STATUS, ChatReadStatusPayload(roomId, messageId))
    }

    fun setReadAll(roomId: String, messageId: String, type: String? = null) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_READ_ALL, ChatReadAllMessagesPayload(roomId, messageId, messageId))
    }

    fun setDeliveredStatus(roomId: String, messageId: String) {
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_PRIVATE_CHAT_DELIVERY_STATUS, ChatReadStatusPayload(roomId, messageId))
    }
    fun privateChatVoicePlayBackStatus(playBack: HuddleVoicePlayBackPayload){
        ChatSocketRepository.sendEvent(ChatSocketEvent.RX_TX_HUDDLE_VOICE_CHAT, HuddleVoicePlayBackPayload(roomId = playBack.roomId, playedBy = playBack.playedBy, messageId =  playBack.messageId, type = playBack.type))
    }
}