package com.app.messej.data.model.socket

import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.enums.MaidanLike
import com.google.gson.annotations.SerializedName

data class PodiumMaidanLikePayload(
    @SerializedName("podium_id"      ) val podiumId: String,
    @SerializedName("user_id") val userId: Int,
    @SerializedName("user") val user: PodiumChallenge.ChallengeUser,
    @SerializedName("likes") val likes: Int,
    @SerializedName("time_created") val timeCreated: String
): SocketEventPayload() {
    val maidanLike: MaidanLike?
        get() = MaidanLike.fromCount(likes)
}
