package com.app.messej.data.utils

import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.SystemClock
import android.provider.BaseColumns
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.util.Log
import androidx.annotation.RawRes
import androidx.core.content.FileProvider
import com.app.messej.BuildConfig
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.model.entity.OfflineMedia
import com.app.messej.data.model.enums.AttachDocumentType
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.utils.DateTimeUtils.FORMAT_DATE_TIME_FILE_TS
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import id.zelory.compressor.Compressor
import id.zelory.compressor.constraint.destination
import id.zelory.compressor.constraint.format
import id.zelory.compressor.constraint.quality
import id.zelory.compressor.constraint.resolution
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.nio.file.Files
import java.nio.file.StandardCopyOption
import java.text.CharacterIterator
import java.text.StringCharacterIterator
import java.time.LocalDateTime
import kotlin.math.abs
import kotlin.math.roundToLong


object MediaUtils {

    private const val DIR_TEMP_MEDIA = "/media/temp"

    private const val DIR_CHAT_IMAGES_IN = "/media/images"
    private const val DIR_CHAT_AUDIO_IN = "/media/audio"
    private const val DIR_CHAT_VIDEO_IN = "/media/videos"
    private const val DIR_CHAT_DOCUMENT_IN = "/media/documents"
    private const val DIR_FLASH = "/media/flash"
    private const val DIR_FLASH_CACHE = "/media/flash/cache"
    private const val DIR_POSTAT = "/media/postat"
    private const val DIR_GIFT_VIDEO="/media/gift_video"
    private const val DIR_CHALLENGE_VIDEO="/media/challenge_video"
    private const val DIR_GALLERY = "Flashat"

    const val QUALITY_HIGH = 82
    const val QUALITY_MEDIUM = 64
    const val QUALITY_LOW = 50

    enum class FileExtension(val ext: String) {
        JPEG("jpg"),
        AAC("aac"),
        MP4("mp4")
    }

    val RESOLUTION_1440P = MediaResolution(1440, 1440)
    val RESOLUTION_1080P = MediaResolution(1080, 1080)

    private fun getDirectory(type: MediaType): String {
        return when(type) {
            MediaType.IMAGE -> DIR_CHAT_IMAGES_IN
            MediaType.AUDIO -> DIR_CHAT_AUDIO_IN
            MediaType.VIDEO -> DIR_CHAT_VIDEO_IN
            MediaType.DOCUMENT -> DIR_CHAT_DOCUMENT_IN
        }
    }

    private fun getFlashDirectory() = DIR_FLASH

    fun getFlashCacheDirectory(c: Context) = File(c.cacheDir, DIR_FLASH_CACHE)

    private fun getPostatDirectory() = DIR_POSTAT

    fun getVideoDownloadDirectory() = File(getDirectory(MediaType.VIDEO))

    private fun createFile(c: Context, fileName: String, dir: String): File {
        val storageDir = File(c.getExternalFilesDir(null)!!.absolutePath + dir)
        var success = true
        if (!storageDir.exists()) {
            success = storageDir.mkdirs()
        }
        return if (success) File(storageDir, fileName) else throw Exception("Could not create directory")
    }

    suspend fun saveDownloadedFile(c: Context, file: ResponseBody, fileName: String, type: MediaType): File = withContext(Dispatchers.IO) {
        val imageFile = createFile(c,fileName, getDirectory(type))
        file.byteStream().use {
            val fOut: OutputStream = FileOutputStream(imageFile)
            fOut.use { targetOutputStream ->
                it.copyTo(targetOutputStream)
            }
            fOut.close()
        }
        return@withContext imageFile
    }

    suspend fun createBlankFile(c: Context, fileName: String, type: MediaType): File = withContext(Dispatchers.IO) {
        return@withContext createFile(c,fileName, getDirectory(type))
    }

    suspend fun createGiftVideoFile(c: Context, fileName: String): File = withContext(Dispatchers.IO) {
        return@withContext createFile(c,fileName, DIR_GIFT_VIDEO)
    }

    suspend fun createChallengeVideoFile(c: Context, fileName: String): File = withContext(Dispatchers.IO) {
        return@withContext createFile(c,fileName, DIR_CHALLENGE_VIDEO)
    }
    suspend fun createBirthdayVideoFile(c: Context, fileName: String): File = withContext(Dispatchers.IO) {
        return@withContext createFile(c,fileName, DIR_GIFT_VIDEO)
    }

    @Throws(IOException::class)
    suspend fun createTempFile(context: Context, type: MediaType): File = withContext(Dispatchers.IO) {
        // Create an image file name
        val timeStamp: String = DateTimeUtils.format(LocalDateTime.now(), FORMAT_DATE_TIME_FILE_TS)
        val storageDir: File = context.cacheDir
        val tempDir = File(storageDir, DIR_TEMP_MEDIA)
        if (!tempDir.exists()) {
            tempDir.mkdirs()
        }
        val file = when(type) {
            MediaType.IMAGE -> File.createTempFile("image_${timeStamp}",".${FileExtension.JPEG.ext}", tempDir)
            MediaType.AUDIO -> File.createTempFile("audio_${timeStamp}",".${FileExtension.AAC.ext}", tempDir)
            MediaType.VIDEO -> File.createTempFile("video_${timeStamp}",".${FileExtension.MP4.ext}", tempDir)
            MediaType.DOCUMENT -> throw Exception("Cannot create temp file for document type")
        }
        file
    }

    suspend fun storeContentUriToTempImageFile(context: Context, uri: Uri): File = withContext(Dispatchers.IO) {
        val inputStream = context.contentResolver.openInputStream(uri)
//        val file = if (mediaType == MEDIA_TYPE_IMAGE) {
        val file = createTempFile(context,MediaType.IMAGE)
//        } else {
//            createTempPdfFile(context)
//        }
        val fos = FileOutputStream(file)
        inputStream?.copyTo(fos)
        fos.close()
        file
    }

    suspend fun storeAudioFile(context: Context, path: String, fileName: String): File = withContext(Dispatchers.IO) {
        val name = "$fileName.${FileExtension.AAC.ext}"
        val rawFile = File(path)
        val audioFile = createFile(context,name, getDirectory(MediaType.AUDIO))
        Firebase.crashlytics.log("moved audio file from ${rawFile.path} to ${audioFile.path}")
        Files.move(rawFile.toPath(), audioFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
        rawFile.delete()
        audioFile
    }

    @Throws(IOException::class)
    suspend fun storeImageFile(context: Context, path: String, fileName: String, resolution: MediaResolution = RESOLUTION_1080P, quality: Int = QUALITY_MEDIUM): File = withContext(Dispatchers.IO) {
        val name = "$fileName.${FileExtension.JPEG.ext}"
        val rawFile = File(path)
        val imageFile = createFile(context,name, getDirectory(MediaType.IMAGE))
        compressRawImageFile(context, rawFile, imageFile, resolution, quality)
    }

    @Throws(IOException::class)
    suspend fun storeVideoFile(context: Context, path: String, fileName: String): File = withContext(Dispatchers.IO) {
        val name = "$fileName.${FileExtension.MP4.ext}"
        val rawFile = File(path)
        val videoFile = createFile(context,name, getDirectory(MediaType.VIDEO))
        Files.move(rawFile.toPath(), videoFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
        rawFile.delete()
        videoFile
    }

    @Throws(IOException::class)
    suspend fun moveFiles(src: File, dest: File) = withContext(Dispatchers.IO) {
        Files.move(src.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING)
    }

    @Throws(IOException::class)
    suspend fun storeFlashVideoFile(context: Context, path: String, fileName: String): File = withContext(Dispatchers.IO) {
        val name = "$fileName.${FileExtension.MP4.ext}"
        val rawFile = File(path)
        val videoFile = createFile(context,name, getFlashDirectory())
        Files.move(rawFile.toPath(), videoFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
        rawFile.delete()
        videoFile
    }

    @Throws(IOException::class)
    suspend fun storePostatFile(context: Context, path: String, fileName: String, mediaType: MediaType, index: Int): File = withContext(Dispatchers.IO) {
        val ext = if(mediaType == MediaType.VIDEO) FileExtension.MP4.ext else FileExtension.JPEG.ext
        val name = "${fileName}_0${index.plus(1)}.${ext}" //eg: uuid_03.mp4 as 3rd postat media in order
        val rawFile = File(path)
        val mediaFile = createFile(context,name, getPostatDirectory())
        Files.move(rawFile.toPath(), mediaFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
        rawFile.delete()
        mediaFile
    }

    @Throws(IOException::class)
    suspend fun storeDocumentFile(context: Context, uri: Uri): File = withContext(Dispatchers.IO) {
        val inputStream = context.contentResolver.openInputStream(uri)
        val timeStamp: String = DateTimeUtils.format(LocalDateTime.now(), FORMAT_DATE_TIME_FILE_TS)
        val srcMeta = getFileNameAndSizeFromUri(uri,context.contentResolver)?: throw Exception("Could not get document info")
        val fileName = "document_${timeStamp}.${getFileExtensionFromName(srcMeta.first)}"
        val file = createFile(context,fileName, getDirectory(MediaType.DOCUMENT))
        val fos = FileOutputStream(file)
        inputStream?.copyTo(fos)
        fos.close()
        file
    }

    @Throws(IOException::class)
    suspend fun compressImageToTempFile(context: Context, path: String, resolution: MediaResolution = RESOLUTION_1080P, quality: Int = QUALITY_MEDIUM): File = withContext(Dispatchers.IO) {
        val rawFile = File(path)
        val imageFile = createTempFile(context,MediaType.IMAGE)
        compressRawImageFile(context, rawFile, imageFile, resolution, quality, false)
    }

    @Throws(IOException::class)
    private suspend fun compressRawImageFile(context: Context, rawFile: File, dest: File, resolution: MediaResolution = RESOLUTION_1080P, quality: Int = QUALITY_MEDIUM, deleteOriginal: Boolean = true): File = withContext(Dispatchers.IO) {
        val compressedFile = Compressor.compress(context, rawFile) {
            destination(dest)
            resolution(resolution.width, resolution.height)
            quality(quality)
            format(Bitmap.CompressFormat.JPEG)
        }
        Log.d("MediaUtil", "compressRawImageFile: ${getImageResolution(compressedFile)}, ${getFileSize(compressedFile)}")
        if(deleteOriginal) rawFile.delete()
        compressedFile
    }

    fun deleteFile(path: String) {
        try {
            File(path).delete()
        } catch (_: Exception) {}
    }
    fun deleteFile(file: File) {
        try {
            file.delete()
        } catch (_: Exception) {}
    }

    fun getMIME(file: File): String {
        return Files.probeContentType(file.toPath())
    }

    fun getFileSize(file: File): String = humanizeBytes(file.length())

    fun getMimeTypeFromUri(uri: Uri, contentResolver: ContentResolver): String? {
        return contentResolver.getType(uri)
    }

    fun getMediaTypeFromFileName(fileName: String): MediaType {
        val ext = getFileExtensionFromName(fileName)
        return AttachDocumentType.forExtension(ext).toMediaType()
    }

    fun AttachDocumentType.toMediaType() = when(this) {
        AttachDocumentType.IMAGE -> MediaType.IMAGE
        AttachDocumentType.AUDIO -> MediaType.AUDIO
        AttachDocumentType.VIDEO -> MediaType.VIDEO
        else -> MediaType.DOCUMENT
    }

    fun getFileNameAndSizeFromUri(uri: Uri, contentResolver: ContentResolver): Pair<String,Long>? {
        contentResolver.query(uri, null, null, null, null, null)?.use { cursor ->
            val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
            val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
            cursor.moveToFirst()
            val name = cursor.getString(nameIndex)
            val fileSize = cursor.getLong(sizeIndex)
            return Pair(name,fileSize)
        }
        return null
    }

    fun getFileName(uri: Uri, contentResolver: ContentResolver): String? {
        val returnCursor: Cursor = contentResolver.query(uri, null, null, null, null)?: return null
        returnCursor.use { cursor ->
            val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
            cursor.moveToFirst()
            return cursor.getString(nameIndex)
        }
    }

    fun humanizeBytes(bytes: Long): String {
        val absB = if (bytes == Long.MIN_VALUE) Long.MAX_VALUE else abs(bytes)
        if (absB < 1024) {
            return "$bytes B"
        }
        var value = absB
        val ci: CharacterIterator = StringCharacterIterator("KMGTPE")
        var i = 40
        while (i >= 0 && absB > 0xfffccccccccccccL shr i) {
            value = value shr 10
            ci.next()
            i -= 10
        }
        value *= java.lang.Long.signum(bytes).toLong()
        return String.format("%.1f %cB", value / 1024.0, ci.current())
    }

    fun getImageResolution(file: File): MediaResolution {
        val bitMapOption = BitmapFactory.Options()
        bitMapOption.inJustDecodeBounds = true
        BitmapFactory.decodeFile(file.absolutePath, bitMapOption)
        val imageWidth = bitMapOption.outWidth
        val imageHeight = bitMapOption.outHeight
        return MediaResolution(imageWidth, imageHeight)
    }

    private fun MediaMetadataRetriever.getResolution(release: Boolean = true): MediaResolution {
        val width: Int = extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull()?:0
        val height: Int = extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull()?:0

        val metaRotation: String = extractMetadata(METADATA_KEY_VIDEO_ROTATION).orEmpty()
        val rotation = metaRotation.toIntOrNull() ?: 0

        if(release) release()
        return if (rotation==90 || rotation==270) MediaResolution(height, width) else MediaResolution(width, height)
    }

    fun getVideoResolution(uri: Uri): MediaResolution {
        val mmr = MediaMetadataRetriever()
        try {
            mmr.setDataSource(MainApplication.applicationContext(), uri)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mmr.getResolution()
    }

    fun getVideoResolution(context: Context, @RawRes videoSource: Int): MediaResolution {
        val assetDescriptor = context.resources.openRawResourceFd(videoSource)
        val retriever = MediaMetadataRetriever()
        retriever.setDataSource(
            assetDescriptor.fileDescriptor,
            assetDescriptor.startOffset,
            assetDescriptor.length
        )
        return retriever.getResolution()
    }

    fun getDuration(file: File, millis: Boolean = false): Long {
        val mmr = MediaMetadataRetriever()
        mmr.setDataSource(file.absolutePath)
        val durationStr = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?:"0"
        val millSecond = durationStr.toDouble()
        mmr.release()
        return if (millis) millSecond.toLong() else millSecond.div(1000).roundToLong()
    }

    fun getDuration(uri: Uri, millis: Boolean = false): Long {
        val mmr = MediaMetadataRetriever()
        mmr.setDataSource(MainApplication.applicationContext(), uri)
        val durationStr = mmr.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)?:"0"
        val millSecond = durationStr.toDouble()
        mmr.release()
        return if (millis) millSecond.toLong() else millSecond.div(1000).roundToLong()
    }

    fun saveFileToDocuments(context: Context, file: File, subFolder: String? = null, fileName: String = file.name): Uri? {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val resolver = context.contentResolver

            val relativePath = if (subFolder != null) {
                "${Environment.DIRECTORY_DOCUMENTS}/$subFolder"
            } else {
                Environment.DIRECTORY_DOCUMENTS
            }

            val folder = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS), subFolder ?: "")
            if (!folder.exists()) folder.mkdirs()

            val contentValues = ContentValues().apply {
                put(MediaStore.MediaColumns.DISPLAY_NAME, fileName)  // File Name
                put(MediaStore.MediaColumns.MIME_TYPE, "text/plain")          // File Type
                put(MediaStore.MediaColumns.RELATIVE_PATH, relativePath)
                put(MediaStore.MediaColumns.IS_PENDING, true)
            }

            val uri = resolver.insert(MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL), contentValues) ?: return null

            resolver.openOutputStream(uri)?.use { out ->
                file.inputStream().use { input ->
                    input.copyTo(out)
                }
            }

            // Release "pending" status after write completes
            contentValues.clear()
            contentValues.put(MediaStore.MediaColumns.IS_PENDING, false)
            resolver.update(uri, contentValues, null, null)
            return uri
        } else {
            val documentsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            val folder = if (subFolder != null) File(documentsDir, subFolder) else documentsDir

            if (!folder.exists()) folder.mkdirs() // Ensure the subfolder exists

            val destFile = File(folder, file.name)
            try {
                file.inputStream().use { input ->
                    FileOutputStream(destFile).use { output ->
                        input.copyTo(output)
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
            }
            return Uri.fromFile(destFile)
        }
    }

    suspend fun saveImageToGallery(context: Context,file: File) = withContext(Dispatchers.IO) {
        val resolver = context.contentResolver

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val imageCollection = MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

            val newImage = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, file.name)
                put(MediaStore.Images.Media.IS_PENDING, true)
                put(MediaStore.Images.Media.MIME_TYPE, getMIME(file))
                put(MediaStore.Images.Media.DATE_ADDED, System.currentTimeMillis() / 1000)
                put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis())
                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/$DIR_GALLERY")
            }
            val newImageUri = resolver.insert(imageCollection, newImage)?: return@withContext

            resolver.openOutputStream(newImageUri)?.use { out ->
                val input = file.inputStream()
                input.copyTo(out)
                input.close()
                out.close()
            }

            // Now that we're finished, release the "pending" status
            newImage.clear()
            newImage.put(MediaStore.Images.Media.IS_PENDING, false)
            resolver.update(newImageUri, newImage, null, null)
        } else {
            val imageCollection = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

            val directory = File(Environment.getExternalStorageDirectory().toString() + "/${Environment.DIRECTORY_PICTURES}/$DIR_GALLERY")
            Log.d("MEDIA", "saveImageToGallery: ${directory.absolutePath}")
            if (!directory.exists()) {
                directory.mkdirs()
            }
            val fileName =  "${file.nameWithoutExtension}_${SystemClock.uptimeMillis()}.${file.extension}"
            val outFile = File(directory, fileName)
            outFile.outputStream().use { out ->
                val input = file.inputStream()
                input.copyTo(out)
                input.close()
                out.close()
            }
            val values = ContentValues()
            values.put(MediaStore.Images.Media.DATA, outFile.absolutePath)
            context.contentResolver.insert(imageCollection, values)
        }
    }

    suspend fun saveVideoToGallery(context: Context, file: File) = withContext(Dispatchers.IO) {
        val resolver = context.contentResolver

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val videoCollection = MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)

            val newVideo = ContentValues().apply {
                put(MediaStore.Video.Media.DISPLAY_NAME, file.name)
                put(MediaStore.Video.Media.IS_PENDING, true)
                put(MediaStore.Video.Media.MIME_TYPE, getMIME(file))
                put(MediaStore.Video.Media.DATE_ADDED, System.currentTimeMillis() / 1000)
                put(MediaStore.Video.Media.DATE_TAKEN, System.currentTimeMillis())
                put(MediaStore.Images.Media.RELATIVE_PATH, "Movies/$DIR_GALLERY")
            }
            val newVideoUri = resolver.insert(videoCollection, newVideo) ?: return@withContext

            resolver.openOutputStream(newVideoUri)?.use { out ->
                val input = file.inputStream()
                input.copyTo(out)
                input.close()
                out.close()
            }

            // Now that we're finished, release the "pending" status
            newVideo.clear()
            newVideo.put(MediaStore.Images.Media.IS_PENDING, false)
            resolver.update(newVideoUri, newVideo, null, null)
        } else {
            val videoCollection = MediaStore.Images.Media.EXTERNAL_CONTENT_URI

            val directory = File(Environment.getExternalStorageDirectory().toString() + "/${Environment.DIRECTORY_MOVIES}/$DIR_GALLERY")
            Log.d("MEDIA", "saveVideoToGallery: ${directory.absolutePath}")
            if (!directory.exists()) {
                directory.mkdirs()
            }
            val fileName = "${file.nameWithoutExtension}_${SystemClock.uptimeMillis()}.${file.extension}"
            val outFile = File(directory, fileName)
            outFile.outputStream().use { out ->
                val input = file.inputStream()
                input.copyTo(out)
                input.close()
                out.close()
            }
            val values = ContentValues()
            values.put(MediaStore.Images.Media.DATA, outFile.absolutePath)
            context.contentResolver.insert(videoCollection, values)
        }
    }

    fun OfflineMedia.getFile(): File {
        return File(this.path)
    }

    fun String.asUri(): Uri {
        return try {
            Uri.parse(this)
        } catch (e: Exception) {
            throw Exception("Failed to obtain URI")
        }
    }

    val File.uri get() = this.absolutePath.asUri()

    fun getUriForFile(context: Context, file: File): Uri = FileProvider.getUriForFile(context, "${BuildConfig.APPLICATION_ID}.provider", file)

    fun getFileExtensionFromName(name: String?): String? {
        name?: return null
        val parts = name.split(".")
        return parts.last()
    }

    fun getDocumentResFromType(type: AttachDocumentType?): Int {
        val res = when(type){
            null -> R.drawable.img_document_share_other
            AttachDocumentType.DOC -> R.drawable.img_document_share_docx
            AttachDocumentType.PPT -> R.drawable.img_document_share_pdf
            AttachDocumentType.IMAGE -> R.drawable.img_document_share_image
            AttachDocumentType.AUDIO -> R.drawable.img_document_share_music
            AttachDocumentType.VIDEO -> R.drawable.img_document_share_video
            AttachDocumentType.FONT -> R.drawable.img_document_share_other
            AttachDocumentType.PDF -> R.drawable.img_document_share_pdf
            AttachDocumentType.TEXT -> R.drawable.img_document_share_text
            AttachDocumentType.CSV -> R.drawable.img_document_share_csv
            else -> R.drawable.img_document_share_other
        }
        return res
    }

    enum class UriLoadable {
        YES, NO, MAYBE
    }
    fun isLoadable(uri: Uri, context: Context): UriLoadable {
        return when(uri.scheme) {
            "content" -> {
                if (DocumentsContract.isDocumentUri(context, uri))
                    if (documentUriExists(uri, context))
                        UriLoadable.YES
                    else
                        UriLoadable.NO
                else // Content URI is not from a document provider
                    if (contentUriExists(uri, context))
                        UriLoadable.YES
                    else
                        UriLoadable.NO
            }
            "file" -> if (File(uri.path).exists()) UriLoadable.YES else UriLoadable.NO
            // http, https, etc. No inexpensive way to test existence.
            else -> UriLoadable.MAYBE
        }
    }
    private fun documentUriExists(uri: Uri, context: Context): Boolean = resolveUri(uri, DocumentsContract.Document.COLUMN_DOCUMENT_ID, context)

    private fun contentUriExists(uri: Uri, context: Context): Boolean = resolveUri(uri, BaseColumns._ID, context)

    private fun resolveUri(uri: Uri, column: String, context: Context): Boolean {
        val cursor = context.contentResolver.query(uri, arrayOf(column), null, null, null)
        val result = cursor?.moveToFirst() ?: false
        cursor?.close()
        return result
    }


}