package com.app.messej.data.repository.pagingSources

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.GiftAPIService
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.DateTimeUtils.FORMAT_ISO_DATE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate
import kotlin.math.max

private const val STARTING_KEY = 1
class GiftListingDataSource (private val api: GiftAPIService,private val giftType: String,private val birthday:Boolean?=false,private val userLevelCongrats:Boolean?=false) : PagingSource<Int, GiftItem>() {
    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, GiftItem> {
        return try {
            withContext(Dispatchers.IO) {
                val currentPage =  params.key ?: STARTING_KEY
                val currentDate = DateTimeUtils.format(LocalDate.now(), FORMAT_ISO_DATE)

                val response = currentPage.let {
                    api.getGiftList(page = currentPage, giftType = giftType,birthday, citizenshipUpgrade = userLevelCongrats, currentDate = currentDate)
                }
                val responseData = mutableListOf<GiftItem>()

                val data = response.body()?.result?.giftItems ?: emptyList()
                data.forEach {
                    responseData.addAll(it.data.orEmpty())
                }

                val nextKey = if (response.body()?.result?.nextPage == false) null else currentPage.plus(1)
                LoadResult.Page(
                    data = responseData, prevKey = if (currentPage == 1) null else currentPage.minus(1), nextKey = nextKey
                )
            }
        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }
    override fun getRefreshKey(state: PagingState<Int, GiftItem>): Int? {
        val anchorPosition = state.anchorPosition ?: return null
        val gift = state.closestItemToPosition(anchorPosition) ?: return null
        return null
    }
    private fun ensureValidKey(key: Int) = max(STARTING_KEY, key)

}