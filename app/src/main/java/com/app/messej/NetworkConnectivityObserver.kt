package com.app.messej

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

object NetworkConnectivityObserver : ConnectivityObserver {

    private val connectivityManager = MainApplication.applicationContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    private val _connectionFlow = MutableStateFlow<ConnectivityObserver.ConnectionStatus>(ConnectivityObserver.ConnectionStatus.UNAVAILABLE)
    val connectionStatusFlow: StateFlow<ConnectivityObserver.ConnectionStatus> = _connectionFlow

    init {

        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Firebase.crashlytics.log("NetworkConnectivityObserver: onAvailable")
                _connectionFlow.tryEmit(ConnectivityObserver.ConnectionStatus.AVAILABLE)
            }

            override fun onLosing(network: Network, maxMsToLive: Int) {
                super.onLosing(network, maxMsToLive)
                Firebase.crashlytics.log("NetworkConnectivityObserver: onLosing")
                _connectionFlow.tryEmit(ConnectivityObserver.ConnectionStatus.LOSING)
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                Firebase.crashlytics.log("NetworkConnectivityObserver: onLost")
                _connectionFlow.tryEmit(ConnectivityObserver.ConnectionStatus.LOST)
            }

            override fun onUnavailable() {
                super.onUnavailable()
                Firebase.crashlytics.log("NetworkConnectivityObserver: onUnavailable")
                _connectionFlow.tryEmit(ConnectivityObserver.ConnectionStatus.UNAVAILABLE)
            }
        }

        connectivityManager.registerDefaultNetworkCallback(callback)
    }


    override fun observe(): Flow<ConnectivityObserver.ConnectionStatus> {
        return connectionStatusFlow
    }
}