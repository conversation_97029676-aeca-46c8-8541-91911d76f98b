package com.app.messej.ui.home.publictab.podiums

import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.annotation.CallSuper
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.databinding.FragmentPodiumBaseBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


abstract class PublicPodiumBaseFragment : Fragment(), MenuProvider {

    protected lateinit var binding: FragmentPodiumBaseBinding

    protected lateinit var mPodiumPagerAdapter: FragmentStateAdapter

    protected val viewModel: PublicPodiumViewModel by activityViewModels()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
        observe()
    }

    @CallSuper
    protected open fun setup() {

        binding.podiumPager.apply {
            isUserInputEnabled = false
            adapter = mPodiumPagerAdapter
        }
        binding.btnAddPodium.setOnClickListener {
            ensurePodiumCreateAllowed {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePodiumFragment())
            }
        }
        binding.btnLivePodium.setOnClickListener {
            viewModel.setCurrentTab(PodiumTab.LIVE_PODIUM)
        }
        binding.btnMyPodium.setOnClickListener{
            viewModel.setCurrentTab(PodiumTab.MY_PODIUM)
        }
        binding.btnLiveFriends.setOnClickListener{
            viewModel.setCurrentTab(PodiumTab.LIVE_FRIENDS)
        }

        viewModel.currentTab.value?.let {
            binding.podiumPager.setCurrentItem(it.ordinal, false)
        }
    }
    private fun observe() {
        viewModel.currentTab.observe(viewLifecycleOwner) {
            it?:return@observe
            if (binding.podiumPager.currentItem==it.ordinal) return@observe
            binding.podiumPager.setCurrentItem(it.ordinal,false)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        Log.w("MNUF", "PPBF: onCreateMenu")
        return menuInflater.inflate(R.menu.menu_home_podium, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_search -> {
                navigateToSearch()
            }
//            R.id.action_more -> showMoreMenu(menuItem)
            else -> return false
        }
        return true
    }

    protected abstract fun navigateToSearch()
}