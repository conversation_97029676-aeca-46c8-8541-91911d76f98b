package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.MutableLiveData
import androidx.paging.LoadState
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.databinding.FragmentPodiumAdminsBottomSheetBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.kennyc.view.MultiStateView

abstract class PodiumAdminsBaseFragment : ExpandableListBottomSheetDialogFragment() {

    protected lateinit var binding: FragmentPodiumAdminsBottomSheetBinding


//    protected var mAdminsListAdapter: BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumAdminsListBinding>>? = null

    protected var mAdminsAllListAdapter : PodiumAdminsListAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_admins_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        setEmptyView()
        super.onViewCreated(view, savedInstanceState)
        this.isCancelable = true
        setup()
    }

    private fun setup(){
        iniAdapter()
    }

    abstract val loadingStateLiveData: MutableLiveData<Boolean>
    abstract val kebabVisibility: Boolean

    private fun iniAdapter() {

        val adminsListDiffer = object : DiffUtil.ItemCallback<PodiumSpeaker>() {
            override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker): Boolean {
                return oldItem.id == newItem.id
            }
            override fun areContentsTheSame(oldItem: PodiumSpeaker,newItem: PodiumSpeaker): Boolean {
                return oldItem == newItem
            }
        }

        mAdminsAllListAdapter = PodiumAdminsListAdapter(kebabVisibility, ::setupMoreMenu)

//        mAdminsListAdapter = object : BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumAdminsListBinding>>(R.layout.item_podium_admins_list, mutableListOf()) {
//
//            override fun convert(holder: BaseDataBindingHolder<ItemPodiumAdminsListBinding>, item: PodiumSpeaker) {
//                holder.dataBinding?.apply {
//                    speaker = item
//                    actionKebab = kebabVisibility
//                    adminsActionButton.isVisible = setupMoreMenu(adminsActionButton, item)
//                }
//            }
//
//        }.apply {mAdminsAllListAdapter
//            animationEnable = true
//            adapterAnimation = ScaleInAnimation()
//            isAnimationFirstOnly = false
//            setDiffCallback(adminsListDiffer)
//            setOnItemClickListener { adapter, _, position ->
//
//            }
//        }

        binding.adminsList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(true)
            adapter = mAdminsAllListAdapter
        }

        mAdminsAllListAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
                    MultiStateView.ViewState.CONTENT
                }
            }
        }

    }

    abstract fun setupMoreMenu(v: View, item: PodiumSpeaker): Boolean

    private var emptyViewBinding: LayoutListStateEmptyBinding? = null
    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            emptyViewBinding?.apply {
                edsEmptyImage.setImageResource(R.drawable.im_eds_podium)
                edsEmptyMessage.text = resources.getString(R.string.podium_no_admins_eds_message)
            }
            Log.w("PodiumABSF", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("PodiumABSF", "setEmptyView: ")
        }
    }




}