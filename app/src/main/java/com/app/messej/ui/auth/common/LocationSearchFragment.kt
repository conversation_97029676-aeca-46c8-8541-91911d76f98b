package com.app.messej.ui.auth.common

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.databinding.FragmentRegisterSearchLocationBinding
import com.chad.library.adapter.base.animation.AlphaInAnimation


class LocationSearchFragment : Fragment() {

    private lateinit var binding: FragmentRegisterSearchLocationBinding

    private val viewModel: LocationSearchViewModel by viewModels()

    private var mAdapter: LocationSearchQuickAdapter? = null

    companion object {
        const val LOCATION_SEARCH_REQUEST_KEY = "locationSearchKey"
        const val LOCATION_SEARCH_RESULT_KEY = "locationSearchResultKey"
        const val LOCATION_SEARCH_RESULT_CURRENT = "currentLocationKey"

    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_register_search_location, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {
        initAdapter()
        binding.textInputSearch.editText?.requestFocus()
        val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(binding.textInputSearch.editText, InputMethodManager.SHOW_IMPLICIT)

        binding.currentLocationLayout.setOnClickListener {
            findNavController().popBackStack()
            setFragmentResult(LOCATION_SEARCH_REQUEST_KEY, bundleOf(LOCATION_SEARCH_RESULT_KEY to LOCATION_SEARCH_RESULT_CURRENT))
        }

        binding.textInputSearch.setStartIconOnClickListener { findNavController().popBackStack() }
    }

    private fun observe() {
        viewModel.suggestions.observe(viewLifecycleOwner){
            mAdapter?.apply {
                Log.d("SUG", "observe: list go ${data.size} to ${it?.size}")
                if (data.isEmpty() || it.isEmpty()) {
                    setNewInstance(it.orEmpty().toMutableList())
                } else {
                    setDiffNewData(it.orEmpty().toMutableList())
                }
            }
        }
    }

    private fun initAdapter() {
//        if(mAdapter != null) {
//            return
//        }
        Log.d("SUG", "initAdapter: create new adapter")
        mAdapter = LocationSearchQuickAdapter(mutableListOf())

        val layoutMan = LinearLayoutManager(context)

        binding.locationSearchList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }
        mAdapter!!.apply {
            animationEnable = true
            adapterAnimation = AlphaInAnimation()
            isAnimationFirstOnly = true

            setOnItemClickListener{ adapter, view, position ->
                val location = (adapter as LocationSearchQuickAdapter).data[position].placeId
                findNavController().popBackStack()
                setFragmentResult(LOCATION_SEARCH_REQUEST_KEY, bundleOf(LOCATION_SEARCH_RESULT_KEY to location))
            }

            setDiffCallback(LocationSearchQuickAdapter.DiffCallback())
        }
    }

}