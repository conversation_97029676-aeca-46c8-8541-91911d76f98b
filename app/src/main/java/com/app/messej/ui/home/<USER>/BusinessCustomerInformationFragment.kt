package com.app.messej.ui.home.businesstab

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import android.os.Bundle
import android.text.BidiFormatter
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.FragmentBusinessCustomerInformationBinding
import com.app.messej.ui.home.businesstab.operations.BusinessCustomerInformationViewModel
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachFragment
import com.app.messej.ui.home.publictab.huddles.create.CreateHuddleFragmentDirections
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonSizeSpec
import com.skydoves.balloon.createBalloon


class BusinessCustomerInformationFragment : BaseProfilePicAttachFragment() {

    private lateinit var binding: FragmentBusinessCustomerInformationBinding
    override val viewModel: BusinessCustomerInformationViewModel by activityViewModels()
    private val businessWithDrawviewModel: BusinessWithDrawViewModel by activityViewModels()
    
    override val bindingRoot: View
        get() = binding.root

    override val shouldCropImage: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_business_customer_information, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    @SuppressLint("StringFormatInvalid")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    private fun setup() {

        val titleText = getString(R.string.business_official_information_title_note)
        val text=String.format( getString(R.string.business_payout_note),  BidiFormatter.getInstance().unicodeWrap(titleText))
        binding.informationLabel.text = text.highlightOccurrences(titleText){StyleSpan(Typeface.BOLD)}

        binding.btnNext.setOnClickListener {

            if (viewModel.validateFields()){
                val payoutId = businessWithDrawviewModel._backEndDbID.value
                viewModel.submitPayoutDetails(payoutId)
            }
            else{
                showToast(R.string.business_payot_fill_all_fields)
            }
        }
        binding.btnCancel.setOnClickListener {
            findNavController().navigateSafe(BusinessCustomerInformationFragmentDirections.actionGlobalPayoutCancelDialogFragment())
        }

        // Set Egypt as the default country for Egyptian users for both pickers
        if (viewModel.user.isEgyptianUser) {
            binding.phoneCountryCodePicker. setCountryForNameCode(EGYPT_COUNTRY_CODE_ISO)
            binding.countryPicker.setCountryForNameCode(EGYPT_COUNTRY_CODE_ISO)
            binding.phoneCountryCodePicker.setCcpClickable(false)
            viewModel.setPhoneCountryCode(binding.phoneCountryCodePicker.selectedCountryCodeWithPlus)
            viewModel.setCountryCodeIso(EGYPT_COUNTRY_CODE_ISO)
            binding.countryPicker.setCcpClickable(false)
        } else {
            // For phone country code picker
            binding.phoneCountryCodePicker.apply {
                setOnCountryChangeListener {
                    viewModel.setPhoneCountryCode(selectedCountryCodeWithPlus)
                }
                registerCarrierNumberEditText(binding.textInputPhoneNumber)
                setPhoneNumberValidityChangeListener { valid ->
                    viewModel.setPhoneNumberValid(valid)
                }
                // Initialize ViewModel with current picker value
                viewModel.setPhoneCountryCode(selectedCountryCodeWithPlus)
            }

            // For country picker
            binding.countryPicker.apply {
                setCcpClickable(true)
                setOnCountryChangeListener {
                    viewModel.setCountryCodeIso(selectedCountryNameCode)
                }
                // Initialize ViewModel with current picker value
                viewModel.setCountryCodeIso(selectedCountryNameCode)
            }
        }
        // FIXME: file size not be more than 5mb
        binding.camera.setOnClickListener{
            val action = CreateHuddleFragmentDirections.actionGlobalProfileImageAttachSourceFragment(true)
            findNavController().navigateSafe(action)
        }

        binding.firstNameInfo.setOnClickListener {
            val text = getString(R.string.firstNameInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.firstNameInfo)
        }

        binding.lastNameInfo.setOnClickListener {
            val text = getString(R.string.lastNameInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.lastNameInfo)
        }

        binding.passportInfo.setOnClickListener {
            val text = getString(R.string.passportInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.passportInfo)
        }
        binding.passportImageInfo.setOnClickListener {
            val text = getString(R.string.passportImageInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.passportImageInfo)
        }

        binding.phoneNumberInfo.setOnClickListener {
            val text = getString(R.string.phoneNumberInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.phoneNumberInfo)
        }

        binding.countryInfo.setOnClickListener {
            val text = getString(R.string.countryInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.countryInfo)
        }

        binding.stateInfo.setOnClickListener {
            val text = getString(R.string.stateInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.stateInfo)
        }

        binding.streetAddressInfo.setOnClickListener {
            val text = getString(R.string.streetAddressInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.streetAddressInfo)
        }

        binding.postalCodeInfo.setOnClickListener {
            val text = getString(R.string.postalCodeInfo)
            val balloon = createTooltipBalloon(requireContext(), text)
            balloon.showAlignBottom(binding.postalCodeInfo)
        }


    }
    private fun observe() {
        viewModel.getPayoutUser()
        setFragmentResultListener(ProfileImageAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when (bundle.getString(ProfileImageAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY)) {
                ProfileImageAttachSourceFragment.SRC_GALLERY -> {
                    selectImageFromGallery()
                }
                ProfileImageAttachSourceFragment.SRC_CAMERA -> {
                    takeImage()
                }
            }
        }

        viewModel.clearAllErrors.observe(viewLifecycleOwner){
            if(it){
                viewModel.clearAllFlags()
            }
        }

        viewModel.payoutUserInfoLiveData.observe(viewLifecycleOwner){
            if(it!=null){
                // Only set phone country code if user is not Egyptian
                if (!viewModel.user.isEgyptianUser) {
                    binding.countryPicker.setCountryForNameCode(it.countryCodeIso)
                    binding.phoneCountryCodePicker.setCountryForPhoneCode(UserInfoUtil.removePlusFromCountryCode(it.countryCode)?.toInt()?:0)

                    // Update ViewModel with the loaded data
                    viewModel.setCountryCodeIso(it.countryCodeIso)
                    viewModel.setPhoneCountryCode(it.countryCode)
                }
            }
        }
        viewModel.isPhoneNumberAlreadyVerified.observe(viewLifecycleOwner){
            if(it){
                findNavController().navigateSafe(BusinessCustomerInformationFragmentDirections.actionBusinessCustomerInformationFragmentToPaymentMethodsFragment())
            }
            else{
                findNavController().navigateSafe(BusinessCustomerInformationFragmentDirections.actionBusinessCustomerInformationFragmentToPayoutOtpFragment())
            }
        }
        viewModel.errorEvent.observe(viewLifecycleOwner){
            if(it!=null){
                Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
            }
        }
    }


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = getString(R.string.business_official_information_title)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
    }
    private fun createTooltipBalloon(context: Context, text: String): Balloon {
        return createBalloon(context) {
            setHeight(BalloonSizeSpec.WRAP)
            setLayout(R.layout.layout_sell_flax_tool_tip)
            setMargin(16)
            setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
            setCornerRadius(8f)
            setBackgroundColorResource(R.color.colorAlwaysLightSurface)
            setBalloonAnimation(BalloonAnimation.ELASTIC)
            build()
        }.apply {
            getContentView().findViewById<AppCompatTextView>(R.id.message).text = text
        }
    }

    companion object {
        const val EGYPT_COUNTRY_CODE_ISO = "EG"
    }
}
