package com.app.messej.ui.home.gift

import GiftListUIModel
import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.insertSeparators
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.enums.GiftType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.GiftRepository
import kotlinx.coroutines.Dispatchers

class GiftReceivedViewModel(app:Application):AndroidViewModel(app) {
    private val giftRepository: GiftRepository = GiftRepository(app)
    private var accountRepo: AccountRepository = AccountRepository(app)
    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails


    val giftType=MutableLiveData(GiftType.PERSONAL)

    private val _currentTab = MutableLiveData<GiftType?>(GiftType.PERSONAL)
    val currentTab: LiveData<GiftType?> = _currentTab.distinctUntilChanged()

//    val giftReceived =giftType.switchMap { type->
//        Log.d("TYPEGIFTAPI", "setCurrentTab: $type")
//        giftRepository.getGiftReceivedHistoryPager(type.toString()).liveData.cachedIn(viewModelScope)
//    }

    val _giftReceived =giftType.switchMap { type->
        Log.d("TYPEGIFTAPI", "setCurrentTab: $type")
        giftRepository.getGiftPagerList(type.toString()).liveData.cachedIn(viewModelScope)
    }

    val giftReceived = _giftReceived.map { pagingData ->
        pagingData.map {
            GiftListUIModel.Gift(it)
        }
            .insertSeparators{ before:GiftListUIModel.Gift?, after:GiftListUIModel.Gift? ->
                if (before == null && after == null) {
                    // List is empty
                    null
                } else if (after == null) {
                    // End of list
                    null
                } else if (before == null) {
                    // Beginning of list, add the header
                    GiftListUIModel.CategoryHeader(after.gift.categoryName ?: "Unknown Category")
                } else if (before.gift.categoryName != after.gift.categoryName) {
                    // Different category, add the header
                    GiftListUIModel.CategoryHeader(after.gift.categoryName ?: "Unknown Category")
                } else {
                    // Same category, no separator
                    null
                }
            }
    }








    fun setCurrentTab(tab: GiftType, skipIfSet: Boolean = false) {
        Log.w("TABTRANSACTION", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.postValue( tab)
    }

}