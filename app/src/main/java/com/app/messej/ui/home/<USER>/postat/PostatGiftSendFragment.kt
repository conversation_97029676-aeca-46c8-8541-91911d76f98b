package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.View
import androidx.core.os.bundleOf
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.ui.home.gift.GiftListBottomSheetBaseFragment
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class PostatGiftSendFragment : GiftListBottomSheetBaseFragment() {
    val args: PostatGiftSendFragmentArgs by navArgs()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    companion object {
        const val GIFT_REQUEST_KEY = "postatGiftRequest"
        const val GIFT_REQUEST_PAYLOAD = "postatGiftPayload"
    }

    private fun setup() {
        viewModel.setArgs(args.receiverId)
        setUpTabData()
    }

    private fun observe() {
        viewModel.onGiftSent.observe(viewLifecycleOwner) { item ->
            requireActivity().supportFragmentManager.setFragmentResult(
                GIFT_REQUEST_KEY, bundleOf(
                    GIFT_REQUEST_PAYLOAD to item.copy(
                        receiverId = args.receiverId
                    )
                )
            )
            giftCommonViewModel.getGiftList()
            if (item.hasVideo) {
                findNavController().popBackStack()
                downloadAndShowGift(item)
            } else {
                findNavController().popBackStack()
                val action = NavGraphHomeDirections.actionGlobalNotificationLottieBottomSheetFragment(item.id, args.receiverId, false)
                val options = NavOptions.Builder().setPopUpTo(R.id.giftListFragment, inclusive = true).build()
                findNavController().navigateSafe(action, options)
            }
        }
    }

    override fun onGiftItemClick(item: GiftItem, preview: Boolean) {
        if(viewModel.isGiftLoading.value==true && preview) return
        viewModel.setGiftLoading(true)
        if (preview) {
            viewModel.sendGiftContent(item = item, receiverId = args.receiverId, sendingSource = args.singleTabItem)
        } else {
            viewModel.sendGift(
                item = item,
                receiverId = args.receiverId,
                sendingSource = args.singleTabItem,
                sendingSourceId = args.messageId,
            )
        }
    }
}
