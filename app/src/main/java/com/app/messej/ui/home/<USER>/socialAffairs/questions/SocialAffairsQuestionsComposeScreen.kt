package com.app.messej.ui.home.publictab.socialAffairs.questions

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.paging.compose.collectAsLazyPagingItems
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse.QuestionAndAnswer
import com.app.messej.data.model.enums.SocialQuestionSubmitType
import com.app.messej.ui.composeComponents.ComposeShimmerListLayout
import com.app.messej.ui.composeComponents.ComposeTextField
import com.app.messej.ui.composeComponents.CustomPaginationView
import com.app.messej.ui.home.publictab.socialAffairs.socialSupport.MySocialSupportRequestShimmerItem

@Composable
fun SocialAffairAskedQuestionsComposeScreen(
    viewModel: SocialAskedQuestionsViewModel,
    onDelete: (isQuestion:Boolean, id: Int) -> Unit
) {
    val questionsPaginatedData = viewModel.questionsList.collectAsLazyPagingItems()
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    var questionSubmitType by remember { mutableStateOf(value = SocialQuestionSubmitType.AddNewQuestion) }
    var selectedQuestion by remember { mutableStateOf<QuestionAndAnswer?>(value = null) }
    val canAskQuestion by viewModel.canAskQuestion.observeAsState(initial = false)
    val isTextBoxVisible = canAskQuestion || selectedQuestion != null
    val presidentId by viewModel.presidentUserId.collectAsState(initial = null)

    fun reset() {
        selectedQuestion = null
        questionSubmitType = SocialQuestionSubmitType.AddNewQuestion
        clearFocus(keyboardController = keyboardController, focusManager = focusManager)
        viewModel.resetTextField()
    }

    LaunchedEffect(key1 = Unit) {
        viewModel.isQuestionsNeedToBeRefresh.collect {
            if (!it) return@collect
            questionsPaginatedData.refresh()
            reset()
        }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        //The Paginated Question View
        CustomPaginationView(
            modifier = Modifier.padding(bottom = if (isTextBoxVisible) 80.dp else 0.dp),
            lazyPagingItem = questionsPaginatedData,
            emptyItemIcon = R.drawable.ic_questions_empty,
            userScrollEnabled = selectedQuestion == null,
            loadingAnimation = {
                ComposeShimmerListLayout(
                    modifier = Modifier
                        .padding(top = dimensionResource(id = R.dimen.activity_margin))
                        .padding(horizontal = dimensionResource(id = R.dimen.activity_margin)),
                    verticalSpace = dimensionResource(id = R.dimen.line_spacing)
                ) { brush ->
                    MySocialSupportRequestShimmerItem(
                        brush = brush,
                        backgroundColor = colorResource(id = R.color.colorSurface)
                    )
                }
            },
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing)),
            contentPadding = PaddingValues(all = dimensionResource(id = R.dimen.activity_margin)),
            emptyViewTitle = R.string.social_asked_questions_empty,
            lazyColumnContent = {
                items(
                    count = questionsPaginatedData.itemCount,
                    key = {
                        val item = questionsPaginatedData[it]
                        item?.id ?: 0
                    }
                ) {
                    val item = questionsPaginatedData[it] ?: return@items
                    QuestionAndAnswerSingleView(
                        item = item,
                        presidentUserId = presidentId,
                        currentUser = viewModel.user,
                        onEdit = { isQuestion ->
                            selectedQuestion = item
                            questionSubmitType = if (isQuestion) SocialQuestionSubmitType.EditQuestion else SocialQuestionSubmitType.EditAnswer
                            viewModel.setTextFieldValue(value = if (isQuestion) item.question else item.answer)
                            requestFocus(keyboardController = keyboardController, focusManager = focusManager)
                        },
                        onDelete = { isQuestion ->
                            item.id?.let {
                                onDelete(isQuestion, item.id)
                            }
                        },
                        onRespondToQuestion = {
                            requestFocus(keyboardController = keyboardController, focusManager = focusManager)
                            questionSubmitType = SocialQuestionSubmitType.AddAnswer
                            selectedQuestion = item
                        },
                    )
                }
            }
        )

        //Used for current editing question/ editing answer/ adding new answer to visible at the top of the page.
        QuestionAndAnswerAlertDialog(
            item = selectedQuestion,
            currentUser = viewModel.user
        )

        //The Text field and close icon visible at the bottom of the screen
        if (isTextBoxVisible) {
            Column(
                modifier = Modifier
                    .padding(all = dimensionResource(id = R.dimen.activity_margin))
                    .align(alignment = Alignment.BottomCenter)
                    .fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    ComposeTextField(
                        modifier = Modifier.fillMaxWidth().weight(weight = 1F),
                        state = viewModel.textFieldState,
                        focusManager = focusManager,
                        keyboardManager = keyboardController,
                        backgroundColor = colorResource(id = R.color.colorSurface),
                        placeHolderText =
                            stringResource(id = if (selectedQuestion?.canAnswerAndEditAnswer(
                                    currentUserId = viewModel.user.id, presidentUserId = presidentId) == true)
                                R.string.social_type_response_place_holder
                            else  R.string.social_ask_question_place_holder),
                        trailingIcon = {
                            SocialAskedQuestionTextFieldSendButton(
                                viewModel = viewModel,
                                questionSubmitType = questionSubmitType,
                                selectedQuestionId = selectedQuestion?.id
                            )
                        }
                    )
                    AnimatedVisibility(visible = selectedQuestion != null) {
                        IconButton(onClick = { reset() }) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_close_circle_filled_white),
                                tint = Color.Unspecified,
                                contentDescription = null
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SocialAskedQuestionTextFieldSendButton(
    viewModel: SocialAskedQuestionsViewModel,
    questionSubmitType: SocialQuestionSubmitType,
    selectedQuestionId: Int?
) {
    IconButton(
        onClick = {
            when(questionSubmitType) {
                SocialQuestionSubmitType.AddNewQuestion -> viewModel.submitQuestion()
                SocialQuestionSubmitType.EditQuestion -> viewModel.editQuestion(questionId = selectedQuestionId)
                SocialQuestionSubmitType.AddAnswer -> viewModel.addAnswer(questionId = selectedQuestionId)
                SocialQuestionSubmitType.EditAnswer -> viewModel.addAnswer(questionId = selectedQuestionId)
            }
        },
        enabled = !viewModel.textFieldState.text.isNullOrEmpty()
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_chat_send),
            tint = colorResource(id = R.color.colorPrimary).copy(alpha = if (!viewModel.textFieldState.text.isNullOrEmpty()) 1F else 0.3F),
            contentDescription = null
        )
    }
}

private fun requestFocus(
    keyboardController: SoftwareKeyboardController?,
    focusManager: FocusManager
) {
    keyboardController?.show()
    focusManager.moveFocus(focusDirection = FocusDirection.Up)
}

private fun clearFocus(
    keyboardController: SoftwareKeyboardController?,
    focusManager: FocusManager
) {
    keyboardController?.hide()
    focusManager.clearFocus()
}