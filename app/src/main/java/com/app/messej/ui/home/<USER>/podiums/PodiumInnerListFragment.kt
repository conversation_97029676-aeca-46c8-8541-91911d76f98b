package com.app.messej.ui.home.publictab.podiums

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.PodiumTab
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.databinding.FragmentPodiumInnerListBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.home.publictab.podiums.PodiumUtil.validateAndConfirmJoin
import com.app.messej.ui.utils.BindingExtensions.prepare
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.app.messej.ui.utils.FragmentExtensions.setAsDestructiveMenuItem
import com.app.messej.ui.utils.FragmentExtensions.setAsDestructiveMenuItemParams
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.ViewUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.kennyc.view.MultiStateView


class PodiumInnerListFragment : Fragment() {

    private lateinit var binding: FragmentPodiumInnerListBinding

    private var mAdapter: PodiumAdapter? = null

    private val viewModel: PublicPodiumViewModel by activityViewModels()


    protected lateinit var tab: PodiumTab

    companion object {
        const val ARG_TAB = "tab"

        fun getTabBundle(tab: PodiumTab) = Bundle().apply {
            putInt(ARG_TAB, tab.ordinal)
        }

        fun parseTabBundle(bundle: Bundle?): PodiumTab {
            val tabInt = bundle?.getInt(ARG_TAB) ?: 0
            return PodiumTab.entries[tabInt]
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_inner_list, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tab = parseTabBundle(arguments)
        Log.d("PILF", "onViewCreated: $tab")
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
//        Log.w("PILF", "onResume: adapter size: ${mAdapter?.itemCount}", )
//        if ((mAdapter?.itemCount ?: 0) > 0) {
        Log.w("PILF", "onResume: refreshing adapter")
        mAdapter?.refresh()
//        }
    }

    private fun setup() {
        setEmptyView()
        initAdapter()
        binding.swipeRefresh.apply {
            setOnRefreshListener {
                mAdapter?.refresh()
            }
        }
    }

    private fun observe() {
        Log.d("PHILF", "observe: loading $tab")
        when (tab) {
            PodiumTab.MY_PODIUM -> viewModel.myPodiumsList
            PodiumTab.LIVE_PODIUM -> viewModel.livePodiumsList
            PodiumTab.LIVE_FRIENDS -> null
        }?.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let { mAdapter?.submitData(viewLifecycleOwner.lifecycle, it) }
        }

        viewModel.podiumInvitationDeclined.observe(viewLifecycleOwner) {
            if (it && tab == PodiumTab.LIVE_PODIUM) {
                mAdapter?.refresh()
            }
        }

        viewModel.podiumRemoval.observe(viewLifecycleOwner) {
            if (it && tab == PodiumTab.LIVE_PODIUM) {
                mAdapter?.refresh()
            }
        }

        viewModel.podiumCloseReason.observe(viewLifecycleOwner) {
            MaterialAlertDialogBuilder(requireContext()).setMessage(it).setCancelable(false).setPositiveButton(getString(R.string.common_cancel)) { dialog, _ ->
                dialog.dismiss()
            }.show()
        }
    }

    private fun setEmptyView() {
        val emptyView: View = binding.multiStateView.getView(MultiStateView.ViewState.EMPTY) ?: return
        val emptyViewBinding = LayoutListStateEmptyBinding.bind(emptyView)
        when (tab) {
            PodiumTab.MY_PODIUM -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_podium, message = R.string.podium_empty_mine, action = R.string.podium_create_prompt
                ) {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePodiumFragment())
                }
            }

            PodiumTab.LIVE_PODIUM -> {
                emptyViewBinding.prepare(
                    image = R.drawable.im_eds_podium, message = R.string.podium_empty_live
                )
            }

            PodiumTab.LIVE_FRIENDS -> {}
        }
    }

    private fun initAdapter() {

        val isFromMyPodium = tab == PodiumTab.MY_PODIUM

        mAdapter = PodiumAdapter(tab, object : PodiumAdapter.PodiumActionListener {
            override fun onPodiumClicked(pod: Podium) {
                if (pod.shouldGoLive) {
                    ensurePodiumCreateAllowed {
                        joinPodium(pod)
                    }
                    return
                }
                joinPodium(pod)
            }

            override fun onEdit(pod: Podium) {
                ensurePodiumCreateAllowed {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCreatePodiumFragment(pod.id))
                }
            }

            override fun onAboutClicked(pod: Podium) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavAboutPodium(pod.id, pod.role?.isElevated!!))
            }

            override fun onPodiumMenuClicked(pod: Podium, view: View) {

                /*info*/
                val isElevatedRole = pod.role?.isElevated == true
                val isPremiumParticipant = viewModel.user.premium && (pod.isAudience || pod.isInvited)
                val isAdmin = pod.role == Podium.PodiumUserRole.ADMIN
                val isAdminAmbassadorOrMinister = (isAdmin && viewModel.isAmbassadorOrMinister)
                val isUserCitizenAndAdmin = (viewModel.user.citizenship == UserCitizenship.CITIZEN && pod.isAdmin)


                val popup = PopupMenu(requireContext(), view)
                popup.inflate(R.menu.menu_podium_actions)
                popup.menu.apply {
                    findItem(R.id.action_end_podium)?.setAsDestructiveMenuItem(requireContext())
                    findItem(R.id.action_info).isVisible = (isElevatedRole || pod.isInvited || isPremiumParticipant || !viewModel.user.premium || pod.isInvitee) && isFromMyPodium
                    findItem(R.id.action_podium_info).isVisible = !isFromMyPodium
                    findItem(R.id.action_join).isVisible = !isFromMyPodium && !pod.isInvited
                    findItem(R.id.action_live_list).isVisible =
                        pod.isLive && (isElevatedRole || viewModel.isAmbassadorOrMinister || isPremiumParticipant || pod.isPrivate) && pod.hideLiveUsers == false
                    findItem(R.id.action_admin).isVisible = pod.isManager || isAdminAmbassadorOrMinister
//                    findItem(R.id.action_remove).isVisible = ((pod.isLive && viewModel.user.premiumUser) && (isAdmin || pod.isInvited) || pod.isInvited || isUserCitizenAndAdmin||isAdminAmbassadorOrMinister || (pod.isInvited && !viewModel.user.premium)) // replaced with you are member
                    findItem(R.id.action_end_podium).isVisible = (viewModel.user.userEmpowerment?.allowEndPodium == true || viewModel.iAmMinister) && !isFromMyPodium
//                    findItem(R.id.action_withdraw_from_admin).isVisible=pod.isAdmin
                    findItem(R.id.you_are_member)?.setAsDestructiveMenuItemParams(requireContext(), getString(R.string.podium_you_are_member), false)
                    findItem(R.id.join_as_member)?.setAsDestructiveMenuItemParams(requireContext(), getString(R.string.podium_join_as_member), true)
                    findItem(R.id.you_are_member).isVisible =
                        ((pod.isLive && viewModel.user.premiumUser) && (isAdmin || pod.isInvited) || pod.isInvited || isUserCitizenAndAdmin || isAdminAmbassadorOrMinister || (pod.isInvited && !viewModel.user.premium) || (pod.isPrivate && pod.isInvited))
                    findItem(R.id.join_as_member).isVisible = (isElevatedRole || isPremiumParticipant || !viewModel.user.premium || pod.isInvitee) && pod.isLive && pod.isInvited
                    findItem(R.id.action_yalla_guys).isVisible = pod.kind == PodiumKind.LECTURE && pod.isLive && tab == PodiumTab.LIVE_PODIUM && pod.allowYallaGuys == true && !pod.isPrivate
                }
                popup.setOnMenuItemClickListener { item ->
                    when (item?.itemId) {
                        R.id.action_podium_info -> findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavAboutPodium(pod.id, pod.role?.isElevated!!))
                        R.id.action_join -> joinPodium(pod)
                        R.id.action_info -> findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavAboutPodium(pod.id, pod.role?.isElevated!!, true))
                        R.id.action_live_list -> findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicLiveUsersListBottomSheet(podiumId = pod.id))
                        R.id.action_admin -> {
                            Log.d("CITIZENCURRENT", "CURENT user citizen: " + viewModel.user.citizenship)
                            val actionKebab = viewModel.user.id != pod.managerId
                            findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPublicAdminBottomSheet(podiumId = pod.id, actionKebab))
                        }

                        R.id.action_end_podium -> ensurePodiumCreateAllowed { confirmEndPodium(pod) }
                        R.id.join_as_member -> joinPodium(pod)
                        R.id.you_are_member -> confirmDeclineInvitation(pod)
//                        R.id.action_withdraw_from_admin ->viewModel.withDrawAsAdmin(viewModel.user.id, pod.id)
                        R.id.action_yalla_guys -> {
                            ensurePodiumCreateAllowed {
                                val joiningFee = if (pod.joiningFeePaid) 0 else pod.joiningFee ?: 0
                                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalYallaGuysCreateFragment(podiumId = pod.id, fromLive = false, joiningFee = joiningFee,podiumRating = pod.requiredUserRating ?: 0,isAdmin = isAdmin))
                            }
                        }
                    }
                    return@setOnMenuItemClickListener true
                }
                popup.show()
            }

            override fun onPodiumDpClicked(pod: Podium) {
                if (pod.isInvited) {
                    confirmInvitation(pod)
                }
            }


        })

        val layoutMan = LinearLayoutManager(context)

        binding.podiumList.apply {
            layoutManager = layoutMan
            setHasFixedSize(true)
            adapter = mAdapter
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = ViewUtils.getViewState(loadState, itemCount)
                if (loadState.refresh !is LoadState.Loading) {
                    binding.swipeRefresh.isRefreshing = false
                }
                if (viewModel.currentTab.value == tab) {
                    viewModel.dataLoadingMore.postValue(loadState.append is LoadState.Loading)
                }
            }
            registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
                override fun onItemRangeMoved(fromPosition: Int, toPosition: Int, itemCount: Int) {
                    super.onItemRangeMoved(fromPosition, toPosition, itemCount)
                    if ((fromPosition == 0 || toPosition == 0) && layoutMan.findFirstCompletelyVisibleItemPosition() == 0) {
                        layoutMan.scrollToPosition(0)
                    }
                }
            })
        }
    }

    private fun joinPodium(pod: Podium) {
            Log.w("PILF", "joinPodium: $pod")
            if (pod.kind == PodiumKind.MAIDAN && pod.canChallenge) {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPodiumMaidanChallengeBottomSheetFragment(podiumId = pod.id, fromPodium = false))
                return
            }
            val enableScroll = if (tab == PodiumTab.LIVE_PODIUM) tab.ordinal else -1
            if (pod.isLive) {
                validateAndConfirmJoin(pod, viewModel.user) {
                    Log.d("PLF", "joinPodium: ${pod.id}")

                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(pod.id, pod.kind?.ordinal ?: -1, enableScrollForTab = enableScroll))
                }
            } else if (pod.shouldGoLive) {
                confirmGoLive { findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(pod.id, pod.kind?.ordinal ?: -1, enableScrollForTab = enableScroll)) }
            }
    }

    private fun confirmGoLive(confirm: () -> Unit) {
        confirmAction(
            title = R.string.podium_action_go_live_confirm_title,
            message = R.string.podium_action_go_live_confirm_message,
            positiveTitle = R.string.common_confirm,
            negativeTitle = R.string.common_cancel
        ) {
            confirm.invoke()
        }
    }

    //confirm end podium
    private fun confirmEndPodium(pod: Podium) {
        confirmAction(
            title = R.string.podium_action_close_confirm_title, message = R.string.podium_action_close_confirm_message, positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
        ) {
            viewModel.closePodium(pod.id)
        }
    }

    //decline go live
    private fun confirmDeclineInvitation(pod: Podium) {
        if (pod.isInvited) {
            confirmAction(
                title = null, message = getString(R.string.podium_decline_invitation), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
            ) {
                viewModel.declinePodiumInvitation(podiumId = pod.id)
            }
        } else if (pod.shouldGoLive && pod.role == Podium.PodiumUserRole.ADMIN) {
            confirmAction(
                title = null, message = getString(R.string.podium_dismiss_admin), positiveTitle = R.string.common_confirm, negativeTitle = R.string.common_cancel
            ) {
                viewModel.withDrawAsAdmin(viewModel.user.id, pod.id)
            }
        }
    }

    private fun confirmInvitation(pod: Podium) {
        confirmAction(
            title = null, message = getString(R.string.podium_invitation_accept), positiveTitle = R.string.common_accept, negativeTitle = R.string.common_decline, onCancel = {
                viewModel.declinePodiumInvitation(podiumId = pod.id)
            }) {
            joinPodium(pod)
        }
    }

}