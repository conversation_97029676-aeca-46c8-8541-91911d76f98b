package com.app.messej.ui.enforcements

import androidx.core.view.isVisible
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.api.profile.UserEnforcements
import com.app.messej.databinding.LayoutUserBannedPopupBinding
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

object EnforcementExtensions {

    /**
     * Validates whether the user has access to interact with the app
     * The appropriate popup will be shown if the user is either banned or blacklisted
     * will call [onAllowed] if the user is not banned or blacklisted
     */
    fun Fragment.ensureInteractionAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()

        if (evm.userBanned || evm.userIsPendingBlackListed) {
            showEnforcementDialog(
                text = getString(R.string.report_user_banned_message),
                fineButton = {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_BANNED))
                }
            )
        } else if (evm.userBlacklisted) {
            showToast("User is blacklisted")
            // Ideally should not reach here as blacklisted user shouldn't even be allowed to login
        }
        else if (evm.userIsSuspectedToBan) {
            showEnforcementDialog(
                text = getString(R.string.report_user_banned_message),
                detailsButton = {
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalCaseDetailBottomSheet(evm.enforcements.enforcementsMeta.suspectedBanReportId?: return@showEnforcementDialog))
                }
            )
        } else {
            onAllowed.invoke()
        }
    }

    fun Fragment.ensureReportBanAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disableBanning) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_DISABLE_BANNING))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    fun Fragment.ensureReportContentAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disableReportingContent) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_DISABLE_REPORTING_CONTENT))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    fun Fragment.ensureReportUserAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disableReportingUser) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_DISABLE_REPORTING_USER))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    fun Fragment.ensureFlashPostingAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disableFlashPost) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_DISABLE_FLASH_POST))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    fun Fragment.ensureHuddlePostingAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disableHuddlePost) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_DISABLE_HUDDLE_POST))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    fun Fragment.ensurePodiumCreateAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disableCreatePodium) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.DISABLE_CREATE_PODIUM))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    fun Fragment.ensurePostatPostingAllowed(onAllowed: () -> Unit) {
        val evm: EnforcementsViewModel by activityViewModels()
        ensureInteractionAllowed {
            if (evm.enforcements.enforcementsStatus.disablePostatPost) {
                showEnforcementDialog(
                    text = getString(R.string.report_feature_banned_message),
                    fineButton = {
                        findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalPayFineFragment(fineCategory = UserEnforcements.ENF_KEY_DISABLE_POSTAT_POST))
                    }
                )
            } else {
                onAllowed.invoke()
            }
        }
    }

    private fun Fragment.showEnforcementDialog(
        text: String,
        fineButton: ((MaterialDialog) -> Unit)? = null,
        detailsButton: ((MaterialDialog) -> Unit)? = null
    ): MaterialDialog {
        val loader = MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutUserBannedPopupBinding>(layoutInflater, R.layout.layout_user_banned_popup, null, false)
            customView(null, view.root, dialogWrapContent = true, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.message.text = text
            view.payButton.isVisible = if (fineButton!=null) {
                view.payButton.setOnClickListener {
                    dismiss()
                    fineButton.invoke(this)
                }
                true
            } else false
            view.detailsButton.isVisible = if (detailsButton!=null) {
                view.detailsButton.setOnClickListener {
                    dismiss()
                    detailsButton.invoke(this)
                }
                true
            } else false
        }
        loader.show(lifecycle)
        return loader
    }

    fun MaterialDialog.show(lifecycle: Lifecycle) {
        lifecycle.apply {
            addObserver(object: LifecycleEventObserver {
                override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
                    if (!event.targetState.isAtLeast(Lifecycle.State.INITIALIZED)) {
                        if (isShowing) dismiss()
                    }
                }
            })
        }
        show()
    }
}