package com.app.messej.ui.home.gift

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.GiftConversion
import com.app.messej.databinding.FragmentGiftLatestBinding
import com.app.messej.ui.home.gift.composeScreen.GiftFileComposeScreen
import com.app.messej.ui.home.gift.composeScreen.onGiftFileItemListener
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class GiftFragment : Fragment(),MenuProvider {

    private lateinit var binding: FragmentGiftLatestBinding

    private lateinit var giftHeaderHolder: GiftHeaderHolder

    private val giftCommonViewModel: GiftCommonViewModel by activityViewModels()

    private val args: GiftFragmentArgs by navArgs()

    private var mGiftPagerAdapter: FragmentStateAdapter? = null


    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolbar.title = getString(R.string.bottom_sheet_gift)

        binding.customActionBar.toolbar.apply {
            setNavigationOnClickListener {
//                if(giftCommonViewModel.processWait.value == true){
//                    showToast(R.string.business_process_not_complete)
//                    return@setNavigationOnClickListener}
//                else
                findNavController().popBackStack()
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_gift_latest, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = giftCommonViewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        addAsMenuHost()
        setup()
//        setupNavigation()
//        observe()
    }

    fun setup(){
        giftCommonViewModel.getGiftList()
        binding.giftFile.setContent {
            GiftFileComposeScreen(viewModel = giftCommonViewModel,listener = object : onGiftFileItemListener {
                override fun onDonateAction() {
                    showFlashatDialog {
                        setMessage(resources.getString(R.string.gift_file_donate_action))
                        setCloseButton(title = R.string.common_close, icon = R.drawable.ic_close) {
                            true
                        }
                        setConfirmButtonVisible(false)
                    }
                }

                override fun onBuyCoinAction() {
                    val action = GiftFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = true, hideActionBar = true)
                    findNavController().navigateSafe(action)
                }

                override fun onBuyFlixAction() {
                    val action = GiftFragmentDirections.actionGlobalBuyflaxFragment(isBuyCoin = false, hideActionBar = true)
                    findNavController().navigateSafe(action)
                }

                override fun onConvertAction() {
                    val action = GiftFragmentDirections.actionGlobalConvertCoinToFlaxFragment(GiftConversion.FLAX_TO_COIN)
                    findNavController().navigateSafe(action)
                }

            })
        }
    }

//    private fun setupNavigation() {
//        giftHeaderHolder = if (giftCommonViewModel.isPremiumUser == true) {
//            GiftHeaderHolder.PremiumHeader(
//                DataBindingUtil.inflate(layoutInflater, R.layout.layout_gift_listing_header_premium, binding.headerHolder, false),
//                object : GiftHeaderHolder.PremiumHeader.ClickListener {
//                    override fun convertCoinToFlax() {
//                        findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToPointsToConvertFragment(GiftConversion.COIN_TO_FLAX))
//                    }
//
//                    override fun convertFlaxToCoin() {
//                        findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToPointsToConvertFragment(GiftConversion.FLAX_TO_COIN))
//
//                    }
//
//                })
//        }else if (giftCommonViewModel.isResidentUser == true ||giftCommonViewModel.isVisitor == true){
//            GiftHeaderHolder.ResidentHeader(
//                DataBindingUtil.inflate(layoutInflater, R.layout.layout_gift_listing_header_resident, binding.headerHolder, false),
//                object : GiftHeaderHolder.ResidentHeader.ClickListener {
//                    override fun convertCoinToFlax() {
//                        findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToPointsToConvertFragment(GiftConversion.COIN_TO_FLAX))
//                    }
//
//                    override fun convertFlaxToCoin() {
//                        findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToPointsToConvertFragment(GiftConversion.FLAX_TO_COIN))
//
//                    }
//
//                })
//        } else {
//
//            GiftHeaderHolder.FreeHeader(
//                DataBindingUtil.inflate(layoutInflater, R.layout.layout_gift_listing_header_free, binding.headerHolder, false)
//            )
//        }
//        binding.headerHolder.addView(giftHeaderHolder.binding.root)
//    }
//
//    private fun observe() {
//
//
//        giftCommonViewModel.currentTab.observe(viewLifecycleOwner) {
//            it ?: return@observe
//            Log.w("PHBF", "observe currentTab: $it")
//            if (binding.giftPager.currentItem == it.ordinal) return@observe
//            binding.giftPager.setCurrentItem(it.ordinal, false)
//        }
//
//        giftCommonViewModel.giftList.observe(viewLifecycleOwner) {
//            giftHeaderHolder.giftList = it
//        }
//
//        setFragmentResultListener(FlaxToPointsConvertDialogFragment.PURCHASE_SUCCESS) { requestKey, bundle ->
//            if (requestKey == FlaxToPointsConvertDialogFragment.PURCHASE_SUCCESS) {
//                giftCommonViewModel.getGiftList()
//                Toast.makeText(requireContext(), resources.getString(R.string.flax_to_point_conversion_succes), Toast.LENGTH_SHORT).show()
//            }
//            if (requestKey == FlaxToPointsConvertDialogFragment.PURCHASE_ERROR) {
//                Toast.makeText(requireContext(), resources.getString(R.string.flax_to_point_conversion_error), Toast.LENGTH_SHORT).show()
//            }
//        }
//    }
//
//    private fun setup() {
//
//        lifecycleScope.launch {
//            val tab = GiftTabs.entries.toTypedArray().getOrElse(args.tab) { GiftTabs.GIFT_COINS }
//            delay(200)
//            giftCommonViewModel.setCurrentTab(tab, true)
//        }
//        giftCommonViewModel.getGiftList()
//   /*     val isPremiumUser: Boolean = giftCommonViewModel.isPremiumUser == true
//        val isResidentUser: Boolean = giftCommonViewModel.isResidentUser == true*/
//
//        mGiftPagerAdapter = object : FragmentStateAdapter(this) {
//            override fun getItemCount(): Int =  3 /*if (isPremiumUser||isResidentUser) 3 else 1*/
//
//            override fun createFragment(position: Int): Fragment {
//                return when (position) {
//                    0 -> ReceivedGiftsFragment()
//                    1 -> BuyCoinsFragment().apply {
//                        arguments = BuyCoinsFragment.getActionBundle(true,true)
//                    }
//                    2 -> BuyCoinsFragment().apply {
//                        arguments = BuyCoinsFragment.getActionBundle(true,false)
//                    }
//                    else -> throw IllegalArgumentException("Invalid position")
//                }
//            }
//        }
//
//        binding.giftPager.apply {
//            adapter = mGiftPagerAdapter
//            isUserInputEnabled = true
//        }
//
//        TabLayoutMediator(binding.giftTab, binding.giftPager) { tab, position ->
//            when (position) {
//                0 -> tab.text = resources.getString(R.string.recieved_gift_title)
//                1 -> tab.text = resources.getString(R.string.title_buy_coins)
//                2 -> tab.text = resources.getString(R.string.title_buy_flix)
//                else -> throw IllegalArgumentException("Invalid position")
//
//            }
//        }.attach()
//
//
//    }
//
//    override fun onDestroyView() {
//        super.onDestroyView()
//        // Nullify binding to avoid memory leaks
//        binding.headerHolder.removeAllViews()
//        giftHeaderHolder.binding.root.parent?.let {
//            (it as ViewGroup).removeView(giftHeaderHolder.binding.root)
//        }
//        binding.giftPager.adapter = null  // Avoid memory leaks
//        mGiftPagerAdapter = null
//        binding.unbind()
//    }
//
//
    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.menu_gift_info,menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.action_gift_info -> {
                findNavController().navigateSafe(GiftFragmentDirections.actionGiftFragmentToGiftInfoBottomSheet())

            }
            else -> return false
        }
        return true
    }


}