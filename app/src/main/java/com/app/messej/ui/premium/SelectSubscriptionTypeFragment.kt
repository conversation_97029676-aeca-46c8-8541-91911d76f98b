package com.app.messej.ui.premium

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClient.BillingResponseCode
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingFlowParams.ProductDetailsParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.app.messej.BuildConfig
import com.app.messej.R
import com.app.messej.data.model.SubscriptionProfile
import com.app.messej.data.utils.AuthUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentSelectSubscriptionTypeBinding
import com.app.messej.databinding.LayoutAlreadySubscribedBinding
import com.app.messej.databinding.LayoutPodiumLiveFriendsActionBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.Executors

class SelectSubscriptionTypeFragment : BottomSheetDialogFragment()  {

    private var subProductDetails: ProductDetails?=null
    private val viewModel: UpgradePremiumViewModel by activityViewModels()
    private lateinit var binding: FragmentSelectSubscriptionTypeBinding
    private lateinit var billingClient: BillingClient
    var isSuccess=false

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_select_subscription_type, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        getSubscriptionDetails()
        viewModel.flixSubscriptionDetails()
    }

    private fun setup() {
        billingClient = BillingClient.newBuilder(requireContext()).setListener(purchasesUpdatedListener).enablePendingPurchases().build()
        viewModel.selectDates()
        viewModel.handleVisibility(false)
        binding.purchaseButton.setOnClickListener {
            billingClient.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(billingResult: BillingResult) {
                    if (billingResult.responseCode == BillingResponseCode.OK) {
                        getProductDetails()
                    }
                }

                override fun onBillingServiceDisconnected() {
                    // Try to restart the connection on the next request to
                    // Google Play by calling the startConnection() method.
                }
            })
        }

        binding.purchaseFlixButton.setOnClickListener {
            viewModel.flixSubscriptionDetails.value?.let { it ->
                if (it.subscriptionMethod == "FLiX" || it.subscriptionMethod.isNullOrEmpty()|| it.subscription_acive==false|| !viewModel.user.premium) {
                    viewModel.handleVisibility(true)
                } else {
                    showAlreadyPurchasedAlert()
                }
            }

        }

        binding.purchaseConfirmButton.setOnClickListener{
            if(viewModel.disableFlixButton.value==false && viewModel.user.premium) return@setOnClickListener
            viewModel.subscribeByFlix()
        }
    }

    private fun showAlreadyPurchasedAlert() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumLiveFriendsActionBinding>(layoutInflater, R.layout.layout_podium_live_friends_action, null, false)
            view.textHeader = getString(R.string.Already_sub_flix_note)
            view.actionFriendUnfollow.visibility = View.GONE
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

    private fun observe() {
        viewModel.alreadySubscribed.observe(viewLifecycleOwner){subscribed->
            subscribed?.let{
                if(!it) {
                    val queryProductDetailsParams = QueryProductDetailsParams.newBuilder().setProductList(
                        listOf(
                            QueryProductDetailsParams.Product.newBuilder().setProductId(SUBSCRIPTION_ID).setProductType(BillingClient.ProductType.SUBS).build()
                        )
                    ).build()

                    val user = viewModel.user
                    billingClient.queryProductDetailsAsync(queryProductDetailsParams) { billingResult, productDetailsList ->
                        for (productDetails in productDetailsList) {
                            if (productDetails.productId == SUBSCRIPTION_ID) {
                                subProductDetails = productDetails
                                val offerToken = productDetails.subscriptionOfferDetails?.get(0)?.offerToken
                                val productDetailsParamsList = listOf(
                                    ProductDetailsParams.newBuilder().setProductDetails(productDetails).setOfferToken(offerToken!!).build()
                                )
                                val userJsonEncoded = AuthUtil.encodeToBase64(
                                    Gson().toJson(
                                        SubscriptionProfile(
                                            userId = user.id,
                                            timezone = DateTimeUtils.getTimeZoneFromEpochTime(System.currentTimeMillis()),
                                            paymentFor = 1
                                        )
                                    )
                                )
                                val billingFlowParams = BillingFlowParams.newBuilder().setProductDetailsParamsList(productDetailsParamsList).setObfuscatedAccountId(BuildConfig.BUILD_TYPE)
                                    .setObfuscatedProfileId(userJsonEncoded).build()
                                billingClient.launchBillingFlow(requireActivity(), billingFlowParams)
                                viewModel.productDetails.postValue(productDetails)
                            }
                        }
                    }
                }else{
                    val binding = LayoutAlreadySubscribedBinding.inflate(LayoutInflater.from(requireContext()))
                    val customView = binding.root
                    val materialAlertDialog = MaterialAlertDialogBuilder(requireContext()).setView(customView).setCancelable(true).create()
                    materialAlertDialog.show()
                }
            }

        }

        viewModel.subscriptionError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.subscription.observe(viewLifecycleOwner) {
            it?.let {
                if(it){
                    Toast.makeText(requireContext(), getString(R.string.title_subscription_sucess), Toast.LENGTH_SHORT).show()
                    findNavController().navigateSafe(UpgradePremiumFragmentDirections.actionUpgradePremiumFragmentToAlreadySubscribedFragment(true))
                }
            }
        }

        viewModel.isSubscribedByFlix.observe(viewLifecycleOwner){
            if(it==true){
                Toast.makeText(requireContext(), getString(R.string.title_subscription_sucess), Toast.LENGTH_SHORT).show()
                findNavController().navigateSafe(SelectSubscriptionTypeFragmentDirections.actionSelectSubscribedTypeFragmentToAlreadySubscribedFragment(true))

            }
        }
        viewModel.disableFlixButton.observe(viewLifecycleOwner){
            Log.d("Disable",""+it)
        }
    }

    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult, purchases ->
            when (billingResult.responseCode) {

                BillingResponseCode.OK -> {
                    for (purchase in purchases!!) {
                        handlePurchase(purchase)
                    }
                }

                BillingResponseCode.ITEM_ALREADY_OWNED->{
                    isSuccess=true
                    requireActivity().runOnUiThread{
                    }

                }
                BillingResponseCode.USER_CANCELED -> {
                    requireActivity().runOnUiThread {
                    }
                }
                else -> {
                    // Handle any other error codes.
                    requireActivity().runOnUiThread {
                        Toast.makeText(requireActivity(), billingResult.debugMessage, Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }

    private fun getProductDetails() {
        billingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS).build()) { result, purchseList ->
            if (purchseList.isEmpty()) {
                viewModel.setAlreadySubscribed(false)
            } else {
                purchseList.map {purchase->
                    purchase.products.map {product->
                        if(product.equals(SUBSCRIPTION_ID)){
                            viewModel.setAlreadySubscribed(true)
                        }else{
                            viewModel.setAlreadySubscribed(false)
                        }
                    }
                }
            }
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            if (!purchase.isAcknowledged) {
                val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                    .setPurchaseToken(purchase.purchaseToken)
                lifecycleScope.launch {
                    withContext(Dispatchers.IO) {
                        billingClient.acknowledgePurchase(acknowledgePurchaseParams.build()) {
                            if(it.responseCode==BillingResponseCode.OK){
                                viewModel.setPurchase(purchase)
                            }
                        }
                    }
                }
            }
        }

    }


    private fun getSubscriptionDetails(){
        billingClient.startConnection(object :BillingClientStateListener{

            override fun onBillingServiceDisconnected() {
                requireActivity().runOnUiThread{
                    Toast.makeText(requireContext(),"Disconnected",Toast.LENGTH_SHORT).show()
                }
            }
            override fun onBillingSetupFinished(result: BillingResult) {
                val executerService= Executors.newSingleThreadExecutor()
                executerService.execute {
                    val productList= listOf(QueryProductDetailsParams.Product.newBuilder().setProductId(SUBSCRIPTION_ID).setProductType(BillingClient.ProductType.SUBS).build())
                    val params=QueryProductDetailsParams.newBuilder().setProductList(productList)
                    billingClient.queryProductDetailsAsync(params.build()) { billingResult, productDetailsList ->
                        for (productDetails in productDetailsList) {
                            if (productDetails.productId == SUBSCRIPTION_ID) {
                                productDetails.subscriptionOfferDetails?.get(0)?.pricingPhases?.pricingPhaseList?.get(0)?.let { viewModel.setPricingPhase(it) }
                            }
                        }
                    }
                }
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        billingClient.endConnection()
    }
    companion object
    {
        const val SUBSCRIPTION_ID = "flashat.premium.autorenew"
        const val SUBSCRIPTION_KEY = "subscription"
    }


    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }
}