package com.app.messej.ui.home.publictab.flash.create

import android.app.Application
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.util.Log
import androidx.compose.ui.geometry.Offset
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.MainApplication
import com.app.messej.data.model.Category
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.EditableFlashMedia
import com.app.messej.data.model.ShareTo
import com.app.messej.data.model.TempFlashMedia
import com.app.messej.data.model.VideoEditInfo
import com.app.messej.data.model.entity.FlashVideoWithMedia
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.repository.worker.FlashUploadWorker
import com.app.messej.data.utils.EnumUtil.next
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.roundToLong

class FlashRecordViewModel(application: Application): AndroidViewModel(application) {

    private val accountRepo = AccountRepository(getApplication())
    private val flashRepo = FlashRepository(application)

    companion object {
        const val MIN_DURATION_MS = 1000
    }

    val user: CurrentUser get() = accountRepo.user

    private val _isFrontCamera = MutableLiveData(false)
    val isFrontCamera: LiveData<Boolean> = _isFrontCamera

    private val _isProcessing = MutableLiveData(false)
    val isProcessing: LiveData<Boolean> = _isProcessing

    val videoFileIsCorrupted = LiveEvent<Boolean>()

    fun toggleCamera() {
        _isFrontCamera.value = _isFrontCamera.value == false
    }

    enum class FlashTorchMode {
        //        AUTO,
        OFF,
        ON,
    }

    private val _torchMode = MutableLiveData(FlashTorchMode.OFF)
    val torchMode: LiveData<FlashTorchMode> = _torchMode

    fun toggleFlash() {
        _torchMode.value = _torchMode.value?.next() ?: FlashTorchMode.OFF
    }

    enum class FlashRecordTimer {
        SECONDS_3,
        SECONDS_5,
        SECONDS_10,
    }

    private val _recordTimer = MutableLiveData<FlashRecordTimer?>(null)
    val recordTimer: LiveData<FlashRecordTimer?> = _recordTimer

    val onTimerSet = LiveEvent<FlashRecordTimer?>()

    private val _tempSelectedTimer = MutableLiveData<FlashRecordTimer?>(null)
    val tempSelectedTimer: LiveData<FlashRecordTimer?> = _tempSelectedTimer

    fun initTimerSelection() {
        _tempSelectedTimer.value = _recordTimer.value
    }
    fun selectTimer(time: FlashRecordTimer) {
        _tempSelectedTimer.value = if (time == _tempSelectedTimer.value) null else time
    }

    fun setTimer() {
        _recordTimer.postValue(_tempSelectedTimer.value)
        onTimerSet.postValue(_tempSelectedTimer.value)
    }

    // Using this record limit from the new api.
    // Replaced old usage.
    private val _recordingLimit = MutableLiveData<Int?>()
    val recordingLimit :LiveData<Int?> = _recordingLimit

    fun setRecordLimit(limit: Int?) {
        _recordingLimit.postValue(limit ?: 0)
    }

    // Recording
//    val recordingLimit: LiveData<Int> = accountRepo.getAccountDetailsFlow().map {
//        it?.flashDurationLimit?:30
//    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val allowGalleryUpload: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>(false)
        fun update() {
            med.postValue(user.premium && isRecording.value != true && _flashRecording.value == null)
        }
        med.addSource(isRecording) { update() }
        med.addSource(_flashRecording) { update() }
        med
    }

    private val _tempRecording = MutableLiveData<TempFlashMedia?>(null)

    private fun clearTempRecording(deleteFiles: Boolean = true) {
        viewModelScope.launch(Dispatchers.IO) {
            _tempRecording.value?.let {
                if (deleteFiles) flashRepo.deleteFile(it.file)
                _tempRecording.postValue(null)
            }
        }
    }

    private var recordTimerJob: Job? = null

    val recordTimerBeat = LiveEvent<Int>()

    private suspend fun startRecordingTimer(seconds: FlashRecordTimer?, onComplete: () -> Unit) {
        Log.w("FRVM", "startRecordingTimer: $seconds")
        stopRecordingTimer()
        var sec = when (seconds) {
            FlashRecordTimer.SECONDS_3 -> 3
            FlashRecordTimer.SECONDS_5 -> 5
            FlashRecordTimer.SECONDS_10 -> 10
            null -> {
                onComplete.invoke()
                return
            }
        }
        recordTimerJob = viewModelScope.launch {
            try {
                while (sec > 0) {
                    delay(1000)
                    recordTimerBeat.postValue(sec--)
                }
                Log.w("FRVM", "startRecordingTimer: done")
                onComplete.invoke()
                stopRecordingTimer()
            }
            catch (e: Exception) {
                Log.e("FRVM", "startRecordingTimer: failed", e)
            } finally {
                Log.w("FRVM", "recording timer cancelled")
            }
        }
    }

    private fun stopRecordingTimer() {
        recordTimerJob?.apply {
            cancel()
            recordTimerJob = null
        }
    }

    val onPrepareRecording = LiveEvent<TempFlashMedia>()
    val onStartRecording = LiveEvent<TempFlashMedia>()

    private var _isRecording = false
        set(value) {
            field = value
            isRecording.postValue(value)
        }
    val isRecording = MutableLiveData<Boolean>(false)

    fun startStopRecording(finishRec: () -> Unit) {
        if (_isRecording) {
            if (recordTimerJob != null) clearRecording() else {
                finishRec.invoke()
            }
            return
        }
        if (_flashRecording.value != null) {
            clearRecording()
        }
        _isRecording = true
        Log.w("FRVM", "startStopRecording: prepare")
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val file = flashRepo.createTempVideoFile()
                TempFlashMedia.of(file).apply {
                    _tempRecording.postValue(this)
                    Log.w("FRVM", "startStopRecording: TFR created")
                    onPrepareRecording.postValue(this)
                    startRecordingTimer(_recordTimer.value) {
                        Log.w("FRVM", "startStopRecording: timer done")
                        onStartRecording.postValue(this)
                    }
                }
            } catch (ex: Exception) {
                Log.e("FRVM", "startStopRecording: catch", ex)
                clearRecording()
            }

        }
    }

    val onRecordingCancelled = LiveEvent<Boolean>()

    fun clearRecording() {
        _isRecording = false
        viewModelScope.launch(Dispatchers.IO) {
            stopRecordingTimer()
            clearTempRecording()
            updateFlashRecording(null)
            resetTextOverlay()
            onRecordingCancelled.postValue(true)
        }
    }

    fun clearVideoPostDetails() {
        flashCaption.postValue("")
        flashCategory.postValue(flashCategoryList.value?.find { it.selected })
        flashSharePublic.postValue(false)
        flashShareDears.postValue(false)
        flashShareFans.postValue(false)
        flashShareLikers.postValue(false)
    }

    val onRecordingFinished = LiveEvent<EditableFlashMedia>()
    val onRecordingTooShort = LiveEvent<Boolean>()
    val onGalleryVideoAdded = LiveEvent<EditableFlashMedia>()

    fun finishRecording() {
        viewModelScope.launch(Dispatchers.IO) {
            stopRecordingTimer()
            Log.w("FREF", "finishing recording")
            _tempRecording.value?.let {
                EditableFlashMedia.from(it).apply {
                    if (this.meta.durationMs >= MIN_DURATION_MS) {
                        updateFlashRecording(this)
                        onRecordingFinished.postValue(this)
                    } else {
                        onRecordingTooShort.postValue(true)
                    }
                }
            }
            clearTempRecording(false)
            _isRecording = false
        }
    }

    fun addVideo(uri: Uri) {
        clearRecording()
        val contentResolver = getApplication<MainApplication>().contentResolver
        if (MediaUtils.isLoadable(uri, getApplication()) == MediaUtils.UriLoadable.NO) {
            videoFileIsCorrupted.value = true
            return
        }

        /*
        By default, the system grants your app access to media files until the device is restarted or until your app stops.
        If your app performs long-running work, such as uploading a large file in the background,
        you might need this access to be persisted for a longer period of time
         */
        try {
            contentResolver.takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
            EditableFlashMedia.from(uri).apply {
                updateFlashRecording(this)
                onGalleryVideoAdded.postValue(this)
            }
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
        }
    }

    private suspend fun triggerFlashSync() = FlashUploadWorker.startIfNotRunning()

    private val _flashRecording = MutableLiveData<EditableFlashMedia?>(null)
    val flashRecording: LiveData<EditableFlashMedia?> = _flashRecording

    private fun updateFlashRecording(rec: EditableFlashMedia?) {
        Log.w("FREF", "updateFlashRecording: $rec")
        _flashRecording.postValue(rec)
        //check for duration and apply a crop
        rec?.let {
            val limitMs = (recordingLimit.value ?: 0) * 1000L
            val isOverLimit = it.meta.durationMs > limitMs
            val edits = VideoEditInfo(
                trim = if (isOverLimit) VideoEditInfo.Trim(0, limitMs, it.meta.durationMs) else null
            )
            Log.w("FREF", "over limit: setting trim ${edits.trim}")
            rec.edits = edits
            _flashEdits.postValue(edits)
        }
        flashVideoOngoingJob?.let { }
        _processedVideo.postValue(null)
    }

    private fun checkDurationLimits(trim: VideoEditInfo.Trim?, anchorStart: Boolean): VideoEditInfo.Trim? {
        trim ?: return null
        val limitMs = (recordingLimit.value ?: 0) * 1000L
        val isBelowLimit = trim.trimDurationMs < MIN_DURATION_MS
        val isOverLimit = trim.trimDurationMs > limitMs
        Log.w("FREF", "checkDurationLimits: suggested trim $trim")
        Log.w("FREF", "checkDurationLimits:trimDur: ${trim.trimDurationMs}, over: $isOverLimit, under: $isBelowLimit")
        val adjTrim = if (isOverLimit) {
            if (anchorStart) {
                trim.copy(
                    endMs = (trim.startMs + limitMs).coerceIn(0, trim.originalDurationMs)
                )
            } else {
                trim.copy(
                    startMs = (trim.endMs - limitMs).coerceIn(0, trim.originalDurationMs)
                )
            }
        } else if (isBelowLimit) {
            if (anchorStart) {
                val adjustedEnd = (trim.startMs + MIN_DURATION_MS).coerceIn(0, trim.originalDurationMs)
                trim.copy(
                    startMs = (adjustedEnd- MIN_DURATION_MS).coerceIn(0,trim.originalDurationMs),
                    endMs = adjustedEnd
                )
            } else {
                val adjustedStart = (trim.endMs - MIN_DURATION_MS).coerceIn(0, trim.originalDurationMs)
                trim.copy(
                    startMs = adjustedStart,
                    endMs = (adjustedStart + MIN_DURATION_MS).coerceIn(0,trim.originalDurationMs)
                )
            }
        } else trim
        Log.w("FREF", "checkDurationLimits: trim set to $adjTrim")
        return adjTrim
    }

    private val _flashEdits = MutableLiveData(VideoEditInfo())
    val flashEdits: LiveData<VideoEditInfo?> = _flashEdits

    private fun updateFlashEdits(edits: VideoEditInfo) {
        _flashEdits.postValue(edits)
        _flashRecording.value?.edits = edits
        flashVideoOngoingJob?.let { }
        _processedVideo.postValue(null)
    }

    fun setTrimRange(values: List<Float>, startHandle: Boolean) {
        val start = values.getOrElse(0) { 0f }
        val end = values.getOrElse(1) { 100f }
        Log.w("FREF", "setTrimRange: ${_flashRecording.value}")
        (_flashEdits.value ?: VideoEditInfo()).let {
            val dur = _flashRecording.value?.meta?.durationMs ?: 0
            val trim = if (start == 0f && end == 100f) null else VideoEditInfo.Trim((dur * start / 100).roundToLong().coerceAtLeast(0), (dur * end / 100).roundToLong().coerceAtMost(dur), dur)
            updateFlashEdits(it.copy(
                    trim = checkDurationLimits(trim, startHandle) ?: it.trim
            ))
        }
    }

    fun toggleMute() {
        Log.w("FREF", "toggleMute: ${_flashRecording.value}")
        (_flashEdits.value ?: VideoEditInfo()).let {
            updateFlashEdits(it.copy(mute = !it.mute))
        }
    }

    private val _flashDraft = MutableLiveData<FlashVideoWithMedia?>(null)
    val flashDraft: LiveData<FlashVideoWithMedia?> = _flashDraft

    val onDraftLoaded = LiveEvent<Boolean>()

    fun prepareDraft(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val draft = flashRepo.getLocalFlash(id) ?: return@launch
            _flashDraft.postValue(draft)
            flashCaption.postValue(draft.flash.caption)
            _isCommentsDisabled.postValue(draft.flash.commentDisabled)
            draft.flash.categoryId?.let {
                flashCategory.postValue(Category(it, draft.flash.category ?: ""))
            }
            flashSharePublic.postValue(draft.flash.shareTo.public)
            flashShareDears.postValue(draft.flash.shareTo.dears)
            flashShareFans.postValue(draft.flash.shareTo.fans)
            flashShareLikers.postValue(draft.flash.shareTo.likers)
            _processedVideo.postValue(draft.media?.file)
            onDraftLoaded.postValue(true)
        }
    }

    val flashCaption = MutableLiveData("")

    val flashCategory = MutableLiveData<Category?>(null)

    val flashSharePublic = MutableLiveData(false)
    val flashShareDears = MutableLiveData(false)
    val flashShareFans = MutableLiveData(false)
    val flashShareLikers = MutableLiveData(false)

    private val _flashCategoryList: MutableLiveData<List<Category>?> = MutableLiveData(null)
    val flashCategoryList: LiveData<List<Category>?> = _flashCategoryList

    init {
        getCategories()
    }

    fun getCategories() {
        if (!_flashCategoryList.value.isNullOrEmpty()) return
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = flashRepo.getFlashCategories()) {
                is ResultOf.Success -> {
                    _flashCategoryList.postValue(result.value)
                    flashCategory.value?.let { cat ->
                        flashCategory.postValue(result.value.find { it.categoryId == cat.categoryId })
                    } ?: run {
                        result.value.find { it.selected }?.let { default ->
                            flashCategory.postValue(default)
                        }
                    }
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    // Flash Posting

    private val _flashVideoProgress = MutableLiveData<Int?>(null)
    val flashVideoProgress: LiveData<Int?> = _flashVideoProgress
    private var flashVideoOngoingJob: Job? = null

    val videoIsEncoding = _flashVideoProgress.map {
        it != null
    }.distinctUntilChanged()

    val enablePostButton: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            val hasShareToData = flashSharePublic.value == true || flashShareDears.value == true || flashShareFans.value == true || flashShareLikers.value == true
            med.postValue(
                _processedVideo.value != null &&
                        flashCategory.value != null &&
                        hasShareToData &&
                        _flashSending.value != true
            )
        }
        med.addSource(onVideoProcessed) { update() }
        med.addSource(flashCategory) { update() }
        med.addSource(flashSharePublic) { update() }
        med.addSource(flashShareDears) { update() }
        med.addSource(flashShareFans) { update() }
        med.addSource(flashShareLikers) { update() }
        med.addSource(_flashSending) { update() }
        med
    }

    val disableSharePublic: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun update() {
            val allOtherShareToSelected = flashShareDears.value == true &&
                    flashShareFans.value == true &&
                    flashShareLikers.value == true
            med.postValue(
                allOtherShareToSelected
            )
            if(allOtherShareToSelected)
                flashSharePublic.postValue(false)
        }
        med.addSource(flashShareDears) { update() }
        med.addSource(flashShareFans) { update() }
        med.addSource(flashShareLikers) { update() }
        med
    }

    private val _flashSending = MutableLiveData(false)
    val messageSending: LiveData<Boolean> = _flashSending

    val onFlashCreate = LiveEvent<String>()
    val onFlashSavedToDraft = LiveEvent<String>()

    private val _processedVideo = MutableLiveData<File?>(null)
    val processedVideo: LiveData<File?> = _processedVideo

    val onVideoProcessed = LiveEvent<Boolean>()

    fun preProcessVideo() {
        if (_processedVideo.value == null && flashVideoOngoingJob?.isActive != true) {
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    val file = processVideo()
                    withContext(Dispatchers.Main) {
                        _processedVideo.value = file
                        onVideoProcessed.postValue(true)
                    }
                } catch (e: Exception) {
                    Firebase.crashlytics.recordException(e)
                    Log.w("FREF", "preProcessVideo: Could not process video")
                }
            }
        }
    }

    enum class VideoSaveStage {
        ENCODING,
        SAVING
    }

    private val _videoSaveProgress = MutableLiveData<VideoSaveStage?>(null)
    val videoSaveProgress: MediatorLiveData<Pair<VideoSaveStage, Int>?> by lazy {
        val med = MediatorLiveData<Pair<VideoSaveStage, Int>?>(null)
        fun update() {
            val progress = when (val stage = _videoSaveProgress.value) {
                VideoSaveStage.ENCODING -> Pair(stage, _flashVideoProgress.value ?: 0)
                VideoSaveStage.SAVING -> Pair(stage, -1)
                null -> null
            }
            med.postValue(progress)
        }
        med.addSource(_flashVideoProgress) { update() }
        med.addSource(_videoSaveProgress) { update() }
        med
    }

    val onSavedToGallery = LiveEvent<Boolean>()

    fun saveVideoToGallery() {
        _videoSaveProgress.value = VideoSaveStage.ENCODING
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val file = processVideo()
                _videoSaveProgress.postValue(VideoSaveStage.SAVING)
                MediaUtils.saveVideoToGallery(getApplication(), file)
                _videoSaveProgress.postValue(null)
                onSavedToGallery.postValue(true)
            } catch (e: Exception) {
                Firebase.crashlytics.recordException(e)
                Log.w("FREF", "preProcessVideo: Could not process video")
            }
        }
    }

    fun postFlashVideo(saveAsDraft: Boolean = false) {
        flashVideoOngoingJob?.let {
            if (it.isActive) {
                Log.w("PASM", "flashVideoOngoingJob is active")
                // TODO toast
                return
            }
        }
        _flashSending.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            try {
                var caption = flashCaption.value.orEmpty()
                val category = flashCategory.value
                val isCommentDisabled = isCommentsDisabled.value
                val shareTo = ShareTo(
                    public = flashSharePublic.value?:false,
                    dears = flashShareDears.value?:false,
                    fans = flashShareFans.value?:false,
                    likers = flashShareLikers.value?:false
                )

                val draft = _flashDraft.value
                val flash = if (draft != null) {
                    Log.w("PASM", "updating draft")
                    val fl = draft.flash.copy(
                        caption = caption,
                        categoryId = category?.categoryId,
                        category = category?.name,
                        shareTo = shareTo,
                        commentDisabled = isCommentDisabled?:true
                    )
                    flashRepo.updateDraft(fl, !saveAsDraft)
                    draft.copy(
                        flash = fl
                    )
                } else {
                    Firebase.crashlytics.log("calling processVideo")
                    val media = _processedVideo.value ?: return@launch

                    Firebase.crashlytics.log("video processed. now sending")
                    Log.d("ENCODE", "prepareAndSendMessage: video processed. Sending message")

                    Log.w("PASM", "prepareAndSendMessage: text: $caption | category: ${category?.name} | shareTo: $shareTo | media: ${media.path}")
                    Log.d("HEEEINDNDND", "Entered fun${isCommentDisabled}")
                    flashRepo.createFlash(media, caption, category, shareTo, saveAsDraft, isCommentDisabled ?: true)
                }
                if (saveAsDraft) onFlashSavedToDraft.postValue(flash.flash.id)
                else onFlashCreate.postValue(flash.flash.id)
                triggerFlashSync()
            } catch (e: Exception) {
                Log.d("ENCODE", "prepareAndSendMessage: error: ${e.message}")
            }
            _flashSending.postValue(false)
        }
    }

    private suspend fun processVideo(): File = withContext(Dispatchers.IO) {
        val media = flashRecording.value ?: throw Exception("No media to process")
        flashVideoOngoingJob?.let {
            flashVideoOngoingJob = null
            if (it.isActive) it.cancelAndJoin()
        }
        _flashVideoProgress.postValue(-1)
        Firebase.crashlytics.log("Starting Video compress")
        Log.d("ENCODE", "Starting Video compress")
        var processed: File? = null
        launch {
            try {
                val result = flashRepo.processVideo(media, object : VideoEncoderUtil.MediaProcessingListener {
                    override fun onProgress(progress: Int) {
                        Log.d("ENCODE", "Progress: $progress%")
                        _flashVideoProgress.postValue(progress)
                    }

                    override fun onProcessingFinished(success: Boolean) {
                        Log.d("ENCODE", "Encode Done ($success) as per VM")
                        _flashVideoProgress.postValue(null)
                    }
                })
                flashVideoOngoingJob = null
                Log.d("ENCODE", "processed ${media.path} to ${result.path}")
                Firebase.crashlytics.log("processed ${media.path} to ${result.path}")
                processed = result
            } catch (e: Throwable) {
                Log.w("ENCODE", "ProcessVideo cancelled", e)
                Firebase.crashlytics.recordException(e)
                flashVideoOngoingJob = null
                _flashVideoProgress.postValue(null)
                throw e
            }
        }.apply {
            flashVideoOngoingJob = this
            join()
        }
        return@withContext processed!!
    }

    private val _isCommentsDisabled = MutableLiveData(false)
    val isCommentsDisabled: LiveData<Boolean> = _isCommentsDisabled

    fun handleCommentsSwitch(checked: Boolean) {
        _isCommentsDisabled.value = checked
    }

    enum class TextOverlayColor{
        WHITE,
        RED,
        GREEN,
        BLACK,
        YELLOW
    }

    data class TextOverlaySpecs(
        val text: String = "",
        val color: TextOverlayColor = TextOverlayColor.WHITE,
        val bold: Boolean = false,
        val italic: Boolean = false,
        val underline: Boolean = false,
        val offset: Offset = Offset(0F, 0F)
    ) {
        var didCommit = false
    }

    private val _textOverlaySpecs = MutableLiveData<TextOverlaySpecs?>(null)
    val textOverlaySpecs: LiveData<TextOverlaySpecs?> = _textOverlaySpecs

    val hasOverlay: Boolean
        get() {
            val spec = _textOverlaySpecs.value?: return false
            return spec.text.isNotBlank() && spec.didCommit!=true
        }

    private val _isTextEditingActive = MutableLiveData<Boolean>(false)
    val isTextEditingActive: LiveData<Boolean> = _isTextEditingActive

    private val _isColorPickerVisible = MutableLiveData<Boolean>(false)
    val isColorPickerVisible: LiveData<Boolean> = _isColorPickerVisible

    fun toggleTextEditing() {
        val wasActive = _isTextEditingActive.value == true
        _isTextEditingActive.value = !wasActive

        if (!wasActive) {
            _isColorPickerVisible.value = false
            _textOverlaySpecs.postValue(_textOverlaySpecs.value?: TextOverlaySpecs())
        }
//        else {
//            _textOverlaySpecs.postValue(null)
//        }
    }

    private fun resetTextOverlay() {
        _textOverlaySpecs.postValue(null)
    }

    private fun updateTextOverlaySpecs(specs: TextOverlaySpecs) {
        specs.didCommit = false
        _textOverlaySpecs.postValue(specs)
        // any extra steps can be done here
    }

    fun updateTextOverlayText(text: String) {
        updateTextOverlaySpecs((_textOverlaySpecs.value ?: TextOverlaySpecs()).copy(text = text))
    }

    fun updateTextOverlayColor(color: TextOverlayColor) {
        updateTextOverlaySpecs((_textOverlaySpecs.value ?: TextOverlaySpecs()).copy(color = color))
    }

    fun toggleColorPicker() {
        _isColorPickerVisible.value = _isColorPickerVisible.value != true
    }

    fun toggleBold() {
        (_textOverlaySpecs.value ?: TextOverlaySpecs()).let {
            updateTextOverlaySpecs(it.copy(bold = !it.bold))
        }
    }

    fun toggleItalic() {
        (_textOverlaySpecs.value ?: TextOverlaySpecs()).let {
            updateTextOverlaySpecs(it.copy(italic = !it.italic))
        }
    }

    fun updateTextOverlayOffset(offset: Offset) {
        updateTextOverlaySpecs((_textOverlaySpecs.value ?: TextOverlaySpecs()).copy(offset = offset))
    }

    fun toggleUnderline() {
        (_textOverlaySpecs.value ?: TextOverlaySpecs()).let {
            updateTextOverlaySpecs(it.copy(underline = !it.underline))
        }
    }

    fun addTextOverlay(bitmap: Bitmap) {
        (_flashEdits.value ?: VideoEditInfo()).let {
            updateFlashEdits(it.copy(
                overlay = VideoEditInfo.TextOverlay(bitmap)
            ))
        }
        _textOverlaySpecs.value?.didCommit = true
    }

}