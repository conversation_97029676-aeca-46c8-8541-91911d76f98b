package com.app.messej.ui.home.publictab.stars

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.view.ActionMode
import androidx.core.os.bundleOf
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.AbstractChatMessage
import com.app.messej.data.model.enums.BroadcastAction
import com.app.messej.databinding.FragmentBroadcastStarredBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.chat.BaseChatDisplayFragment
import com.app.messej.ui.chat.adapter.ChatAdapter
import com.app.messej.ui.home.publictab.common.BroadcastStarredChatAdapter
import com.app.messej.ui.home.publictab.common.BroadcastStarredViewModel
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.kennyc.view.MultiStateView

class StarBroadcastStarredFragment : BaseChatDisplayFragment(), MenuProvider {

    companion object {
        const val SELECTED_MESSAGE_KEY = "sb_select_message"
    }

    private lateinit var binding: FragmentBroadcastStarredBinding

    private val args: StarBroadcastStarredFragmentArgs by navArgs()

    override val viewModel: BroadcastStarredViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_broadcast_starred, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        addAsMenuHost()
        setup()
        observe()
    }

    override val chatRecyclerView: RecyclerView
        get() = binding.chatList

    override val bindingRoot: View
        get() = binding.root

    override val multiStateView: MultiStateView
        get() = binding.multiStateView

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.apply {
            setupActionBar(binding.customActionBar.toolbar)
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        menuInflater.inflate(R.menu.menu_broadcast_starred_options, menu)
        menu.findItem((R.id.action_unstar_all)).apply {
            isVisible = mAdapter?.snapshot()?.isEmpty()==false
            actionView?.setOnClickListener {
                viewModel.unstarAll()
            }
        }
    }

    override fun onMenuItemSelected(menuItem: MenuItem) = false

    private fun setup() {
        viewModel.setIncomingBroadcastMode(args.broadcaster)

        mAdapter?.addOnPagesUpdatedListener {
            (activity as MenuHost).invalidateMenu()
        }

        mAdapter?.apply {
            addLoadStateListener { loadState ->
                multiStateView.let { msv ->
                    val state = if (itemCount < 1) {
                        if (loadState.append.endOfPaginationReached){
                            MultiStateView.ViewState.EMPTY
                        } else MultiStateView.ViewState.CONTENT
                    } else MultiStateView.ViewState.CONTENT
                    binding.multiStateView.viewState = state
                    viewModel.setViewState(state)
                }
            }
        }
    }

    private var emptyViewBinding: LayoutListStateEmptyBinding? = null
    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            emptyViewBinding?.apply {
                edsEmptyImage.visibility = View.GONE
                edsEmptyMessage.text = resources.getString(R.string.broadcast_starred_eds)
            }
            Log.w("BCDFLSL", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("BCDFLSL", "setEmptyView: ")
        }
    }

    private fun observe() {
        viewModel.actionIsStar.observe(viewLifecycleOwner) { star ->
            actionMode?.menu?.findItem(R.id.action_star)?.setIcon(if(star) R.drawable.ic_star else R.drawable.ic_unstar)
        }
        viewModel.onStarAction.observe(viewLifecycleOwner) {
            when (it) {
                BroadcastAction.STAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_starred), Toast.LENGTH_SHORT).show()
                BroadcastAction.UNSTAR -> Toast.makeText(requireContext(), resources.getString(R.string.broadcast_toast_unstarred), Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.broadcasterImage.observe(viewLifecycleOwner) {
            (mAdapter as BroadcastStarredChatAdapter).setBroadCasterImage(it)
        }
    }

    override val provideAdapter: ChatAdapter
        get() = BroadcastStarredChatAdapter(layoutInflater, viewModel.user.id, this)

    override fun onItemClick(item: AbstractChatMessage, position: Int): Boolean {
        if (viewModel.selectMessage(item,position)) return true
        findNavController().popBackStack()
        setFragmentResult(SELECTED_MESSAGE_KEY, bundleOf(SELECTED_MESSAGE_KEY to item.messageId))
        return true
    }

    override fun showSelectionMode(show: Boolean) {
        if(show) {
            val callback = object: ActionMode.Callback {
                override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    requireActivity().menuInflater.inflate(R.menu.menu_star_broadcast_selection,menu)
                    return true
                }
                override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
                    return false
                }
                override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
                    when(item?.itemId) {
                        R.id.action_star -> viewModel.toggleStar()
                        R.id.action_copy -> viewModel.copySelection()
                        else -> return false
                    }
                    return true
                }
                override fun onDestroyActionMode(mode: ActionMode?) {
                    viewModel.exitSelectionMode()
                }
            }
            actionMode = (requireActivity() as MainActivity).startSupportActionMode(callback)
        }
        else {
            actionMode?.finish()
            actionMode = null
        }
    }
}