package com.app.messej.ui.home.settings

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.AppLocalSettings
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.api.subscription.Subscription
import com.app.messej.data.model.enums.NightModeSetting
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.repository.SettingsRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.LogCatReader
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class SettingsBottomSheetViewModel(application: Application) : AndroidViewModel(application) {
    private val authRepo = AuthenticationRepository(getApplication())
    private val accountRepo: AccountRepository = AccountRepository(application)
    private val businessRepo = BusinessRepository(application)
    private val datastore: FlashatDatastore = FlashatDatastore()
    private val profileRepo = ProfileRepository(application)
    private val settingsRepo = SettingsRepository(application)
    val onSwitchNightMode = LiveEvent<NightModeSetting>()


    val userAccount = accountRepo.userFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val user: CurrentUser?
        get() = if (accountRepo.loggedIn) accountRepo.user else null

    init {
        getSubscriptionDetails()
    }

    private val _appSettings = datastore.settingsFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = null
    )
    val appSettings: LiveData<AppLocalSettings?> = _appSettings.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext).distinctUntilChanged()

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails

    private val _loggingOut = MutableLiveData<Boolean>(false)
    val loggingOut: LiveData<Boolean> = _loggingOut

    val onLoggedOut = LiveEvent<Boolean>()

    var isPremiumUser = if (accountRepo.loggedIn) accountRepo.isPremiumUser else null
        private set

    val citizenship : UserCitizenship?
        get() = if (accountRepo.loggedIn) accountRepo.user.citizenship else null

    var isResident = if (accountRepo.loggedIn) accountRepo.isResident else null
        private set

    var isVisitor = if (accountRepo.loggedIn) accountRepo.isVisitor else null
        private set

    fun logout() {
        _loggingOut.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val result = authRepo.logoutUser()
            _loggingOut.postValue(false)
            if (result is ResultOf.Success) {
                onLoggedOut.postValue(true)
            }
        }
    }

//    fun countAppShare() {
//        viewModelScope.launch {
//            when (profileRepo.countAppShare()) {
//                is ResultOf.Success -> businessRepo.getBusinessOperations()
//                else -> {
//
//                }
//            }
//        }
//    }


    fun saveNightModeSetting(setting: NightModeSetting) {
        _appSettings.value?.appNightMode = setting
        viewModelScope.launch {
            val settings = settingsRepo.getSettings().firstOrNull() ?: return@launch
            settings.appNightMode = setting
            datastore.saveSettings(settings)
            onSwitchNightMode.value = setting
        }
    }
    private val _isActive = MutableLiveData<UserSubscriptionStatus?>(null)
    val isActive: LiveData<UserSubscriptionStatus?> = _isActive

    private fun getSubscriptionDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Subscription> = profileRepo.getSubscriptionDetails()) {
                is ResultOf.Success -> {
                    if(result.value.status.equals("active")){
                        _isActive.postValue(UserSubscriptionStatus.ACTIVE)
                    }else{
                        _isActive.postValue(UserSubscriptionStatus.EXPIRED)
                    }
                }
                is ResultOf.APIError -> {
                    _isActive.postValue(UserSubscriptionStatus.FREE)
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

    val logCapture = LogCatReader.loggingEnabledFlow.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
}