package com.app.messej.ui.auth.common

import android.graphics.Color
import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.NearbyPlacesCompleteResponse
import com.app.messej.databinding.ItemNearByPlacesBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class NearbyLocationAdapter(data: MutableList<NearbyPlacesCompleteResponse.Results>) :
    BaseQuickAdapter<NearbyPlacesCompleteResponse.Results, BaseDataBindingHolder<ItemNearByPlacesBinding>>(R.layout.item_near_by_places, data) {

    override fun convert(holder: BaseDataBindingHolder<ItemNearByPlacesBinding>, item: NearbyPlacesCompleteResponse.Results) {
        holder.dataBinding?.apply {
            locationItem = item

            try {
                locationIconMcv.setCardBackgroundColor(Color.parseColor(item.iconBackgroundColor))
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<NearbyPlacesCompleteResponse.Results>() {
        override fun areItemsTheSame(oldItem: NearbyPlacesCompleteResponse.Results, newItem: NearbyPlacesCompleteResponse.Results): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(
            oldItem: NearbyPlacesCompleteResponse.Results,
            newItem: NearbyPlacesCompleteResponse.Results,
        ): Boolean {
            return oldItem.placeId == newItem.placeId
        }
    }
}