package com.app.messej.ui.home.publictab.podiums.manage

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.navigation.navGraphViewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.databinding.FragmentPodiumBlockedUsersBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.kennyc.view.MultiStateView

class PodiumFrozenUsersFragment : Fragment() {

    private val viewModel: PodiumLiveViewModel by navGraphViewModels(R.id.nav_live_podium)

//    private var mFrozenUsersListAdapter: BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumBlockedUserBinding>>? = null
    private var mFrozenUsersListAdapter: PodiumBlockedUsersListAdapter? = null

    private lateinit var binding: FragmentPodiumBlockedUsersBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_blocked_users, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setEmptyView()
        setup()
        observe()
    }

    override fun onResume() {
        super.onResume()
        mFrozenUsersListAdapter?.refresh()
    }

    fun setup(){
        initAdapter()
    }

    fun observe(){
//        viewModel.frozenUsers.observe(viewLifecycleOwner) {
//            mFrozenUsersListAdapter?.apply {
//                if (data.size == 0 || it?.size == 0) {
//                    setNewInstance(it.orEmpty().toMutableList())
//                } else {
//                    setDiffNewData(it.orEmpty().toMutableList())
//                }
//            }
//        }

        viewModel.frozenUsersList.observe(viewLifecycleOwner) {
            it?: return@observe
            mFrozenUsersListAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }

//        viewModel.frozenUsersListLoading.observe(viewLifecycleOwner) {
//            binding.multiStateView.viewState = if (it) MultiStateView.ViewState.LOADING
//            else if (mFrozenUsersListAdapter?.data?.size == 0) {
//                MultiStateView.ViewState.EMPTY
//            } else {
//                MultiStateView.ViewState.CONTENT
//            }
//        }

        viewModel.onUserFreezeToggled.observe(viewLifecycleOwner) { msg: String? ->
            msg?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
            mFrozenUsersListAdapter?.refresh()
        }
    }

    private fun initAdapter() {
//        val differ = object : DiffUtil.ItemCallback<PodiumSpeaker>() {
//            override fun areItemsTheSame(oldItem: PodiumSpeaker, newItem: PodiumSpeaker): Boolean {
//                return oldItem.id == newItem.id
//            }
//            override fun areContentsTheSame(oldItem: PodiumSpeaker,newItem: PodiumSpeaker): Boolean {
//                return oldItem == newItem
//            }
//        }

//        mFrozenUsersListAdapter = object : BaseQuickAdapter<PodiumSpeaker, BaseDataBindingHolder<ItemPodiumBlockedUserBinding>>(R.layout.item_podium_blocked_user, mutableListOf()) {
//            override fun convert(holder: BaseDataBindingHolder<ItemPodiumBlockedUserBinding>, item: PodiumSpeaker) {
//                holder.dataBinding?.apply {
//                    speaker = item
//                    action = resources.getString(R.string.podium_action_unfreeze)
//                    unblockActionButton.isVisible = item.id!=viewModel.user.id
//                    unblockActionButton.setOnClickListener {
//                        confirmAction(
//                            message = R.string.podium_action_unfreeze_user_confirm_message
//                        ) {
//                            viewModel.freezeToggle(item.id,item.name,false)
//                        }
//                    }
//                }
//            }
//        }.apply {
//            animationEnable = true
//            adapterAnimation = ScaleInAnimation()
//            isAnimationFirstOnly = false
//            setDiffCallback(differ)
//        }


        mFrozenUsersListAdapter = PodiumBlockedUsersListAdapter(object : PodiumBlockedUsersListAdapter.PodiumActionListener {
            override fun onActionButtonClicked(view: View, speaker: PodiumSpeaker) {
                confirmAction(
                    message = R.string.podium_action_unblock_user_confirm_message
                ) {
                    viewModel.freezeToggle(speaker.id, speaker.name, false)
                }
            }
        }, resources.getString(R.string.podium_action_unfreeze))

        binding.blockedUsers.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = mFrozenUsersListAdapter
        }

        mFrozenUsersListAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1){
                        MultiStateView.ViewState.EMPTY
                    }else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
                    MultiStateView.ViewState.CONTENT
                }
            }
        }

    }

    private var emptyViewBinding: LayoutListStateEmptyBinding? = null

    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            emptyViewBinding?.apply {
                edsEmptyImage.setImageResource(R.drawable.im_eds_podium)
                edsEmptyMessage.text = resources.getString(R.string.podium_frozen_user_eds_message)
            }
            Log.w("BCDFLSL", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("BCDFLSL", "setEmptyView: ")
        }
    }

}