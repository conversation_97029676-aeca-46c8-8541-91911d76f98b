package com.app.messej.ui.home.publictab.socialAffairs.questions

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse.Companion.questionTestData
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse.Companion.questionTestDataWithAnswer
import com.app.messej.data.model.api.socialAffairs.AskedQuestionsResponse.Companion.testQuestionCurrentUser
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.google.android.filament.utils.degrees

@Composable
fun QuestionAndAnswerSingleView(
    item: AskedQuestionsResponse.QuestionAndAnswer,
    currentUser: CurrentUser,
    isExpanded:Boolean = false,
    isAlertView: Boolean = false,
    presidentUserId: Int? = null,
    onEdit: (isQuestion: Boolean) -> Unit,
    onDelete: (isQuestion: Boolean) -> Unit,
    onRespondToQuestion: () -> Unit
) {
    var isExpanded by remember { mutableStateOf(value = isExpanded) }
    val rotationDegree by animateFloatAsState(targetValue = if (isExpanded) 180F else 0F)
    val canDeleteOrEditQuestion = item.canDeleteOrEditQuestion(currentUserId = currentUser.id)
    val canDeleteOrEditAnswer = item.canDeleteOrEditAnswer(currentUserId = currentUser.id, presidentUserId = presidentUserId)
    val isRTLDirection = LocalLayoutDirection.current == LayoutDirection.Rtl

    Column (
        modifier = Modifier
            .animateContentSize()
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
            .background(color = colorResource(id = R.color.colorSurface))
            .padding(horizontal = dimensionResource(id = R.dimen.element_spacing))
            .padding(top = 13.dp)
            .fillMaxWidth()
    ) {
        //Question Icon, Question Text and Arrow mark view
        Row(
            modifier = Modifier
                .height(intrinsicSize = IntrinsicSize.Max)
                .fillMaxWidth(),
        ) {
            //Question Icon and Arrow mark view
            Column(
                modifier = Modifier.fillMaxHeight(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                QuestionIcon()
                //Arrow Mark Line and Image view
                AnimatedVisibility(
                    modifier = Modifier.offset(x = 7.dp),
                    visible = item.isAnswered && isExpanded
                ) {
                    Column(
                        modifier = Modifier
                            .padding(top = dimensionResource(id = R.dimen.element_spacing))
                            .fillMaxHeight()
                    ) {
                        //Arrow Mark Line
                        Box(
                            modifier = Modifier
                                .fillMaxHeight()
                                .weight(weight = 1F)
                                .background(color = colorResource(id = R.color.colorAlwaysLightSurfaceSecondaryDarker))
                                .width(width = 1.dp)
                        )
                        //Arrow Mark Image View
                        Icon(
                            painter = painterResource(id = R.drawable.ic_arrow_right_curved),
                            modifier = Modifier
                                .graphicsLayer {
                                    rotationY = if (isRTLDirection) 180F else 0F
                                }
                                .offset(y = (-3).dp, x = (-0.4).dp),
                            tint = colorResource(id = R.color.colorAlwaysLightSurfaceSecondaryDarker),
                            contentDescription = null
                        )
                    }
                }
            }
            //Question Text
            Text(
                text = item.question ?: "",
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(weight = 1F)
                    .padding(bottom = dimensionResource(id = R.dimen.extra_margin))
                    .padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
                color = colorResource(id = R.color.textColorPrimary),
                style = FlashatComposeTypography.defaultType.body2
            )
            if (item.isDropdownArrowVisible(currentUserId = currentUser.id, presidentUserId = presidentUserId)) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_dropdown_down_rounded),
                    modifier = Modifier
                        .clip(shape = CircleShape)
                        .clickable { isExpanded = !isExpanded }
                        .rotate(degrees = rotationDegree),
                    tint = colorResource(id = R.color.colorSocialRed),
                    contentDescription = null
                )
            }
        }
        AnimatedVisibility(
            modifier = Modifier.offset(y = (-16).dp),
            visible = isExpanded
        ) {
            //Answer, Waiting Response, Tap to Respond, edit, delete icons.
            Row(
                modifier = Modifier
                    .padding(start = dimensionResource(id = R.dimen.extra_margin))
                    .fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(weight = 1F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    //Answer Text
                    if (item.isAnswered) {
                        Text(
                            text = item.answer ?: "",
                            modifier = Modifier
                                .padding(horizontal = dimensionResource(id = R.dimen.element_spacing))
                                .fillMaxWidth(),
                            color = colorResource(id = R.color.textColorPrimary),
                            style = FlashatComposeTypography.defaultType.overline
                        )
                    }
                    //Waiting Response Text...
                    if (item.isAwaitingResponse(currentUserId = currentUser.id)) {
                        Text(
                            text = stringResource(id = R.string.social_questions_waiting_response),
                            color = colorResource(id = R.color.textColorSecondaryLight),
                            style = FlashatComposeTypography.defaultType.body2
                        )
                    }
                    //Tap to Respond Text...
                    if (item.canAnswer(currentUserId = currentUser.id, presidentUserId = presidentUserId)) {
                        TextButton(onClick = onRespondToQuestion) {
                            Text(
                                text = stringResource(id = R.string.social_reply_question),
                                color = colorResource(id = R.color.textColorSecondaryLight),
                                style = FlashatComposeTypography.defaultType.body2
                            )
                        }
                    }
                }
                if ((canDeleteOrEditQuestion || canDeleteOrEditAnswer) && !isAlertView) {
                    Column(
                        modifier = Modifier.padding(top = dimensionResource(id = R.dimen.element_spacing)),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        //Delete Icon
                        EditDeleteIcon(
                            isEdit = false,
                            onClick = {
                                onDelete(canDeleteOrEditQuestion)
                            }
                        )
                        CustomVerticalSpacer(
                            space = dimensionResource(id = R.dimen.element_spacing)
                        )
                        //Edit Icon
                        EditDeleteIcon(
                            isEdit = true,
                            onClick = {
                                onEdit(canDeleteOrEditQuestion)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun QuestionAndAnswerSingleItemPreview() {
    Column {
        QuestionAndAnswerSingleView(
            item = questionTestData,
            currentUser = testQuestionCurrentUser,
            isExpanded = true,
            onRespondToQuestion = {},
            onEdit = {},
            onDelete = {}
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        QuestionAndAnswerSingleView(
            item = questionTestDataWithAnswer,
            currentUser = testQuestionCurrentUser,
            isExpanded = true,
            onRespondToQuestion = {},
            onEdit = {},
            onDelete = {}
        )
    }
}

@Composable
fun QuestionAndAnswerAlertDialog(
    item: AskedQuestionsResponse.QuestionAndAnswer?,
    currentUser: CurrentUser
) {
    val state = rememberScrollState()
    AnimatedVisibility(
        visible = item != null,
        modifier = Modifier
            .fillMaxSize()
            .background(color = colorResource(id = R.color.colorAlwaysDarkSurfaceSecondaryDarker).copy(alpha = 0.9F))
            .padding(bottom = 80.dp),
        enter = slideInVertically { -it },
        exit = slideOutVertically { -2 * it }
    ) {
        Box(
            modifier = Modifier
                .verticalScroll(state = state)
                .padding(all = dimensionResource(id = R.dimen.activity_margin))
        ) {
            QuestionAndAnswerSingleView(
                item = item ?: return@AnimatedVisibility,
                currentUser = currentUser,
                isExpanded = true,
                isAlertView = true,
                onEdit = {},
                onDelete = {},
                onRespondToQuestion = {}
            )
        }
    }
}

@Preview
@Composable
private fun QuestionIcon() {
    Box(
        modifier = Modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)))
            .size(size = dimensionResource(id = R.dimen.extra_margin))
            .background(color = colorResource(id = R.color.colorSocialRed)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_social_question),
            modifier = Modifier.padding(all = dimensionResource(id = R.dimen.line_spacing)),
            tint = colorResource(id = R.color.white),
            contentDescription = null
        )
    }
}

@Composable
private fun EditDeleteIcon(
    isEdit: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clip(shape = CircleShape)
            .size(size = dimensionResource(id = R.dimen.extra_margin))
            .clickable { onClick() }
            .background(color = colorResource(id = if (isEdit) R.color.colorPrimary else R.color.colorSocialRed)),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(id = if (isEdit) R.drawable.ic_edit else R.drawable.ic_delete),
            modifier = Modifier.padding(all = dimensionResource(id = R.dimen.line_spacing)),
            tint = colorResource(id = R.color.white),
            contentDescription = null
        )
    }
}

@Preview
@Composable
private fun EditDeleteIconPreview() {
    Row {
        EditDeleteIcon(isEdit = true, onClick = {})
        EditDeleteIcon(isEdit = false, onClick = {})
    }
}