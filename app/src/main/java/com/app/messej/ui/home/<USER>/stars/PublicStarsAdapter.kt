package com.app.messej.ui.home.publictab.stars

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.updateLayoutParams
import androidx.paging.PagingDataAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.app.messej.R
import com.app.messej.data.model.entity.UserStar
import com.app.messej.databinding.ItemHomeSuperstarListBinding
import com.app.messej.databinding.ItemUserWithActionStatsBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.utils.BindingExtensions.prepare

class PublicStarsAdapter(private val listener: StarClickListener) : PagingDataAdapter<StarsUIModel, PublicStarsAdapter.StarsListViewHolder>(StarsDiff) {

    interface StarClickListener {
        fun onStarClick(star: UserStar)
        fun muteUnmuteSuperStar(star: UserStar)
        fun showHideSuperStar(star: UserStar)
    }

    companion object {
        const val ITEM_SUPERSTAR = 1
        const val ITEM_STAR = 5
        const val ITEM_STAR_EDS = 8
    }


    override fun onBindViewHolder(holder: StarsListViewHolder, position: Int) {
        getItem(position)?.let { holder.bind(it) }
    }

    abstract inner class StarsListViewHolder(view: View): RecyclerView.ViewHolder(view) {
        abstract fun bind(item: StarsUIModel)
    }


    override fun getItemViewType(position: Int): Int {
        return when(peek(position)) {
            is StarsUIModel.SuperStarModel -> ITEM_SUPERSTAR
            is StarsUIModel.StarModel -> ITEM_STAR
            StarsUIModel.EmptyStarModel -> ITEM_STAR_EDS
            null -> throw IllegalStateException("Unknown view")
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        when(viewType){
            ITEM_SUPERSTAR -> SuperStarViewHolder(ItemHomeSuperstarListBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            ITEM_STAR -> StarsViewHolder(ItemUserWithActionStatsBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            ITEM_STAR_EDS -> StarsEmptyViewHolder(LayoutListStateEmptyBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            else -> throw IllegalStateException("Unknown view type")
        }

    inner class StarsViewHolder(private val binding: ItemUserWithActionStatsBinding) : StarsListViewHolder(binding.root) {
        override fun bind(item: StarsUIModel) = with(binding) {
            val data  = (item as StarsUIModel.StarModel).star
            user = data
            clickable = true
            badge = if(data.unreadMessagesCount>0) data.unreadMessagesCount.toString() else null
            binding.root.setOnClickListener {
                listener.onStarClick(data)
            }
        }
    }

    inner class StarsEmptyViewHolder(private val binding: LayoutListStateEmptyBinding) : StarsListViewHolder(binding.root) {
        override fun bind(item: StarsUIModel) = with(binding) {
            binding.root.updateLayoutParams<ViewGroup.LayoutParams> {
                height = ViewGroup.LayoutParams.WRAP_CONTENT
            }
            prepare(
                image = R.drawable.im_eds_stars,
                message = R.string.stars_list_eds
            )
        }
    }

    inner class SuperStarViewHolder(private val binding: ItemHomeSuperstarListBinding) : StarsListViewHolder(binding.root) {
        override fun bind(item: StarsUIModel) = with(binding) {
            val data  = (item as StarsUIModel.SuperStarModel).superStar
            superstar = data
            clickable = true
            binding.superstarMuteIcon.apply {
                setImageResource(if(data.muted) R.drawable.ic_mute else R.drawable.ic_unmute)
                setOnClickListener { listener.muteUnmuteSuperStar(data) }
            }

            binding.superstarVisibilityIcon.apply {
                setImageResource(if(data.hidden) R.drawable.ic_eye_invisible_white else R.drawable.ic_eye_visible_white)
                setOnClickListener { listener.showHideSuperStar(data) }
            }

            badge = if(data.unreadMessagesCount>0) data.unreadMessagesCount.toString() else null
            binding.userLayout.root.setOnClickListener {
                listener.onStarClick(data)
            }
        }
    }

    object StarsDiff : DiffUtil.ItemCallback<StarsUIModel>() {
        override fun areItemsTheSame(oldItem: StarsUIModel, newItem: StarsUIModel): Boolean {
            val isSameSuperStar = oldItem is StarsUIModel.SuperStarModel && newItem is StarsUIModel.SuperStarModel
                    && oldItem.superStar.id == newItem.superStar.id
            val isSameStar = oldItem is StarsUIModel.StarModel && newItem is StarsUIModel.StarModel
                    && oldItem.star.id == newItem.star.id

            return isSameSuperStar || isSameStar
        }

        override fun areContentsTheSame(oldItem: StarsUIModel, newItem: StarsUIModel) : Boolean {
            return oldItem == newItem
        }
    }
}