package com.app.messej.ui.home.publictab.postat

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.VideoPlaybackCookie
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.entity.Postat
import com.app.messej.data.model.enums.LegalAffairPermissionSource
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.repository.PostatRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.utils.CountryListUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

abstract class PostatFeedBaseViewModel(application: Application) : AndroidViewModel(application) {

    val postatRepo = PostatRepository(application)
    val profileRepo = ProfileRepository(application)
    val accountRepo = AccountRepository(application)
    private val legalAffairsRepo = LegalAffairsRepository(application)

    val user: CurrentUser
        get() = accountRepo.user

    val isVisitor = user.citizenship.isVisitor

    private val countryList = MutableLiveData<Map<String, Int>>()

    init {
        countryList.postValue(CountryListUtil.getCustomCountryMap())
    }

    val showCompactLoading = MediatorLiveData(false)
    private val nickNamesLiveData: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _actionLoading = MutableLiveData(false)
    val actionLoading: LiveData<Boolean> = _actionLoading

    val onCookiesRefreshed = LiveEvent<VideoPlaybackCookie>()

    init {
        refreshCookies()
    }

    private fun refreshCookies() {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = postatRepo.getPlaybackCookie()) {
                is ResultOf.Success -> {
                    result.value.let {
                        onCookiesRefreshed.postValue(it)
                    }
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val dataLoadingMore = MutableLiveData<Boolean>(false)

    abstract val _postatList: LiveData<PagingData<Postat>>

    val postatList: MediatorLiveData<PagingData<FeedPostatAdapter.PostatUIModel>> by lazy {
        val med = MediatorLiveData<PagingData<FeedPostatAdapter.PostatUIModel>>()
        fun combine() {
            val list = _postatList.value
            val nickNames = nickNamesLiveData.value
            val flags = countryList.value

            val data: PagingData<FeedPostatAdapter.PostatUIModel>? = list?.map { item ->
                var nick = nickNames?.find { nn -> nn.userId == item.userId }?.nickName
                if(nick.isNullOrBlank()) nick = null
                FeedPostatAdapter.PostatUIModel(item.copy(
                    senderDetails = item.senderDetails.copy(
                        name = nick?: item.senderDetails.name,
                        _username = nick?: item.senderDetails.name
                    ))).apply {
                    item.senderDetails.countryCode?.let { cc ->
                        countryFlag = flags?.get(cc)
                    }
                }
            }
            med.postValue(data)
        }
        med.addSource(_postatList) { combine() }
        med.addSource(nickNamesLiveData) { combine() }
        med.addSource(countryList) { combine() }
        med
    }

    private val _cookies = postatRepo.playbackCookieFlow()
    val cookies = _cookies.map {
        if (it?.isExpired==true) {
            refreshCookies()
            null
        } else it
    }.stateIn(viewModelScope, SharingStarted.Eagerly, null)

    val onUserFollow = LiveEvent<String>()
    val onUserUnFollow = LiveEvent<String>()

    private suspend fun getUserProfile(id: Int): OtherUser? {
        when (val result =profileRepo.getPublicUserDetails(id)) {
            is ResultOf.Success -> {
                return result.value
            }
            is ResultOf.APIError -> {
            }
            is ResultOf.Error -> {
            }
        }
        return null
    }

    private val _activeItem = MutableLiveData<Int?>(null)
    val activeItem: LiveData<Int?> = _activeItem

    fun setActiveItem(pos: Int) {
        _activeItem.value = pos
    }

    fun clearActiveItem() {
        _activeItem.value = null
    }

    fun followUser(id: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            var user = profileRepo.getLocalOtherUserProfile(id).value
            if (user==null) {
                user = getUserProfile(id)?: return@launch
            }
            when (val result = profileRepo.followUser(user)) {
                is ResultOf.Success -> {
                    profileRepo.updateOtherUser(user.copy(isStar = true))
                    onUserFollow.postValue(user.username)
                }
                is ResultOf.APIError -> { }
                is ResultOf.Error -> { }
            }
        }
    }

    fun unFollowUser(userId: Int, userName: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.unFollowStar(userId)) {
                is ResultOf.Success -> onUserUnFollow.postValue(userName)
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val onPostatDeleted = LiveEvent<Boolean>()


    fun deletePost(postatId: String, position: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            when (postatRepo.deletePostat(postatId)) {
                is ResultOf.Success -> {
                    onPostatDeleted.postValue(true)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    fun hidePost(postatId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (postatRepo.hidePostat(postatId)) {
                is ResultOf.Success -> {}
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val onShareCodeGenerated = LiveEvent<String>()

    fun getShareCode(postatId: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = postatRepo.getShareCode(postatId)) {
                is ResultOf.Success -> {
                    result.value.result.let {
                        onShareCodeGenerated.postValue(it)
                    }
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}

            }
        }
    }

    val onPostatUserIgnored = LiveEvent<String>()

    fun ignorePostatUser(postat: Postat, block: Boolean) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = postatRepo.ignorePostatUser(postat.userId.toString(), block)) {
                is ResultOf.Success -> {
                    onPostatUserIgnored.postValue(postat.senderDetails.name)
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    val onSuccessfullyBlockedUserFromPosting = LiveEvent<Boolean>()
    fun blockUserFromPostat(userId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            val result = postatRepo.blockUserFromPostingOnPostat(userId = userId)
            if (result is ResultOf.Success) {
                onSuccessfullyBlockedUserFromPosting.postValue(true)
            }
        }
    }

    private fun setActionLoading(isLoading: Boolean) {
        _actionLoading.postValue(isLoading)
    }

    val errorMessage = LiveEvent<String>()
    val isNavigateToUpdatePost = LiveEvent<Pair<Boolean, String>>()
    fun checkPermissionToUpdatePostat(id: String) {
        viewModelScope.launch (Dispatchers.IO) {
            setActionLoading(isLoading = true)
            when(val result = legalAffairsRepo.checkPermission(id = id, source = LegalAffairPermissionSource.POSTAT)) {
                is ResultOf.Success -> {
                    isNavigateToUpdatePost.postValue(Pair(result.value.isPermissionToUpdate, id))
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    errorMessage.postValue(result.exception.message)
                }
            }
            setActionLoading(isLoading = false)
        }
    }

}