package com.app.messej.ui.home.publictab.socialAffairs

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.databinding.FragmentSocialPolicyDocumentBinding
import com.app.messej.ui.common.PolicyDocumentViewModel
import com.app.messej.ui.utils.FragmentExtensions.showToast
import kotlin.getValue

class SocialPolicyDocumentFragment : Fragment() {

    private lateinit var binding: FragmentSocialPolicyDocumentBinding
    private val viewModel: PolicyDocumentViewModel by viewModels()
    private val args: SocialPolicyDocumentFragmentArgs by navArgs()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_social_policy_document, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.getLegalDocument(documentType = args.documentType)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(toolBar = this, customNavIcon = R.drawable.ic_social_back_button)
                setNavigationOnClickListener { findNavController().navigateUp() }
            }
        }
    }

    private fun setup() {
        setPageTitle()
    }

    private fun observe() {
        viewModel.policyData.observe(viewLifecycleOwner) {
            binding.policyWebView.visibility = View.VISIBLE
            val html = it?.legalDocument?.description
            if (!html.isNullOrEmpty()) {
                binding.policyWebView.loadData(html, "text/html; charset=utf-8", "UTF-8")
            }
        }
        viewModel.tncError.observe(viewLifecycleOwner) {
            if (!it.isNullOrEmpty()) {
                showToast(message = it)
            }
            binding.policyWebView.visibility = View.GONE
        }
    }

    private fun setPageTitle() {
        binding.customActionBar.toolBarTitle.text = when (args.documentType) {
            DocumentType.ABOUT_PRESIDENTIAL_AFFAIRS ->getString(R.string.settings_title_about_presidential_affairs)
            DocumentType.ABOUT_SOCIAL_AFFAIRS ->getString( R.string.settings_title_about_social_affairs)
            DocumentType.SOCIAL_AFFAIRS_ABOUT_DONATE -> getString(R.string.social_about_donate)
            DocumentType.SOCIAL_AFFAIRS_ABOUT_COMMITTEE -> getString(R.string.social_affairs_committee)
            DocumentType.SOCIAL_AFFAIRS_ABOUT_SOCIAL_SUPPORT -> getString(R.string.social_about_social_support)
            DocumentType.SOCIAL_AFFAIRS_ABOUT_MINISTER -> getString(R.string.social_minister_about_title)
            else -> ""
        }
    }

}