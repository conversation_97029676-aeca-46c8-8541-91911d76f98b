package com.app.messej.ui.home.publictab.podiums.yalla

import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.navigation.navGraphViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.R
import com.app.messej.data.model.api.podium.challenges.YallaGuysJoinResponse
import com.app.messej.data.model.entity.YallaGuysChallenge
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.databinding.FragmentPodiumYallaBottomSheetBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensurePodiumCreateAllowed
import com.app.messej.ui.utils.ExpandableListBottomSheetDialogFragment
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.Synchronize
import com.app.messej.ui.utils.ViewUtils

class YallaGuysListBottomSheetFragment : ExpandableListBottomSheetDialogFragment() {

    private lateinit var binding: FragmentPodiumYallaBottomSheetBinding
    val args: YallaGuysListBottomSheetFragmentArgs by navArgs()
    val viewModel: YallaGuysListViewModel by navGraphViewModels(R.id.nav_live_podium)
    private var mAdapter: YallaGuysListAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.Widget_Flashat_PodiumBottomSheet)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_podium_yalla_bottom_sheet, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
//        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        viewModel.setParams(args.podiumId, args.challengeId)
        setUp()
        observe()
    }

    private fun setUp() {

        viewModel.setParams(args.podiumId)

        snapToBottom(binding.actionHolder,binding.root)

        mAdapter = YallaGuysListAdapter(object: YallaGuysListAdapter.YallaGuysListListener() {

            override fun getChallengeIcon(type: ChallengeType?): Drawable? {
                return when(type) {
                    null -> null
                    else -> ContextCompat.getDrawable(requireContext(),type.iconRes)
                }
            }

            override fun statusText(data: YallaGuysChallenge): String {
                return if (data.queued || data.status== YallaGuysJoinResponse.YallaJoinStatus.QUEUED) getString(R.string.podium_yalla_guys_status_queued)
                else if(data.waiting) getString(R.string.podium_yalla_guys_status_waiting)
                else ""
            }

            override fun getParticipantCount(data: YallaGuysChallenge): String {
                return "${data.participantsCount}/${data.maxParticipantsCount}"
            }

            override fun canJoin(data: YallaGuysChallenge): Boolean {
                return data.isMoreParticipantsAllowed && !data.isParticipant(viewModel.user.id)
            }

            override fun onJoinClick(data: YallaGuysChallenge) {
                ensurePodiumCreateAllowed {
                    confirmJoin(data)
                }
            }
        }).apply {
            addLoadStateListener { loadState ->
                val state = ViewUtils.getViewState(loadState, itemCount)
                viewModel.setViewState(state)
//                viewModel.showCompactLoading.postValue(loadState.append is LoadState.Loading)
            }
        }

        binding.challengesList.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            setHasFixedSize(false)
            adapter = mAdapter
        }

        binding.actionCreate.setOnClickListener {
            ensurePodiumCreateAllowed {
                findNavController().navigateSafe(YallaGuysListBottomSheetFragmentDirections.actionGlobalYallaGuysCreateFragment(args.podiumId,true))
            }
        }
    }

    private fun observe() {
        viewModel.yallaGuysList.observe(viewLifecycleOwner) { pagingData ->
            pagingData?.let {
                mAdapter?.submitData(viewLifecycleOwner.lifecycle, pagingData)
            }
        }

        viewModel.debouncedViewState.observe(viewLifecycleOwner) {
            it?.let { vs ->
                binding.multiStateView.viewState = vs
            }
        }

        viewModel.onChallengeJoined.observe(viewLifecycleOwner) {
            when(it) {
                YallaGuysJoinResponse.YallaJoinStatus.JOINED -> showSnackbar(R.string.podium_yalla_guys_status_joined)
                YallaGuysJoinResponse.YallaJoinStatus.QUEUED -> showSnackbar(R.string.podium_yalla_guys_queued_message)
                YallaGuysJoinResponse.YallaJoinStatus.WAITING -> showSnackbar(R.string.podium_yalla_guys_waiting_message)
                YallaGuysJoinResponse.YallaJoinStatus.STARTED -> popSafely()
            }
        }

        viewModel.onChallengeJoinFull.observe(viewLifecycleOwner) {
            showJoinFullAlert()
        }

        viewModel.onChallengeJoinError.observe(viewLifecycleOwner) {
            showToast(it)
        }
    }

    private var didPopBackstack: Boolean by Synchronize(false)

    private fun popSafely() {
        if (didPopBackstack) return
        didPopBackstack = true
        findNavController().popBackStack(R.id.yallaGuysListBottomSheetFragment, true)
    }

    private fun confirmJoin(item: YallaGuysChallenge) {
        showFlashatDialog {
            setMessage(getString(R.string.podium_yalla_guys_join_confirm,getString(item.gameType.resId)))
            setConfirmButton(R.string.podium_yalla_guys, R.drawable.ic_yalla_gamepad_square, tint = false, iconPadding = false) {
                viewModel.joinChallenge(item)
                true
            }
        }
    }

    private fun showJoinFullAlert() {
        showFlashatDialog {
            setMessage(R.string.podium_yalla_guys_join_full)
            setConfirmButton(R.string.podium_yalla_guys_new,R.drawable.ic_plus) {
                findNavController().navigateSafe(YallaGuysListBottomSheetFragmentDirections.actionGlobalYallaGuysCreateFragment(args.podiumId,true))
                true
            }
        }
    }
}