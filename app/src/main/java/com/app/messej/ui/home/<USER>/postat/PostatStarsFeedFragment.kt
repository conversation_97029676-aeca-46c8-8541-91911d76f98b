package com.app.messej.ui.home.publictab.postat

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModel
import com.app.messej.R

class PostatStarsFeedFragment : PostatFeedBaseFragment() {

    override val viewModel: PostatStarsFeedViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {

        innerBinding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_inner_list, container, false)
        innerBinding.lifecycleOwner = viewLifecycleOwner
        innerBinding.viewModel = viewModel
        return innerBinding.root
    }
    override fun onResume() {
        super.onResume()
        mAdapter?.refresh()
    }

}