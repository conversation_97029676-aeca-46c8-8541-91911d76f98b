package com.app.messej.ui.utils

import com.app.messej.MainApplication
import com.app.messej.R
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hbb20.CCPCountry
import com.hbb20.CountryCodePicker

object CountryListUtil {

    private var list: List<CCPCountry>? = null
    private var map: Map<String, Int>? = null

    @Synchronized
    private fun fetchData() {
        try {
            val data = CCPCountry.getLibraryMasterCountryList(MainApplication.applicationInstance(), CountryCodePicker.Language.ENGLISH).toMutableList().apply {
                add(CCPCountry("SJ", "+47", "'Svalbard and Jan Mayen", com.hbb20.R.drawable.flag_norway))
                add(CCPCountry("EH", "+212", "Western Sahara", R.drawable.flag_western_sahara))
                add(CCPCountry("TF", "+262", "French Southern Territories", R.drawable.flag_french_southern))
                add(CCPCountry("BQ", "+599", "Bonaire", <PERSON>.drawable.flag_bonaire))
                add(CCPCountry("TP", "+670", "East Timor", R.drawable.flag_east_timor))
                add(CCPCountry("UM", "+1", "United States Minor Outlying Islands", R.drawable.flag_us_islands))
                add(CCPCountry("BV", "+47", "Bouvet Island", R.drawable.flag_bovet_islands))
                add(CCPCountry("GS", "+500", "South Georgia and the South Sandwich Islands", R.drawable.flag_southern_georgia))
                add(CCPCountry("HM", "+61", "Heard Island and McDonald Islands", R.drawable.flag_heard_island))

                removeAll { it.nameCode.lowercase() == "sy" }
                add(CCPCountry("sy", "963", "Syrian Arab Republic", R.drawable.flag_syria))
            }.sortedBy { it.nameCode }
            list = data
            map = data.associate { it.nameCode to it.flagID }
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
        }
    }

    fun getCustomCountryList(): List<CCPCountry> {
        if(list==null) fetchData()
        return list.orEmpty()
    }

    fun getCustomCountryMap(): Map<String, Int> {
        if (map==null) fetchData()
        return map.orEmpty()
    }
}