package com.app.messej.ui.home

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.subscription.Subscription
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SubscriptionStatusViewModel(application: Application) : AndroidViewModel(application) {

    private val profileRepo = ProfileRepository(application)

    private val _isActive = MutableLiveData<UserSubscriptionStatus?>(null)
    val isActive: LiveData<UserSubscriptionStatus?> = _isActive

    private val _subscriptionLoaded = MutableLiveData(false)
    val subscriptionLoaded: LiveData<Boolean> = _subscriptionLoaded

    init {
        getSubscriptionDetails()
    }

    private fun getSubscriptionDetails() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                _subscriptionLoaded.postValue(false)
                when (val result: ResultOf<Subscription> = profileRepo.getSubscriptionDetails()) {
                    is ResultOf.Success -> {
                        _subscriptionLoaded.postValue(true)
                        if(result.value.status.equals("active")){
                            _isActive.postValue(UserSubscriptionStatus.ACTIVE)
                        }else{
                            _isActive.postValue(UserSubscriptionStatus.EXPIRED)
                        }
                    }
                    is ResultOf.APIError -> {
                        _subscriptionLoaded.postValue(true)
                        _isActive.postValue(UserSubscriptionStatus.FREE)
                    }
                    is ResultOf.Error -> {
                        _subscriptionLoaded.postValue(true)
                    }
                }
            }catch (e:Exception){
                Log.e("SubscriptionStatusException",e.message.toString())
                Firebase.crashlytics.recordException(e)
            }
        }
    }
}