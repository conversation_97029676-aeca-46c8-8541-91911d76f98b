package com.app.messej.ui.home.publictab.flash.comments

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.cachedIn
import androidx.paging.liveData
import com.app.messej.data.model.FlashCommentLikeRequest
import com.app.messej.data.model.FlashCommentPayload
import com.app.messej.data.model.FlashReplyCommentPayload
import com.app.messej.data.model.SenderDetails
import com.app.messej.data.model.entity.PostCommentWithReplies
import com.app.messej.data.model.enums.CommentType
import com.app.messej.data.repository.FlashRepository
import com.app.messej.data.repository.PostatRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.common.BaseCommentsViewModel
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class FlashCommentsViewModel(application: Application) : BaseCommentsViewModel<PostCommentWithReplies>(application) {

    private val flashRepo = FlashRepository(getApplication())
    private val postatRepo = PostatRepository(getApplication())

    val repository = FlashatDatastore()

    private var flashID = MutableLiveData<String?>(null)
    private var commentID = MutableLiveData<String?>(null)
    var mentionedUser = MutableLiveData<String?>(null)

    private val _selectedCommentId = MutableLiveData<String?>(null)
    val selectedCommentId: LiveData<String?> = _selectedCommentId

    private val _selectedReplyCommentId = MutableLiveData<String?>(null)
    val selectedReplyCommentId: LiveData<String?> = _selectedReplyCommentId

    var undoPressed = false




    val isVisitor: Boolean
        get() = user.citizenship.isVisitor

    fun setFlashId(id: String, commentsEnabled: Boolean = true) {
        flashID.postValue(id)
        _commentsEnabled.postValue(commentsEnabled)
    }

    override val _commentList = flashID.switchMap { id ->
        if (id != null) flashRepo.getFlashComments(id, commentCountCallback).liveData.cachedIn(viewModelScope) else null
    }

    override fun replaceSenderDetails(obj: PostCommentWithReplies, senderDetails: SenderDetails?): PostCommentWithReplies {
        return obj.copy(
            commentItem = obj.commentItem.copy(senderDetails = senderDetails)
        )
    }

    override suspend fun executeWriteComment(comment: String): ResultOf<Unit> {
        return flashRepo.writeComment(flashID.value.toString(), FlashCommentPayload(accountRepo.user.id, comment))
    }

    override suspend fun executeReplayComment(comment: String, mentionedUId: Int): ResultOf<Unit> {
        val cleanedComment = comment.replace(mentionedUser.value ?: "", "").trim()
        Log.d("ENCODED_CMT", "$cleanedComment")

        // Store the current flashId for use in the repository
        val currentFlashId = flashID.value.toString()

        return flashRepo.writeReplyComment(
            commentID.value.toString(), FlashReplyCommentPayload(
                reply = cleanedComment, mentionedUserId = mentionedUId, flashId = currentFlashId // Pass flashId to repository
            )
        )
    }

    private val _onCommentDeleted = MutableLiveData<Boolean>()
    val onCommentDeleted: LiveData<Boolean> = _onCommentDeleted

    fun deleteFlashComment(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Boolean> = flashRepo.deleteFlashComment(id)) {
                is ResultOf.Success -> {
                    conNewCommendAdded.emit(value = true)
                    replyDeleted.emit(true)
                    _onCommentDeleted.postValue(false)
                }

                else -> {}
            }
        }
    }

    fun deleteFlashCommentsFromDB(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            flashRepo.deleteFlashCommentFromDB(id)
            _onCommentDeleted.postValue(true)
        }
    }


    fun deleteFlashCommentReply(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Boolean> = flashRepo.deleteFlashCommentReply(id)) {
                is ResultOf.Success -> {
                    postatRepo.deleteReplyById(id, CommentType.FLASH)
                    replyDeleted.emit(true)
                    _onCommentDeleted.postValue(false)
                    undoPressed = false
                }

                else -> {}
            }
        }
    }

    fun deleteFlashCommentsReplyFromDB(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            flashRepo.deleteFlashCommentReplyFromDB(id)
            _onCommentDeleted.postValue(true)
        }
    }

    val skipNextTime = LiveEvent<Boolean>()


    fun getSkipAction() {
        viewModelScope.launch {
            skipNextTime.postValue(repository.getFlashPaidLikeCheckBoxAction())
        }
    }


    fun setUpPaidLike(checked: Boolean) {
//        if(checked) skipNextTime.postValue(true) else skipNextTime.postValue(false)
        viewModelScope.launch {
            if (checked) repository.saveFlashPaidLikeCheckBoxAction(true) else repository.saveFlashPaidLikeCheckBoxAction(false)
        }
    }

    private val _isLikeLoading = MutableLiveData<Boolean>(false)
    val isLikeLoading: LiveData<Boolean> = _isLikeLoading


    val onPaidLikeInsufficientBalance = LiveEvent<Boolean>()
    val onPaidLikeError = LiveEvent<String>()

    fun sendCommentPaidLike(commentId: String?, replayId: String?) {
        viewModelScope.launch(Dispatchers.IO) {
            _isLikeLoading.postValue(true)
            try {
                when (val result = flashRepo.sendFlashCommentLike(FlashCommentLikeRequest(commentId = commentId, replyId = replayId))) {
                    is ResultOf.Success -> {
                        conNewCommendAdded.emit(value = true)
                        getSkipAction()
                    }

                    is ResultOf.APIError -> {
                        if (result.code == 400) {
                            onPaidLikeInsufficientBalance.postValue(true)
                        } else {
                            onPaidLikeError.postValue(result.error.message)
                        }
                    }

                    is ResultOf.Error -> {
                        onPaidLikeError.postValue(result.errorMessage())
                    }
                }
                _isLikeLoading.postValue(false)

            } catch (e: Exception) {
                Log.d("PodiumLVM", "sendLikeByOthers: erPodiumActionTyperor: ${e.message}")
            }
        }
    }

    fun callReplies(commentId: String) {
        viewModelScope.launch {
            flashRepo.getFlashReplies(
                flashId = flashID.value.toString(), parentCommentId = commentId, page = 1, pageSize = 20
            )
        }

    }

    fun setCommentId(commentId: String) {
        commentID.postValue(commentId)
    }

    fun setMentionedUser(user: String) {
        mentionedUser.postValue(user)
    }


    fun validateDeleteAction(reply: Boolean, commentId: String) {
        viewModelScope.launch {
            undoPressed = false
            if (reply) {
                deleteFlashCommentsReplyFromDB(commentId)
            } else {
                deleteFlashCommentsFromDB(commentId)
            }

            delay(5000L) // Wait for 5 seconds

            if (!undoPressed) {
                if (reply) {
                    deleteFlashCommentReply(commentId)
                } else {
                    deleteFlashComment(commentId)
                }
            } else {
                conNewCommendAdded.emit(value = true)
            }
        }
    }

    fun undoComment() {
        undoPressed = true
        _onCommentDeleted.postValue(false)
    }


//    val isSendButtonEnabled: Boolean
//        get() = (postingComment.value==false) && commentText.value?.trim()?.isNotEmpty() == true && commentText.value?.contains(mentionedUser.value.toString())==true


    val isSendButtonEnabled: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun combineSources() {
            val isPosting = postingComment.value ?: false
            val text = commentText.value?.trim().orEmpty()
            val mention = mentionedUser.value?.toString().orEmpty()
            // Check if text contains @mention and also has other characters
            val hasMention = mention.isNotBlank() && text.contains(mention)
            val onlyMention = text == mention

           val value = !isPosting && text.isNotEmpty() && (/*!hasMention ||*/ !onlyMention)

            med.postValue(value)
        }
        med.addSource(postingComment) { combineSources() }
        med.addSource(commentText) { combineSources() }
        med.addSource(mentionedUser) { combineSources() }
        med
    }


}