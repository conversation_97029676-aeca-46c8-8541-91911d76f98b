package com.app.messej.ui.home.publictab.podiums.manage

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.podium.PodiumParticipant
import com.app.messej.data.model.api.podium.PodiumSpeaker
import com.app.messej.data.model.entity.NickName.Companion.nickNameOrName
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class PodiumLiveUsersListPublicViewModel(application: Application) : AndroidViewModel(application) {
    private val podiumRepository = PodiumRepository(application)
    private val profileRepo = ProfileRepository(application)
    private val accountRepo = AccountRepository(application)

    val user: CurrentUser get() = accountRepo.user

    private val _podiumId = MutableLiveData<String?>(null)
    val podiumId: LiveData<String?> = _podiumId



    private val _podiumDetails = MutableLiveData<Podium?>(null)
    val podiumDetails: LiveData<Podium?> = _podiumDetails

    val _speakerList = podiumDetails.switchMap {
        MutableLiveData(it?.speakers)
    }.distinctUntilChanged()


    fun isSpeaker(userId: Int): Boolean {
        val speakerList = _speakerList.value
        if (speakerList != null) {
            return speakerList.any { it?.id == userId }
        }
        return false
    }


    fun setPodiumId(id: String) {
        getPodiumDetails(id)
        if (_podiumId.value == id) return
        _podiumId.value = id
    }

    val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    private val nickNamesLiveData = nickNames.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val adminListActionLoading: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            med.postValue(
                adminsListLoading.value == true || cancelAdminInviteLoading.value == true || dismissAsAdminLoading.value == true || appointAsAdminLoading.value == true
            )
        }
        med.addSource(adminsListLoading) { update() }
        med.addSource(cancelAdminInviteLoading) { update() }
        med.addSource(dismissAsAdminLoading) { update() }
        med.addSource(appointAsAdminLoading) { update() }
        med
    }

    val adminsListLoading = MutableLiveData(false)

//    private val _admins = MutableLiveData<MutableList<PodiumSpeaker>>(mutableListOf())
//    val admins: LiveData<MutableList<PodiumSpeaker>> = _admins.map { it ->
//        it.map { ps ->
//            ps.copy(
//                name = nickNames.nickNameOrName(ps)
//            )
//        }.toMutableList()
//    }

    private val _allAdmins = podiumId.switchMap {
        it ?: return@switchMap null
        podiumRepository.getPodiumAdminsListPager(it).liveData.cachedIn(viewModelScope)
    }
    val allAdmins = _allAdmins.map {
        it.map { ps ->
            if (ps.invitedToBeAdmin)
                if (!_invitedAdminIdList.contains(ps.id)) _invitedAdminIdList.add(ps.id)
                else
                    if (_invitedAdminIdList.contains(ps.id)) _invitedAdminIdList.remove(ps.id)

            ps.copy(
                name = nickNames.nickNameOrName(ps)
            )
        }
    }


    var _adminIdList: MutableList<Int> = mutableListOf()

    private var _invitedAdminIdList: MutableList<Int> = mutableListOf()

//    fun getPodiumAdminsList() {
//        viewModelScope.launch(Dispatchers.IO) {
//            try {
//                _podiumId.value ?: return@launch
//                adminsListLoading.postValue(true)
//                when (val result = podiumRepository.getPodiumAdminsList(_podiumId.value!!)) {
//                    is ResultOf.APIError -> {
//                    }
//
//                    is ResultOf.Error -> {
//                    }
//
//                    is ResultOf.Success -> {
//
//                        result.value.toMutableList().let {
//                            _admins.postValue(it)
//
//                            _invitedAdminIdList = it.filter { adminList ->
//                                adminList.invitedToBeAdmin
//                            }.map { flList -> flList.id }.toMutableList()
//                        }
//
//                    }
//                }
//                adminsListLoading.postValue(false)
//            } catch (e: Exception) {
//                Log.d("PodiumLVM", "getPodiumAdminsList: error: ${e.message}")
//            }
//        }
//    }

    val onAdminActionFinished = LiveEvent<Boolean>()
    val appointAsAdminLoading = MutableLiveData(false)

    val dismissAsAdminLoading = MutableLiveData(false)
    fun dismissAsAdmin(userId: Int) {
        _podiumId.value?.let {
            dismissAsAdminLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (val result = podiumRepository.dismissAnAdmin(it, userId)) {
                        is ResultOf.Success -> {
                            onAdminActionFinished.postValue(true)
                            _adminIdList.removeIf { userId == it }
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "dismissAsAdmin: erPodiumActionTyperor: ${e.message}")
                }
                dismissAsAdminLoading.postValue(false)
            }
        }
    }

    val cancelAdminInviteLoading = MutableLiveData(false)
    fun cancelAdminInvite(userId: Int) {
        _podiumId.value?.let {
            cancelAdminInviteLoading.postValue(true)
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (podiumRepository.cancelAdminAppoint(it, userId)) {
                        is ResultOf.Success -> {
                            onAdminActionFinished.postValue(true)
                            _invitedAdminIdList.removeIf { userId == it }
                        }

                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }

                } catch (e: Exception) {
                    Log.d("PodiumLVM", "cancelAdminInvite: erPodiumActionTyperor: ${e.message}")
                }
                cancelAdminInviteLoading.postValue(false)
            }
        }
    }

    private fun MutableLiveData<MutableList<PodiumSpeaker>>.removeAndPost(id: Int) {
        try {
            if (this.value?.find { it.id == id } == null) return
            this.postValue(this.value?.apply {
                removeAll { it.id == id }
            })
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
        }
    }

    val liveUsersListLoading = MutableLiveData(false)

    private val _liveUsersList = _podiumId.switchMap {
        it ?: return@switchMap null
        podiumRepository.getPodiumLiveUsersListingPager(it).liveData.cachedIn(viewModelScope)
    }

    val liveUsersList: MediatorLiveData<PagingData<PodiumParticipant>> by lazy {
        val med = MediatorLiveData<PagingData<PodiumParticipant>>()
        fun update() {
            val data = _liveUsersList.value?.map { speaker ->
                speaker.copy(
                    name = nickNames.nickNameOrName(speaker)
                )
            }
            med.postValue(data)
        }
        med.addSource(nickNamesLiveData) { update() }
        med.addSource(_liveUsersList) { update() }
        med
    }

    fun getPodiumDetails(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                when (val result = podiumRepository.getPodiumDetails(id)) {
                    is ResultOf.Success -> {
                        _podiumDetails.postValue(result.value)
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                Log.d("PodiumLVM", "getPodiumDetails: error: ${e.message}")
            }
        }
    }



    val onFollowedUser = LiveEvent<String>()

    val onUnfollowedUser = LiveEvent<String>()

    fun toggleFollow(id : Int, name :String, isFollowed: Boolean) {
        if (isFollowed) unFollowUser(id, name) else followUser(id, name)
    }

    private fun followUser(id : Int, name :String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.followUser(id)) {
                is ResultOf.Success -> {
                    onFollowedUser.postValue(name)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
        }
    }

    private fun unFollowUser(id : Int, name : String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (profileRepo.unFollowStar(id)) {
                is ResultOf.Success -> {
                    onUnfollowedUser.postValue(name)
                }

                is ResultOf.APIError -> {
                }

                is ResultOf.Error -> {
                }
            }
        }
    }
}