package com.app.messej.ui.auth.common

import androidx.recyclerview.widget.DiffUtil
import com.app.messej.R
import com.app.messej.data.model.api.PlacesAutoCompleteResponse
import com.app.messej.databinding.ItemSearchLocationBinding
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder

class LocationSearchQuickAdapter(data: MutableList<PlacesAutoCompleteResponse.Predictions>): BaseQuickAdapter<PlacesAutoCompleteResponse.Predictions, BaseDataBindingHolder<ItemSearchLocationBinding>>(R.layout.item_search_location, data) {

    override fun convert(holder: BaseDataBindingHolder<ItemSearchLocationBinding>, item: PlacesAutoCompleteResponse.Predictions) {
        holder.dataBinding?.apply {
            locationItem = item
        }
    }

    class DiffCallback: DiffUtil.ItemCallback<PlacesAutoCompleteResponse.Predictions>() {
        override fun areItemsTheSame(oldItem: PlacesAutoCompleteResponse.Predictions,newItem: PlacesAutoCompleteResponse.Predictions): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: PlacesAutoCompleteResponse.Predictions,
                                        newItem: PlacesAutoCompleteResponse.Predictions): Boolean {
            return oldItem == newItem
        }
    }
}