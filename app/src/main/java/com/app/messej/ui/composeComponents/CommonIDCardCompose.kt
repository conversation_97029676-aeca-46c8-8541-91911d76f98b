package com.app.messej.ui.composeComponents

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.app.messej.R
import com.app.messej.data.model.Achievement
import com.app.messej.data.model.entity.OtherUser
import com.app.messej.data.model.enums.FollowerType
import com.app.messej.data.model.enums.UserBadge
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.enums.UserType
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomVerticalSpacer
import com.app.messej.ui.home.publictab.socialAffairs.components.UserImageBadgeAndCountryCodeView
import com.app.messej.ui.profile.PublicUserProfileViewModel
import com.app.messej.ui.utils.EnumUtils.displayText
import java.util.Locale.getDefault

/**
Used for displaying the ID card
 * @param viewModel is type of [PublicUserProfileViewModel]
 Pass this from the fragment. And also in fragment's on created view
 call [PublicUserProfileViewModel.setUserId] function in the [PublicUserProfileViewModel]
 * @param userDescription If need to show description under the citizenship text. Currently using in Social Affairs
 */
@Composable
fun UserIDCardCompose(
    viewModel: PublicUserProfileViewModel,
    userDescription: String?
) {
    val context = LocalContext.current
    val pagerState = rememberPagerState(pageCount = { 4 })
    val user by viewModel.profile.observeAsState()
    val isCurrentUser by viewModel.isCurrentUser.observeAsState()
    val accountDetails by viewModel.accountDetails.observeAsState()
    val countryFlag by viewModel.countryFlagCompose.observeAsState()
    val nickName by viewModel.userNickName.observeAsState()
    val isLoading by viewModel.dataLoading.observeAsState(initial = false)

    if (isLoading) {
        ComposeShimmerLayout(
            modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
        ) { brush ->
            IDCardShimmerView(brush = brush)
        }
        return
    }

    val achievementList = getAchievementList(context = context, user = user)

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        HorizontalPager(
            state = pagerState,
            contentPadding = PaddingValues(horizontal = dimensionResource(id = R.dimen.activity_margin)),
            modifier = Modifier
                .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
                .height(height = 240.dp), /*Used custom height because pager content need to have same size. Need to check whether it can be replaced*/
            pageSpacing = dimensionResource(id = R.dimen.activity_margin)
        ) { position ->
            when (position) {
                0 -> {
                    IDCardView(
                        modifier = Modifier.fillMaxHeight(),
                        user = user,
                        userDescription = userDescription,
                        isCurrentUser = isCurrentUser,
                        nickName = nickName?.nickName,
                        countryFlag = if (accountDetails?.showCountryFlag == true) countryFlag else null
                    )
                }
                else -> {
                    AchievementIDCardView(
                        modifier = Modifier.fillMaxHeight(),
                        citizenship = user?.citizenship,
                        achievements = achievementList[position - 1]
                    )
                }
            }
        }
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        HorizontalDotIndicator(
            selectedItemPosition = pagerState.currentPage,
            totalCount = pagerState.pageCount
        )
    }
}


@Composable
private fun AchievementIDCardView(
    modifier: Modifier = Modifier,
    citizenship: UserCitizenship?,
    achievements: List<Achievement>
) {
    val image = painterResource(id = citizenship.setIdCardBackground())
    val labelTextColor = colorResource(id = citizenship.setupLabelTextColor())
    //For different background id card, the extra padding can be add by using the below condition
    //Add another citizenship, if need any extra padding
    val isToBeAddExtraPadding = citizenship?.isPresident == true

    Column(
        modifier = modifier
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            //Adding the drawable background here
            .drawBehind {
                with(receiver = image) {
                    draw(size = <EMAIL>)
                }
            }.padding(all = dimensionResource(id = if (isToBeAddExtraPadding) R.dimen.double_margin else R.dimen.activity_margin))
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.id_card_achievements).uppercase(locale = getDefault()),
            style = FlashatComposeTypography.defaultType.subtitle1,
            color = labelTextColor
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
        ) {
            repeat(times = achievements.size) {
                val item = achievements[it]
                UserIDCardAchievementSingleItem(
                    icon = item.icon,
                    textColor = labelTextColor,
                    title = item.title,
                    value = item.value
                )
            }
        }
    }
}

@Preview
@Composable
private fun AchievementIDCardPreview() {
    val testAchievements = List(size = 4) {
        Achievement(icon = R.drawable.ic_idcard_lv, title = R.string.id_card_tribe_members, value = "20")
    }
    AchievementIDCardView(
        citizenship = UserCitizenship.MINISTER,
        achievements = testAchievements
    )
}

@Composable
private fun UserIDCardAchievementSingleItem(
    @DrawableRes icon: Int,
    textColor: Color,
    @StringRes title: Int,
    value: String?
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = icon),
            tint = textColor,
            modifier = Modifier.size(size = 20.dp),
            contentDescription = null
        )
        CustomHorizontalSpacer(
            space = dimensionResource(id = R.dimen.activity_margin)
        )
        Text(
            text = buildAnnotatedString {
                append(text = stringResource(id = title) + ": ")
                withStyle(style = SpanStyle(fontWeight = FontWeight.ExtraBold)) { append(value ?: "") }
            }, style = FlashatComposeTypography.overLineBold,
            color = textColor
        )
    }
}


@Preview(showBackground = true)
@Composable
private fun AchievementSingleItemPreview() {
    UserIDCardAchievementSingleItem(
        icon = R.drawable.ic_idcard_lv,
        textColor = colorResource(id = R.color.colorPrimary),
        title = R.string.id_card_generosity_level,
        value = "89"
    )
}

/**
Used for displaying the ID card
 * @param user Current user details
 * @param userDescription If need to show description under the citizenship text. Currently using in Social Affairs
 * @param nickName The nickname shown in the id card
 * @param isCurrentUser Checks whether the user is same
 * @param countryFlag The country flag
 */
@Composable
private fun IDCardView(
    modifier: Modifier = Modifier,
    user: OtherUser?,
    userDescription: String? = null,
    nickName: String?,
    isCurrentUser:Boolean?,
    @DrawableRes countryFlag: Int?
) {
    val labelTextColor = colorResource(id = user?.citizenship.setupLabelTextColor())
    val dateTitleTextColor = colorResource(id = user?.citizenship.setupDateTitleColor())
    val dateTextColor = colorResource(id = user?.citizenship.setupDateTextColor())
    val image = painterResource(id = user?.citizenship.setIdCardBackground())
    val isPresident = user?.citizenship?.isPresident == true

    //For different background id card, the extra padding can be add by using the below condition
    //Add another citizenship, if need any extra padding
    val isToBeAddExtraPadding = user?.citizenship?.isPresident == true

    Column(
        modifier = modifier
            .clip(shape = RoundedCornerShape(size = if (isToBeAddExtraPadding) 0.dp else 8.dp))
            //Adding the drawable background here
            .drawBehind {
                with(receiver = image) {
                    draw(size = <EMAIL>)
                }
            }
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .padding(top = if (isToBeAddExtraPadding) 32.dp else dimensionResource(id = R.dimen.activity_margin), end = 14.dp)
                .padding(horizontal = if (isToBeAddExtraPadding) 20.dp else 0.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            //ID card text and background
            IDCardTitleView(
                isFullTextVisible = !isPresident,
                background = user?.citizenship?.setIDCardTextBackground(),
                textColor = user?.citizenship?.setupIDCardTextColor()
            )
            Spacer(
                modifier = Modifier.fillMaxWidth().weight(weight = 1F)
            )
            //The citizenship is showing here
            Column(
                horizontalAlignment = Alignment.End
            ) {
                user?.citizenship?.displayText()?.let {
                    val updatedText = if(user.citizenship.isPresident) stringResource(id = it).uppercase(locale = getDefault()) else stringResource(id = it)
                    Text(
                        text = updatedText,
                        style = FlashatComposeTypography.defaultType.subtitle1.copy(fontSize = 18.sp),
                        color = colorResource(id = user.citizenship.setupCitizenshipTextColor())
                    )
                }
                //Use this if need description below the citizenship
                //Currently used in Social Affairs
                userDescription?.let {
                    Text(
                        text = it,
                        style = FlashatComposeTypography.overLineSmaller,
                        color = colorResource(id = user?.citizenship.setupCitizenshipTextColor())
                    )
                }
            }
        }
        //Name, Id code, etc... other details and the User image view
        Row(
            modifier = Modifier
                .padding(top = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            //Name, ID Code, Nickname, flashat age, flag text and description view
            Column(
                modifier = Modifier
                    .fillMaxWidth().weight(weight = 1F)
                    .padding(start = if (isToBeAddExtraPadding) 32.dp else dimensionResource(id = R.dimen.activity_margin)),
                verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
            ) {
                IDCardTitleDescription(
                    title = R.string.common_name,
                    description = user?.name,
                    textColor = labelTextColor
                )
                IDCardTitleDescription(
                    title = R.string.id_card_title_id_code,
                    description = user?.username,
                    textColor = labelTextColor
                )
                if (isCurrentUser != true) {
                    IDCardTitleDescription(
                        title = R.string.title_id_card_nick_name,
                        description = nickName,
                        textColor = labelTextColor
                    )
                }
                IDCardTitleDescription(
                    title = R.string.id_card_title_flashat_age,
                    description = "${user?.dage ?: 0}",
                    textColor = labelTextColor
                )
                IDCardTitleDescription(
                    title = R.string.id_card_title_flag,
                    description = null,
                    flag = countryFlag,
                    textColor = labelTextColor
                )
            }
            //User image view
            UserImageBadgeAndCountryCodeView(
                modifier = Modifier.padding(
                    start = dimensionResource(id = R.dimen.line_spacing),
                    end = if (isToBeAddExtraPadding) 28.dp else 14.dp
                ),
                userImage = user?.thumbnail,
                userImageSize = 73.dp,
                userBadgeSize = 20.dp,
                userType = if (user?.premiumUser == true) UserBadge.PREMIUM else null
            )
        }
        //shows the issued date and expiry date
        Row(
            modifier = Modifier
                .padding(
                    end = 28.dp,
                    bottom = if (isToBeAddExtraPadding) 32.dp else 16.dp
                ).align(alignment = Alignment.End)
        ) {
            DateView(
                title = R.string.id_card_title_issue_date,
                date  = user?.issueDate,
                dateColor = dateTextColor,
                titleTextColor = dateTitleTextColor
            )
            if (user?.citizenship?.isFreeType == false) {
                CustomHorizontalSpacer(
                    space = dimensionResource(id = R.dimen.activity_margin)
                )
                DateView(
                    title = R.string.id_card_title_expiry_date,
                    date  = user.subscriptionExpiryDate,
                    dateColor = dateTextColor,
                    titleTextColor = dateTitleTextColor
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun CommonIDCardPreview() {
    val dummyData = OtherUser(
        id = 1,
        membership = UserType.PREMIUM,
        citizenship = UserCitizenship.PRESIDENT,
        name = "Abijith",
        issueDate = "11/10/2025",
        subscriptionExpiryDate = "20/20/2025",
        dage = 12,
        username = "Abi",
        countryCode = "IN",
        isSuperstar = false,
        verified = true,
        followerType = FollowerType.FAN,
        isStar = true,
        online = true
    )
    IDCardView(
        user = dummyData,
        nickName = "jith",
        countryFlag = R.drawable.flag_us_islands,
        isCurrentUser = false
    )
}


@Composable
private fun IDCardShimmerView(
    brush: Brush
) {
    Column(
        modifier = Modifier
            .height(height = 180.dp)
            .clip(shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing)))
            .background(color = colorResource(id = R.color.colorSurfaceSecondary))
            .fillMaxWidth()
    ) {
        ShimmerDefaultItem(
            modifier = Modifier.padding(top = dimensionResource(id = R.dimen.activity_margin)),
            brush = brush,
            fraction = 0.35F,
            height = 22.dp,
            shape = RoundedCornerShape(
                topEnd = dimensionResource(id = R.dimen.element_spacing),
                bottomEnd = dimensionResource(id = R.dimen.element_spacing)
            )
        )
        Row(
            modifier = Modifier
                .padding(top = dimensionResource(id = R.dimen.activity_margin))
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            //Name, ID Code, Nickname, flashat age, flag text and description view
            Column(
                modifier = Modifier
                    .fillMaxWidth().weight(weight = 1F)
                    .padding(start = dimensionResource(id = R.dimen.activity_margin)),
                verticalArrangement = Arrangement.spacedBy(space = 14.dp)
            ) {
                repeat(times = 4) {
                    ShimmerDefaultItem(
                        brush = brush,
                        height = 16.dp,
                        fraction = 0.7F
                    )
                }
            }
            ShimmerDefaultCircleItem(
                modifier = Modifier.padding(end = 20.dp),
                brush = brush,
                size = 85.dp
            )
        }
    }
}

/**
 Used for displaying the Identity Card Text.
 * @param background - Set background here
 * @param isFullTextVisible - if [true] the text will be "Identity Card" otherwise "ID CARD"
 */
@Composable
private fun IDCardTitleView(
    isFullTextVisible: Boolean = true,
    @DrawableRes background: Int? = null,
    @ColorRes textColor: Int?
) {
    val text = if (isFullTextVisible) stringResource(id = R.string.id_card_header_identity_card)
    else stringResource(id = R.string.title_id_card).uppercase(locale = getDefault())

    Box(
        modifier = Modifier.width(intrinsicSize = IntrinsicSize.Max).height(intrinsicSize = IntrinsicSize.Max),
        contentAlignment = Alignment.CenterStart
    ) {
        background?.let {
            Image(
                painter = painterResource(id = background),
                modifier = Modifier.fillMaxWidth().fillMaxHeight(),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
        }
        Text(
            text = text,
            style = FlashatComposeTypography.defaultType.subtitle1,
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = dimensionResource(id = R.dimen.element_spacing), end = dimensionResource(id = R.dimen.extra_margin))
                .padding(vertical = dimensionResource(id = R.dimen.line_spacing)),
            color = colorResource(id = textColor ?: R.color.colorPrimary)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun IDCardTitlePreview() {
    IDCardTitleView(
        background = R.drawable.bg_label_idcard_citizen_ambassador,
        textColor = R.color.white
    )
}

/**
 Used for displaying the title, description view of id card.
 If need to show flag instead of description, make description to null,
 and provide the corresponding flag.
 */
@Composable
private fun IDCardTitleDescription(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    description: String?,
    textColor: Color,
    @DrawableRes flag: Int? = null,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(weight = 0.4F),
            text = stringResource(id = title),
            style = FlashatComposeTypography.defaultType.overline,
            color = textColor
        )
        Text(
            text = ":",
            style = FlashatComposeTypography.defaultType.overline,
            color = textColor
        )
        Row(
            modifier = Modifier
                .padding(start = dimensionResource(id = R.dimen.line_spacing))
                .weight(weight = 0.7F)
        ) { 
            description?.let {
                Text(
                    text = description,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = FlashatComposeTypography.overLineBold,
                    color = textColor
                )
            }
            flag?.let {
                AsyncImage(
                    model = ImageRequest
                        .Builder(context = LocalContext.current)
                        .crossfade(enable = true)
                        .data(data = it)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier.size(width = 18.dp, height = 12.dp)
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun IDCardTitleDescriptionPreview() {
    IDCardTitleDescription(
        title = R.string.common_name,
        description = null,
        textColor = colorResource(id = R.color.black),
        flag = R.drawable.flag_east_timor
    )
}

/**
Used for displaying issue date and expiry date
 */
@Composable
private fun DateView(
    modifier: Modifier = Modifier,
    @StringRes title: Int,
    titleTextColor: Color,
    date: String?,
    dateColor: Color,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = title),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = FlashatComposeTypography.overLineSmaller,
            color = titleTextColor
        )
        CustomVerticalSpacer(
            space = dimensionResource(id = R.dimen.line_spacing)
        )
        Text(
            text = date ?: "",
            style = FlashatComposeTypography.overLineSmallerBold,
            color = dateColor
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun DatePreview() {
    DateView(
        title = R.string.id_card_title_issue_date,
        date = "01/01/2022",
        titleTextColor = colorResource(id = R.color.textColorPrimary),
        dateColor = colorResource(id = R.color.textColorPrimary)
    )
}

@DrawableRes
private fun UserCitizenship.setIDCardTextBackground() = when(this) {
    UserCitizenship.VISITOR -> R.drawable.bg_idcard_resident_white
    UserCitizenship.RESIDENT -> R.drawable.bg_idcard_resident_white
    UserCitizenship.CITIZEN -> R.drawable.bg_label_idcard_citizen_ambassador
    UserCitizenship.OFFICER -> R.drawable.bg_label_idcard_citizen_ambassador
    UserCitizenship.AMBASSADOR -> R.drawable.bg_label_idcard_citizen_ambassador
    UserCitizenship.MINISTER -> R.drawable.bg_label_idcard_leader
    UserCitizenship.PRESIDENT -> R.drawable.bg_label_idcard_president
    UserCitizenship.GOLDEN -> R.drawable.bg_label_idcard_president
}

@ColorRes
private fun UserCitizenship?.setupIDCardTextColor() = when(this) {
    UserCitizenship.VISITOR -> R.color.textColorOnSecondary
    UserCitizenship.RESIDENT -> R.color.textColorOnSecondary
    UserCitizenship.CITIZEN -> R.color.colorHuddleTagCitizen
    UserCitizenship.OFFICER -> R.color.colorHuddleTagCitizen
    UserCitizenship.AMBASSADOR -> R.color.colorHuddleTagCitizen
    UserCitizenship.MINISTER -> R.color.colorPrimary
    UserCitizenship.PRESIDENT -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.GOLDEN -> R.color.colorPrimaryColorDarkest1
    null -> R.color.colorPrimary
}

@ColorRes
private fun UserCitizenship?.setupCitizenshipTextColor() = when(this) {
    UserCitizenship.VISITOR -> R.color.textColorAlwaysLightPrimary
    UserCitizenship.RESIDENT -> R.color.textColorOnPrimary
    UserCitizenship.CITIZEN -> R.color.colorPrimary
    UserCitizenship.OFFICER -> R.color.colorPrimary
    UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.MINISTER -> R.color.textColorOnPrimary
    UserCitizenship.PRESIDENT -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.GOLDEN -> R.color.colorPrimaryColorDarkest1
    null -> R.color.colorPrimary
}

@ColorRes
private fun UserCitizenship?.setupLabelTextColor() = when(this) {
    UserCitizenship.VISITOR -> R.color.textColorOnSecondary
    UserCitizenship.RESIDENT -> R.color.textColorOnPrimary
    UserCitizenship.CITIZEN -> R.color.colorPrimary
    UserCitizenship.OFFICER -> R.color.colorPrimary
    UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.MINISTER -> R.color.textColorOnPrimary
    UserCitizenship.PRESIDENT -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.GOLDEN -> R.color.colorPrimaryColorDarkest1
    null -> R.color.colorPrimary
}

@ColorRes
private fun UserCitizenship?.setupDateTitleColor() = when(this) {
    UserCitizenship.VISITOR -> R.color.textColorOnSecondary
    UserCitizenship.RESIDENT -> R.color.textColorOnPrimary
    UserCitizenship.CITIZEN -> R.color.colorPrimary
    UserCitizenship.OFFICER -> R.color.colorPrimary
    UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.MINISTER -> R.color.textColorOnPrimary
    UserCitizenship.PRESIDENT -> R.color.colorPresidentIDText
    UserCitizenship.GOLDEN -> R.color.colorPresidentIDText
    null -> R.color.colorPrimary
}

@ColorRes
private fun UserCitizenship?.setupDateTextColor() = when(this) {
    UserCitizenship.VISITOR -> R.color.textColorOnSecondary
    UserCitizenship.RESIDENT -> R.color.textColorOnPrimary
    UserCitizenship.CITIZEN -> R.color.colorPrimary
    UserCitizenship.OFFICER -> R.color.colorPrimary
    UserCitizenship.AMBASSADOR -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.MINISTER -> R.color.textColorOnPrimary
    UserCitizenship.PRESIDENT -> R.color.colorPrimaryColorDarkest1
    UserCitizenship.GOLDEN -> R.color.colorPrimaryColorDarkest1
    null -> R.color.colorPrimary
}

@DrawableRes
private fun UserCitizenship?.setIdCardBackground() = when(this) {
    UserCitizenship.VISITOR -> R.drawable.bg_idcard_visitor
    UserCitizenship.RESIDENT -> R.drawable.bg_idcard_resident
    UserCitizenship.CITIZEN -> R.drawable.bg_idcard_citizen
    UserCitizenship.OFFICER -> R.drawable.bg_idcard_officer
    UserCitizenship.AMBASSADOR -> R.drawable.bg_idcard_ambassador
    UserCitizenship.MINISTER -> R.drawable.bg_idcard_leader
    UserCitizenship.PRESIDENT -> R.drawable.bg_idcard_president
    UserCitizenship.GOLDEN -> R.drawable.bg_idcard_president
    null -> R.drawable.bg_idcard_visitor
}

private fun getAchievementList(
    context: Context,
    user: OtherUser?
) : List<List<Achievement>> {

    val firstScreenItem = listOf(
        Achievement(icon = R.drawable.ic_idcard_lv, title = R.string.id_card_generosity_level, value = user?.lvModified),
        Achievement(icon = R.drawable.ic_id_card_gift, title = R.string.id_card_total_gift_given, value = "0" /*Need to get this value*/),
        Achievement(icon = R.drawable.ic_gift_given, title = R.string.id_card_total_gift_given_value, value = context.getString(R.string.podium_buy_camera_required_flix, "0") /*Need to get this value*/),
        Achievement(icon = R.drawable.ic_idcard_pl, title = R.string.id_card_skills_level, value = user?.plModified),
        Achievement(icon = R.drawable.ic_id_card_member, title = R.string.id_card_members_in_huddle, value = "${user?.totalHuddlesParticipant ?: 0}"),
    )
    val secondScreenItem = listOf(
        Achievement(icon = R.drawable.ic_id_card_member, title = R.string.id_card_tribe_members, value = "${user?.tribeParticipantsCount ?: 0}"),
        Achievement(icon = R.drawable.ic_id_card_member, title = R.string.id_card_citizen_count, value = "0" /*Need to get this value*/),
        Achievement(icon = R.drawable.ic_id_card_member, title = R.string.id_card_officers_count, value = "0" /*Need to get this value*/),
        Achievement(icon = R.drawable.ic_id_card_member, title = R.string.id_card_ambassador_count, value = "0" /*Need to get this value*/),
        Achievement(icon = R.drawable.ic_id_card_member, title = R.string.id_card_ministers_count, value = "0" /*Need to get this value*/),
    )
    val thirdScreenItem = listOf(
        Achievement(icon = R.drawable.ic_podium_statistics, title = R.string.id_card_podium_statistics, value = "${user?.totalPublicPodiums ?: 0}"),
        Achievement(icon = R.drawable.ic_huddle_count, title = R.string.id_card_huddle_count, value = "${user?.totalHuddleOwned ?: 0}"),
        Achievement(icon = R.drawable.ic_postat_count, title = R.string.id_card_postat_count, value = "${user?.totalPostatPosts ?: 0}"),
        Achievement(icon = R.drawable.ic_flash_count, title = R.string.id_card_flash_count, value = "${user?.totalFlashPublished ?: 0}")
    )

    return listOf(
        firstScreenItem, secondScreenItem, thirdScreenItem
    )
}
