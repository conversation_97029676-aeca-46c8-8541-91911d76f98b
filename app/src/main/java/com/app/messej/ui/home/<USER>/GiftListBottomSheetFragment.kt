package com.app.messej.ui.home.gift

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.core.os.bundleOf
import androidx.fragment.app.setFragmentResult
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.api.gift.GiftItem
import com.app.messej.data.model.enums.GiftContext
import com.app.messej.ui.utils.FragmentExtensions.downloadAndShowGift
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe


class GiftListBottomSheetFragment : GiftListBottomSheetBaseFragment() {
    val args: GiftListBottomSheetFragmentArgs by navArgs()
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    companion object {
        const val GIFT_REQUEST_KEY = "giftRequest"
        const val GIFT_REQUEST_PAYLOAD = "giftPayLoad"
        const val CONTEXT_ID ="context_ID"
    }

    private fun setup() {
        viewModel.setArgs(args.receiverId,args.birthday,args.challengeId,args.userLevelCongrats)
        setUpTabData()
//        viewModel.loadGift(args.giftContext)
    }

    private fun observe() {
        viewModel.onGiftSent.observe(viewLifecycleOwner) { item ->
            setFragmentResult(
                GIFT_REQUEST_KEY, bundleOf(
                    GIFT_REQUEST_PAYLOAD to item.copy(podiumId =if(args.giftContext == GiftContext.GIFT_PODIUM) args.giftContextId else null, challengeId = args.challengeId), CONTEXT_ID to args.giftContextId
                )
            )
            giftCommonViewModel.getGiftList()
            if (item.hasVideo) {
                findNavController().popBackStack()
                downloadAndShowGift(item)
            } else {
                val action = GiftListBottomSheetFragmentDirections.actionGlobalNotificationLottieBottomSheetFragment(item.id, args.receiverId, false)
                val options = NavOptions.Builder().setPopUpTo(R.id.giftListFragment, inclusive = true).build()
                findNavController().navigateSafe(action, options)
//                Toast.makeText(requireContext(), getString(R.string.gift_sent_successfully, viewModel.nameOrNickname.value), Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onGiftItemClick(item: GiftItem, preview:Boolean) {
        // podiumId, challengeEndTimeStampUTC and challengeId needs only for challenge case, all other case pass null
//        val podiumId = if (!args.podiumId.isNullOrBlank()) args.podiumId else null
        val challengeId = if (!args.challengeId.isNullOrBlank()) args.challengeId else null
        val challengeEndTimeStampUTC = if (args.challengeEndTimeStampUTC > 0L && challengeId != null) args.challengeEndTimeStampUTC else null
        val managerId = if (args.managerId != -1) args.managerId else null
        val giftContextId =if(!args.giftContextId.isNullOrBlank()) args.giftContextId else null

        val podiumId = if(args.giftContext == GiftContext.GIFT_PODIUM) args.giftContextId else null

        Log.d("QQQQ,", "" + preview)
        if(viewModel.isGiftLoading.value==true && preview) return
        viewModel.setGiftLoading(true)
        if (preview) {
            viewModel.sendGiftContent(item, args.receiverId, sendingSource = args.giftContext, challengeId = challengeId, podiumId = podiumId, challengeEndTimeStampUTC = challengeEndTimeStampUTC)
        } else {
            viewModel.sendGift(
                item = item,
                receiverId = args.receiverId,
                challengeId = challengeId,
                podiumId = podiumId,
                challengeEndTimeStampUTC = challengeEndTimeStampUTC,
                sendingSource = args.giftContext,
                sendingSourceId = giftContextId,
                managerId = args.managerId
            )
        }
    }

}