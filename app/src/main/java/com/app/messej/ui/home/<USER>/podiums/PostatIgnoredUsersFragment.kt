package com.app.messej.ui.home.publictab.podiums

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.PostatMentionedUser
import com.app.messej.databinding.FragmentPostatIgnoredUsersBinding
import com.app.messej.databinding.LayoutListStateEmptyBinding
import com.app.messej.ui.home.publictab.postat.PostatBlockedUsersListAdapter
import com.app.messej.ui.home.publictab.postat.PostatIgnoredUsersViewModel
import com.app.messej.ui.utils.FragmentExtensions.confirmAction
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.kennyc.view.MultiStateView


class PostatIgnoredUsersFragment : Fragment() {

    private val viewModel: PostatIgnoredUsersViewModel by viewModels()
    private var postatBlockedUsersListAdapter: PostatBlockedUsersListAdapter? = null
    private lateinit var binding: FragmentPostatIgnoredUsersBinding

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_postat_ignored_users, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }
    private fun setup() {
        setEmptyView()
        initAdapter()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity?)?.setupActionBar(binding.customActionBar.toolbar)
        val actionBar = (activity as MainActivity?)?.supportActionBar
        binding.customActionBar.toolbar.title = getString(R.string.postat_ignored_users_title)
    }

    private fun initAdapter() {
        postatBlockedUsersListAdapter = PostatBlockedUsersListAdapter(object : PostatBlockedUsersListAdapter.PostatActionListener {
            override fun onActionButtonClicked(view: View, user: PostatMentionedUser) {
                confirmAction(
                    message = R.string.postat_ignore_user_unblock_dialog
                ) {
                    viewModel.ignorePostatUser(user,false)
                }
            }

        }, resources.getString(R.string.common_unblock))

        binding.blockedUsers.apply {
            layoutManager = LinearLayoutManager(context)
            setHasFixedSize(true)
            adapter = postatBlockedUsersListAdapter
        }
        postatBlockedUsersListAdapter?.apply {
            addLoadStateListener { loadState ->
                binding.multiStateView.viewState = if (itemCount < 1 && loadState.source.refresh is LoadState.Loading) MultiStateView.ViewState.LOADING
                else if (loadState.append.endOfPaginationReached) {
                    if (itemCount < 1) {
                        MultiStateView.ViewState.EMPTY
                    } else {
                        MultiStateView.ViewState.CONTENT
                    }
                } else {
                    MultiStateView.ViewState.CONTENT
                }
            }
        }
    }
    private var emptyViewBinding: LayoutListStateEmptyBinding? = null

    private fun setEmptyView() {
        try {
            val empty = DataBindingUtil.inflate<LayoutListStateEmptyBinding>(layoutInflater, R.layout.layout_list_state_empty, binding.multiStateView, false)
            binding.multiStateView.setViewForState(empty.root, MultiStateView.ViewState.EMPTY, false)
            emptyViewBinding = empty
            emptyViewBinding?.apply {
                edsEmptyMessage.text = resources.getString(R.string.postat_blocked_user_eds_message)
            }
            Log.w("BCDFLSL", "setEmptyView: empty view binding has been set!!")
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            Log.e("BCDFLSL", "setEmptyView: ")
        }
    }

    private fun observe() {

        viewModel.onPostatUserIgnored.observe(viewLifecycleOwner){
            Toast.makeText(requireContext(), getString(R.string.postat_user_unblocked_text, it), Toast.LENGTH_SHORT).show()
            postatBlockedUsersListAdapter?.refresh()
        }
        viewModel.blockedUserList.observe(viewLifecycleOwner){
            it?: return@observe
            postatBlockedUsersListAdapter?.submitData(viewLifecycleOwner.lifecycle, it)
        }
    }

}