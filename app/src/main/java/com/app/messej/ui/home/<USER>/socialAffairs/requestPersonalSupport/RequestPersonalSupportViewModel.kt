package com.app.messej.ui.home.publictab.socialAffairs.requestPersonalSupport

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.MainApplication
import com.app.messej.R
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.SocialProofMedia
import com.app.messej.data.model.api.socialAffairs.PersonalSupportRequest
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.api.socialAffairs.SocialVoteErrorResponse.SocialError
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.MySocialSupportActionMenuItem
import com.app.messej.data.model.enums.SocialCaseStatus
import com.app.messej.data.repository.SocialAffairsRepository
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class RequestPersonalSupportViewModel(application: Application) : AndroidViewModel(application = application) {

    private val socialAffairRepo = SocialAffairsRepository(context = application)

    val amountNeededTextFieldState = ComposeTextFieldState()
    val amountNeededTextLiveData = MutableLiveData<String?>()

    val titleTextFieldState = ComposeTextFieldState()
    val titleTextLiveData = MutableLiveData<String?>()

    val writeDetailsTextFieldState = ComposeTextFieldState()
    val writeDetailsTextLiveData = MutableLiveData<String?>()

    val uploadTextFieldState = ComposeTextFieldState()

    private val _isTermsAccepted = MutableLiveData<Boolean>()
    val isTermsAccepted: LiveData<Boolean> = _isTermsAccepted

    private val _isSubmitting = MutableLiveData<Boolean>()
    val isSubmitting: LiveData<Boolean> = _isSubmitting

    val requestCaseError = LiveEvent<SocialError?>()
    val errorMessage = LiveEvent<String?>()
    val fileUploadErrorMessage = LiveEvent<Int>()
    val isRequestedForPersonalSupport = LiveEvent<Boolean>()
    val isSavedToDrafts = LiveEvent<Boolean>()

    private val _proofFiles = MutableLiveData<List<SocialProofMedia>>()
    val proofFiles: LiveData<List<SocialProofMedia>> = _proofFiles

    private val _mediaEncode = MutableLiveData<MediaTransfer?>(null)
    private var requestSupportOngoingJob: Job? = null

    fun setAmountTextLiveData(value: String?) {
        amountNeededTextLiveData.postValue(value)
    }
    fun setTitleTextLiveData(value: String?) {
        titleTextLiveData.postValue(value)
    }
    fun setWriteDetailTextLiveData(value: String?) {
        writeDetailsTextLiveData.postValue(value)
    }
    fun setTermsAccepted(isAccepted: Boolean) {
        _isTermsAccepted.postValue(isAccepted)
    }

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    fun setLoading(isLoading: Boolean) {
        _isLoading.postValue(isLoading)
    }

    //_caseDetail is used for editing the case
    private val _caseDetail = MutableLiveData<SocialCaseInfo?>(null)
    val caseDetail : LiveData<SocialCaseInfo?> = _caseDetail
    fun getPersonalRequestDetails(caseId: String) {
        viewModelScope.launch(context = Dispatchers.IO) {
            setLoading(true)
            val response = socialAffairRepo.getCaseInfo(id = caseId)
            when(response) {
                is ResultOf.Success -> {
                    prefillDetails(caseInfo = response.value.data)
                    _caseDetail.postValue(response.value.data)
                }
                is ResultOf.APIError -> {
                    errorMessage.postValue(response.error.message)
                }
                is ResultOf.Error -> {
                    errorMessage.postValue(response.errorMessage())
                }
            }
            setLoading(false)
        }
    }

    private fun prefillDetails(caseInfo: SocialCaseInfo?) {
        val amountRequested = caseInfo?.amountRequested.formatDecimalWithRemoveTrailingZeros()
        amountNeededTextFieldState.text = amountRequested
        setAmountTextLiveData(value = amountRequested)

        titleTextFieldState.text = caseInfo?.caseTitle
        setTitleTextLiveData(value = caseInfo?.caseTitle)

        writeDetailsTextFieldState.text = caseInfo?.caseDescription
        setWriteDetailTextLiveData(value = caseInfo?.caseDescription)

        setTermsAccepted(isAccepted = true)

        //Adding list of proof files from the detail api.
        //It is used only for displaying file name and extension.
        //Do not use it for uploading it to the backend
        //No need to send this proof files on editing case.
        //If case is editing, delete all existing proof files
        // and add original files from the device
        caseInfo?.proofFiles?.map {
            val fileName = it.substringAfterLast('/')
            val mimeType = ""
            SocialProofMedia(
                uri = Uri.EMPTY,
                name = fileName,
                mimeType = mimeType
            )
        }?.let { proofList -> _proofFiles.postValue(proofList) }
    }

    fun isProofFilesLoadedFromApiDetails() : Boolean {
        //Checks whether the proof files contains at least one item with Uri.EMPTY
        //If it is true, that means the proof file is loaded from the detail api. (Not added from the device)
        //If need to add new files from the device, we remove all proof files, and add new proof files from the device
        return _proofFiles.value?.any { it.uri == Uri.EMPTY } == true
    }

    fun deleteAllProofFiles() {
        _proofFiles.postValue(emptyList())
    }

    fun addProofFile(uri: Uri) {
        val contentResolver = getApplication<MainApplication>().contentResolver
        val mime = MediaUtils.getMimeTypeFromUri(uri,contentResolver).orEmpty()
        val meta = MediaUtils.getFileNameAndSizeFromUri(uri, contentResolver)
        /*
        By default, the system grants your app access to media files until the device is restarted or until your app stops.
        If your app performs long-running work, such as uploading a large file in the background,
        you might need this access to be persisted for a longer period of time
         */
        try {
            contentResolver.takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        } catch (_: Exception) {
        }
        viewModelScope.launch(Dispatchers.IO) {
            _proofFiles.add(
                SocialProofMedia(
                    uri = uri,
                    name = meta?.first.orEmpty(),
                    mimeType = mime
                )
            )
        }
    }

    private fun MutableLiveData<List<SocialProofMedia>>.add(file: SocialProofMedia) {
        val tempList = value?.toMutableList() ?: mutableListOf()
        if (tempList.find { it.uriString == file.uriString } != null) return
        tempList.add(file)
        postValue(tempList)
    }

    fun deleteFile(file: SocialProofMedia) {
        _proofFiles.remove(file)
    }

    private fun MutableLiveData<List<SocialProofMedia>>.remove(file: SocialProofMedia) {
        val tempList = value?.toMutableList() ?: mutableListOf()
        tempList.remove(file)
        postValue(tempList)
    }

    val isSubmitButtonEnabled : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun checkInputsValid() {
            med.postValue(
                !amountNeededTextLiveData.value.isNullOrBlank() &&
                        !titleTextLiveData.value.isNullOrBlank() &&
                        !writeDetailsTextLiveData.value.isNullOrBlank() &&
                        !_proofFiles.value.isNullOrEmpty() &&
                        _isTermsAccepted.value == true &&
                        _isLoading.value != true &&
                        _isSubmitting.value != true &&
                        isAmountRequestedIsValid.value == true

            )
        }
        med.addSource(amountNeededTextLiveData) { checkInputsValid() }
        med.addSource(titleTextLiveData) { checkInputsValid() }
        med.addSource(writeDetailsTextLiveData) { checkInputsValid() }
        med.addSource(_proofFiles) { checkInputsValid() }
        med.addSource(_isTermsAccepted) { checkInputsValid() }
        med.addSource(_isLoading) { checkInputsValid() }
        med.addSource(_isSubmitting) { checkInputsValid() }
        med.addSource(isAmountRequestedIsValid) { checkInputsValid() }
        med
    }

    fun isProofFilesChanged(): Boolean {
        val proofFilesFromApi = _caseDetail.value?.proofFiles?.map { it.substringAfterLast('/') }
        val addedProofFiles = _proofFiles.value?.map { it.name }
        return proofFilesFromApi != addedProofFiles
    }

    val isDraftButtonEnabled : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun checkInputsValid() {
            //when case is in draft case, only enable draft button if any one of field edit.
            //If no fields edited, then no need to enable draft button
            val isValuesChanged = if (_caseDetail.value?.id != null) {
                val newUpdatedValues = _caseDetail.value?.copy(
                    amountRequested = amountNeededTextFieldState.text?.toDoubleOrNull(),
                    caseTitle = titleTextFieldState.text,
                    caseDescription = writeDetailsTextFieldState.text
                )
                newUpdatedValues != _caseDetail.value || isProofFilesChanged()
            } else true

            med.postValue(
                isSubmitButtonEnabled.value == true &&
                        _caseDetail.value?.status != SocialCaseStatus.NEW &&
                        _caseDetail.value?.status != SocialCaseStatus.ACTIVE && isValuesChanged
            )
        }
        med.addSource(amountNeededTextLiveData) { checkInputsValid() }
        med.addSource(titleTextLiveData) { checkInputsValid() }
        med.addSource(writeDetailsTextLiveData) { checkInputsValid() }
        med.addSource(_proofFiles) { checkInputsValid() }
        med.addSource(_isTermsAccepted) { checkInputsValid() }
        med.addSource(_isLoading) { checkInputsValid() }
        med.addSource(_isSubmitting) { checkInputsValid() }
        med.addSource(isAmountRequestedIsValid) { checkInputsValid() }
        med.addSource(isSubmitButtonEnabled) { checkInputsValid() }
        med.addSource(_caseDetail) { checkInputsValid() }
        med
    }


    val isAmountRequestedIsValid : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun checkInputsValid() {
            val amount = amountNeededTextLiveData.value?.toDoubleOrNull() ?: 0.0
            med.postValue((amount % 100.00 == 0.0) && (amount != 0.0))
        }
        med.addSource(amountNeededTextLiveData) { checkInputsValid() }
        med
    }

    val canEditAmountField : MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun checkInputsValid() {
            val isCaseHaveAtLeastOneVote = ((_caseDetail.value?.supportVotes ?: 0) != 0) || ((_caseDetail.value?.opposedVotes ?: 0) != 0)
            med.postValue(_isSubmitting.value != true && _isLoading.value != true && !isCaseHaveAtLeastOneVote)
        }
        med.addSource(_isSubmitting) { checkInputsValid() }
        med.addSource(_isLoading) { checkInputsValid() }
        med.addSource(_caseDetail) { checkInputsValid() }
        med
    }

    fun requestOrEditSupport(status: MySocialSupportActionMenuItem? = null) {
        viewModelScope.launch(Dispatchers.IO) {
            _isSubmitting.postValue(true)

            val request = PersonalSupportRequest(
                amountRequested = amountNeededTextFieldState.text,
                caseTitle = titleTextFieldState.text,
                caseDetails = writeDetailsTextFieldState.text
            )

            //Skipping proof files uploading if the proof files loaded from the api.
            if (!isProofFilesLoadedFromApiDetails()) {
                val proofs = _proofFiles.value.orEmpty()
                proofs.forEach {
                    if (!it.isProcessed) {
                        try {
                            Log.w("RSV", "process media: $it")
                            processMedia(it)
                        } catch(e: Exception) {
                            return@launch
                        }
                    }
                    if (it.mediaUploaded) return@forEach

                    Log.w("RSV", "upload media: $it")
                    when(socialAffairRepo.uploadMedia(it)) {
                        is ResultOf.Success -> { }
                        else -> {
                            fileUploadErrorMessage.postValue(R.string.default_file_upload_failed)
                            _isSubmitting.postValue(false)
                            return@launch
                        }
                    }
                }
                if (!proofs.all { it.isProcessed } || !proofs.all { it.mediaUploaded }) {
                    fileUploadErrorMessage.postValue(R.string.default_file_upload_failed)
                    _isSubmitting.postValue(false)
                    return@launch
                }
                request.proofFiles = proofs.map { it.s3UploadMedia.key }
            }

            //Choosing whether edit, adding new or save draft
            val isEditRequest = _caseDetail.value?.id != null
            val result = if (isEditRequest) socialAffairRepo.editPersonalSupportCase(
                //if case have at least one vote, Making amount null. other wise pass the amount value.
                request = request.copy(amountRequested = if (canEditAmountField.value == true) amountNeededTextFieldState.text else null),
                status = status,
                id = "${_caseDetail.value?.id}"
            )
            else socialAffairRepo.requestSupport(req = request.copy(status = status?.serializedName()))

            when(result) {
                is SocialAffairsRepository.SocialResultOf.Success -> {
                    if (status == MySocialSupportActionMenuItem.Draft) {
                        isSavedToDrafts.postValue(true)
                    } else isRequestedForPersonalSupport.postValue(true)
                }
                is SocialAffairsRepository.SocialResultOf.APIError -> {
                    if (result.code == 403 && result.error.result?.reason != null) {
                        requestCaseError.postValue(result.error.result)
                    } else errorMessage.postValue(result.error.message)
                }
                is SocialAffairsRepository.SocialResultOf.Error -> {
                    errorMessage.postValue(result.exception.message)
                }
            }
            _isSubmitting.postValue(false)
        }
    }

    val onDeleteCaseSuccessMessage = LiveEvent<Int>()
    fun deleteActiveOrArchiveCase(activeCaseId: String, isArchiveCase: Boolean?) {
        viewModelScope.launch(context = Dispatchers.IO) {
            _isSubmitting.postValue(true)
            val response = socialAffairRepo.editPersonalSupportCase(
                id = activeCaseId, status = MySocialSupportActionMenuItem.Delete
            )
            when(response) {
                is SocialAffairsRepository.SocialResultOf.Success -> {
                    onDeleteCaseSuccessMessage.postValue(
                        if (isArchiveCase == true) R.string.social_archive_case_delete_message
                        else R.string.social_active_case_delete_message
                    )
                    //After deleting the active case, submit the case from "draft" to "new
                    requestOrEditSupport()
                }
                is SocialAffairsRepository.SocialResultOf.APIError -> {
                    errorMessage.postValue(response.error.message)
                }
                is SocialAffairsRepository.SocialResultOf.Error -> {
                    errorMessage.postValue(response.exception.message)
                }
            }
            _isSubmitting.postValue(false)
        }
    }

    private suspend fun processMedia(med: SocialProofMedia) = withContext(Dispatchers.IO) {
        requestSupportOngoingJob?.let {
            requestSupportOngoingJob = null
            if (it.isActive) it.cancelAndJoin()
        }
        launch {
            try {
                val transfer = MediaTransfer(med.uuid)
                _mediaEncode.postValue(transfer)
                if (med.processedFile != null) return@launch
                med.processedFile = when (med.mediaType) {
                    MediaType.IMAGE -> processImage(med)
                    MediaType.VIDEO -> processVideo(med, object : VideoEncoderUtil.MediaProcessingListener {
                        override fun onProgress(progress: Int) {
                            Log.d("ENCODE", "Progress: $progress%")
                            transfer.progress = progress
                        }

                        override fun onProcessingFinished(success: Boolean) {
                            Log.d("ENCODE", "Encode Done ($success) as per VM")
                        }
                    })
                    else -> null
                }
                _mediaEncode.postValue(null)
            } catch (e: Throwable) {
                Log.w("ENCODE", "ProcessVideo cancelled", e)
                Firebase.crashlytics.recordException(e)
                requestSupportOngoingJob = null
                _mediaEncode.postValue(null)
                throw Exception("media processing cancelled")
            }
        }.apply {
            requestSupportOngoingJob = this
            join()
        }
    }

    private suspend fun processImage(med: SocialProofMedia): File = withContext(Dispatchers.IO) {
        Firebase.crashlytics.log("Starting image compress")
        Log.d("ENCODE", "Starting image compress")
        try {
            val imageFile = socialAffairRepo.storeImageUriToTempFile(med.uri)
            val result = socialAffairRepo.compressImage(imageFile)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "Process image cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("image processing cancelled")
        }
    }

    private suspend fun processVideo(med: SocialProofMedia, listener: VideoEncoderUtil.MediaProcessingListener): File = withContext(Dispatchers.IO) {
        Firebase.crashlytics.log("Starting Video compress")
        Log.d("ENCODE", "Starting Video compress")
        try {
            val result = socialAffairRepo.processVideo(med, listener)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "ProcessVideo cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("Video processing cancelled")
        }
    }
}