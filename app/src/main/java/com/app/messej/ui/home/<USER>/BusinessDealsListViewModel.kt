package com.app.messej.ui.home.businesstab

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import androidx.paging.liveData
import androidx.paging.map
import com.app.messej.data.model.api.business.GetRestoreRatingResponse
import com.app.messej.data.model.api.profile.AccountDetailsResponse
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.entity.DealsTransferHistory
import com.app.messej.data.model.entity.NickName
import com.app.messej.data.model.entity.NickName.Companion.findNickName
import com.app.messej.data.model.enums.TransactionTab
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BusinessDealsListViewModel(application: Application) : AndroidViewModel(application) {
    val businessRepo = BusinessRepository(application)
    val accountRepo = AccountRepository(application)
    private val profileRepo = ProfileRepository(application)

    val businessStatement: LiveData<BusinessStatement?> = businessRepo.getStatements()

    val count = businessRepo.getCount()

    private val _currentTab = MutableLiveData<TransactionTab?>(null)
    val currentTab: LiveData<TransactionTab?> = _currentTab.distinctUntilChanged()

    var searchTerm = MutableLiveData("")
    var isDoubleClickPrevent = true


    val _transferType = MutableLiveData<String>("")
    private val transferType: LiveData<String> = _transferType

    private val _nickNames: LiveData<List<NickName>> = profileRepo.getNickNamesLiveData()

    private val _accountDetails = accountRepo.getAccountDetailsFlow().asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)
    val accountDetails: LiveData<AccountDetailsResponse?> = _accountDetails


    val isFlaxRateFull: LiveData<Boolean> = _accountDetails.map { accountDetails ->
        accountDetails?.flaxRatePercentage==100
    }


   private val _transferHistoryList = searchTerm.switchMap { search ->
        if (search.isNullOrEmpty()) businessRepo.getDealsTransferListPager(_transferType.value.toString()).liveData.cachedIn(viewModelScope)
        else businessRepo.getDealsTransferListSearchPager(search, _transferType.value.toString()).liveData.cachedIn(viewModelScope)
    }


    val transferHistoryList: MediatorLiveData<PagingData<DealsTransferHistory>> by lazy {
        val med = MediatorLiveData<PagingData<DealsTransferHistory>>()
        fun update() {
            val data = _transferHistoryList.value?.map { pc ->

                val userId =when(pc.status) {
                    DealsTransferHistory.FlaxStatus.Received -> pc.senderId!!.toInt()
                    DealsTransferHistory.FlaxStatus.Sent -> pc.receiverId!!.toInt()
                    DealsTransferHistory.FlaxStatus.Credited -> 0
                    DealsTransferHistory.FlaxStatus.Debited -> 0
                    null -> 0
                }
                _nickNames.value?.findNickName(userId)?.let {
//                    Log.d("NICKKKKNAME","nick id: $it"+"  Org. name ${pc.name}")
                    pc.name = it
                }
                pc
            }
            med.postValue(data)
        }
        med.addSource(_transferHistoryList) { update() }
        med.addSource(_nickNames) { update() }
        med
    }


    val isSearchMode: LiveData<Boolean> = searchTerm.map {
        return@map it.isNullOrEmpty()
    }


     fun loadBusinessStatement() {

        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<BusinessStatement> = businessRepo.getBusinessStatements()) {
                is ResultOf.Success -> {

                }

                is ResultOf.APIError -> {}
                is ResultOf.Error -> {
                    // TODO show some feedback
                }
            }
        }
    }

    fun setTransferType(type: String) {
        _transferType.value=type
    }

    fun loadAllData() {
        searchTerm.postValue(null)
    }

    fun setCurrentTab(tab: TransactionTab, skipIfSet: Boolean = false) {
        Log.w("TABTRANSACTION", "setCurrentTab: $tab")
        if (skipIfSet && currentTab.value!=null) return
        _currentTab.value = tab
    }
    private val _restoreRatingDetails = MutableLiveData<GetRestoreRatingResponse>(null)
    val restoreRatingDetails: LiveData<GetRestoreRatingResponse> = _restoreRatingDetails

    private val _getRestoreRatingShimmerLoading = MutableLiveData<Boolean>(false)
    val getRestoreRatingShimmerLoading: LiveData<Boolean> = _getRestoreRatingShimmerLoading

    fun getRestoreRating() {
        _getRestoreRatingShimmerLoading.postValue(false)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<GetRestoreRatingResponse> = businessRepo.getRestoreRatingDetails()) {
                is ResultOf.Success -> {
                    _getRestoreRatingShimmerLoading.postValue(true)
                    _restoreRatingDetails.postValue(result.value)
                }
                is ResultOf.APIError -> {
                    _getRestoreRatingShimmerLoading.postValue(false)
                }
                is ResultOf.Error -> {}
            }

        }
    }
    val onRestoreRatingSuccess = LiveEvent<Boolean>()

    fun purchaseRate() {
            viewModelScope.launch(Dispatchers.IO) {
                try {
                    when (val result=businessRepo.postRestoreRating(_restoreRatingDetails.value?.restoratingFlix.toString())) {
                        is ResultOf.Success -> {
                            onRestoreRatingSuccess.postValue(true) }
                        is ResultOf.APIError -> {}
                        is ResultOf.Error -> {}
                    }
                } catch (e: Exception) {
                    Log.d("RestoreRatingLCVM", "restoreRatingException: ${e.message}")
                }
            }
    }


}