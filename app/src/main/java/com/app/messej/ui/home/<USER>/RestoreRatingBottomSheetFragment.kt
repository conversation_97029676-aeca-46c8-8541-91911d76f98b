package com.app.messej.ui.home.businesstab

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.databinding.FragmentRestoreRatingBottomSheetBaseBinding
import com.app.messej.databinding.LayoutPodiumCameraDisabledBinding
import com.app.messej.databinding.LayoutRequiredFlixPopupBinding
import com.app.messej.databinding.LayoutRestoreRatingBinding
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import java.util.Locale

class RestoreRatingBottomSheetFragment : BottomSheetDialogFragment() {

    private lateinit var binding: FragmentRestoreRatingBottomSheetBaseBinding
    private val viewModels: BusinessDealsListViewModel by viewModels()
     private val args: RestoreRatingBottomSheetFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(DialogFragment.STYLE_NORMAL, R.style.Widget_Flashat_GiftBottomSheet)
    }

    companion object {
        const val RESTORE_RATING_SUCCESS_REQUEST = "restore_rating_success_request"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_restore_rating_bottom_sheet_base, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModels
        binding.isResident =args.isResident
        return binding.root
    }

     override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
         super.onViewCreated(view, savedInstanceState)
         setup()
         observe()
     }

     private fun setup() {
         viewModels.getRestoreRating()
         binding.btnClose.setOnClickListener {
             findNavController().popBackStack()
         }
         binding.aboutInfo.setOnClickListener {
             restoreRatingInfo(resources.getString(R.string.restore_rating_note))
         }
         binding.restoreRatingButton.setOnClickListener {
             if(viewModels.restoreRatingDetails.value?.sufficientBalance == true){
                 proceedToRestore(title = getString(R.string.restore_rating_purchase_confirmation,String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.restoratingFlix)))
             } else{
                 if(viewModels.restoreRatingDetails.value?.sufficientEffectiveBalance == true){
                     proceedToRestore(title = getString(R.string.restore_effective_flix_confirmation,String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.flixBalance),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.coinAmount)))
                 }else{
                     showRequiredFlixPopUp()
                 }
             }


         }
     }

    private fun observe(){
       viewModels.onRestoreRatingSuccess.observe(viewLifecycleOwner){
           Toast.makeText(requireContext(), getString(R.string.restore_rating_your_rating_is_restored), Toast.LENGTH_SHORT).show()
           requireActivity().supportFragmentManager.setFragmentResult(RESTORE_RATING_SUCCESS_REQUEST, bundleOf())
           findNavController().popBackStack()
       }
   }

    private fun proceedToRestore(title:String) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutRestoreRatingBinding>(layoutInflater, R.layout.layout_restore_rating, null, false)
            view.viewModel = viewModels
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.nickNameTitle.text = title
            view.nickNameTitle.textSize = 13F
            view.actionProceed.setOnClickListener {
                if(viewModels.isDoubleClickPrevent){
                    viewModels.isDoubleClickPrevent =false
                    viewModels.purchaseRate()
                    dismiss()
                }

            }
            view.actionCancel.setOnClickListener {
                viewModels.isDoubleClickPrevent = true
                dismiss()
            }
        }
    }


    private fun showRequiredFlixPopUp() {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutRequiredFlixPopupBinding>(layoutInflater, R.layout.layout_required_flix_popup, null, false)
            view.viewModel = viewModels
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(false)
            view.textHeader = getString(R.string.restore_rating_error,String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.flixBalance),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.restoratingFlix),String.format(Locale.US, "%.2f", viewModels.restoreRatingDetails.value?.requiredFlix))
            view.textHead.textSize = 13F
            view.actionBuy.setOnClickListener {
                findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalBuyflaxFragment(isBuyCoin = false))
                dismiss()
            }
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

    override fun getTheme(): Int {
        return R.style.Widget_Flashat_Tribe_BottomSheet
    }

    fun restoreRatingInfo(header: String) {
        MaterialDialog(requireContext()).show {
            val view = DataBindingUtil.inflate<LayoutPodiumCameraDisabledBinding>(layoutInflater, R.layout.layout_podium_camera_disabled, null, false)
            view.textHeader = header
            val header = view.layoutHeader
            header.setPadding(header.paddingLeft, header.paddingTop, header.paddingRight,2)
            customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
            cancelable(true)
            view.actionClose.setOnClickListener {
                dismiss()
            }
        }
    }

}