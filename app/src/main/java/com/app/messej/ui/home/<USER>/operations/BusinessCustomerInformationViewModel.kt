package com.app.messej.ui.home.businesstab.operations

import android.app.Application
import android.net.Uri
import android.os.CountDownTimer
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.OtpRequestResponse
import com.app.messej.data.model.api.UserDetailResponse
import com.app.messej.data.model.enums.AppLocale
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.data.utils.UserInfoUtil.containsSpecialCharacters
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachViewModel
import com.app.messej.ui.utils.LocaleUtil
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BusinessCustomerInformationViewModel (application: Application) : BaseProfilePicAttachViewModel(application) {
    private val podiumRepo = PodiumRepository(getApplication())
    private var businessRepo = BusinessRepository(application)

    companion object {
        const val EGYPT_COUNTRY_CODE = "+20"
    }

    val _payoutUserInfoLiveData : MutableLiveData<UserDetailResponse> = MutableLiveData()
    val payoutUserInfoLiveData : LiveData<UserDetailResponse> = _payoutUserInfoLiveData

    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user



    val isPhoneNumberAlreadyVerified = LiveEvent<Boolean>()

    val errorEvent = LiveEvent<String>()


    private val _isPhoneNumberVerified : MutableLiveData<Boolean> = MutableLiveData()
    val isPhoneNumberVerified : LiveData<Boolean> = _isPhoneNumberVerified

    private val _loaderLiveData = MutableLiveData<Boolean>(false)
    val loaderLiveData: LiveData<Boolean> = _loaderLiveData


    val firstName = MutableLiveData<String?>(null)
    val lastName = MutableLiveData<String?>(null)
    val passportIdNumber = MutableLiveData<String?>(null)
    val phoneNumber = MutableLiveData<String?>(null)
    val phoneCountryCode = MutableLiveData<String?>(null)
    val countryCodeIso = MutableLiveData<String?>(null)
    val cityState = MutableLiveData<String?>(null)
    val streetAddress = MutableLiveData<String?>(null)
    val postalCode = MutableLiveData<String?>(null)
    val isDataPersist = MutableLiveData<Boolean>(false)
    val backEndDbID = MutableLiveData<Int?>(null)
    val otp = MutableLiveData<Int>()


    val passportImageError = MutableLiveData<String>()

    val firstNameErrorFlag = MutableLiveData(false)
    val firstNameSpecialCharErrorFlag = MutableLiveData(false)
    val firstNameNumberErrorFlag = MutableLiveData(false)
    val lastNameErrorFlag = MutableLiveData(false)
    val lastNameSpecialCharErrorFlag = MutableLiveData(false)
    val lastNameNumberErrorFlag = MutableLiveData(false)
    val passportIdNumberErrorFlag = MutableLiveData(false)
    val passportIdNumberSpaceErrorFlag = MutableLiveData(false)
    val passportIdNumberSpecialCharErrorFlag = MutableLiveData(false)
    val phoneNumberErrorFlag = MutableLiveData(false)
    val invalidPhoneNumberErrorFlag = MutableLiveData(false)
    val countryCodeErrorFlag = MutableLiveData(false)
    val cityStateErrorFlag = MutableLiveData(false)
    val cityStateSpecialCharErrorFlag = MutableLiveData(false)
    val streetAddressErrorFlag = MutableLiveData(false)
    val streetAddressSpecialCharErrorFlag = MutableLiveData(false)
    val postalCodeErrorFlag = MutableLiveData(false)
    val postalCodeSpecialCharErrorFlag = MutableLiveData(false)
    val passportImageErrorFlag = MutableLiveData(false)

    val clearAllErrors: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(firstName) { _ -> med.value = true }
        med.addSource(lastName) { _ -> med.value = true }
        med.addSource(passportIdNumber) { _ -> med.value = true }
        med.addSource(phoneNumber) { _ -> med.value = true }
        med.addSource(phoneCountryCode) { _ -> med.value = true }
        med.addSource(countryCodeIso) { _ -> med.value = true }
        med.addSource(cityState) { _ -> med.value = true }
        med.addSource(streetAddress) { _ -> med.value = true }
        med.addSource(postalCode) { _ -> med.value = true }
        med.addSource(finalImagePath) { _ -> med.value = true }

        return@lazy med
    }

    fun clearAllFlags() {
        firstNameErrorFlag.value = false
        firstNameSpecialCharErrorFlag.value = false
        firstNameNumberErrorFlag.value = false
        lastNameErrorFlag.value = false
        lastNameSpecialCharErrorFlag.value = false
        lastNameNumberErrorFlag.value = false
        passportIdNumberErrorFlag.value = false
        passportIdNumberSpecialCharErrorFlag.value = false
        passportIdNumberSpaceErrorFlag.value = false
        phoneNumberErrorFlag.value = false
        invalidPhoneNumberErrorFlag.value = false
        countryCodeErrorFlag.value = false
        cityStateErrorFlag.value = false
        cityStateSpecialCharErrorFlag.value = false
        streetAddressErrorFlag.value = false
        streetAddressSpecialCharErrorFlag.value = false
        postalCodeErrorFlag.value = false
        postalCodeSpecialCharErrorFlag.value = false
        passportImageErrorFlag.value = false
    }


    private val _otpTimeout = MutableLiveData<Long>(0)
    val otpTimeout: LiveData<Long> = _otpTimeout

    private var timer: CountDownTimer? = null

    val optTimeoutString = _otpTimeout.map {
        return@map DateTimeUtils.formatSeconds(it)
    }
    private val _canRequestOTPResend = MutableLiveData(true)
    val canRequestOTPResend: LiveData<Boolean> = _canRequestOTPResend

    private val _submitPayoutLoading: MutableLiveData<Boolean> = MutableLiveData(false)


    override fun addCroppedImage(uri: Uri) {
        super.addCroppedImage(uri)
        _useProfilePhoto.postValue(false)
    }


    private val _useProfilePhoto = MutableLiveData<Boolean>(false)
    private var _isPhoneValid = MutableLiveData<Boolean?>(null)
    val isPhoneValid: LiveData<Boolean?>
        get() = _isPhoneValid
    fun setPhoneCountryCode(selectedCountryCode: String?) {
        phoneCountryCode.postValue(selectedCountryCode)
    }
    fun setCountryCodeIso(selectedCountryCode: String?) {
        countryCodeIso.postValue(selectedCountryCode)
    }
    fun setPhoneNumberValid(valid: Boolean) {
        _isPhoneValid.postValue(valid)
    }
    fun getPayoutUser() {

        viewModelScope.launch(Dispatchers.IO) {
            _loaderLiveData.postValue(true)
            when (val result: ResultOf<UserDetailResponse> = businessRepo.getPayoutUserInfo()) {
                is ResultOf.Success -> {
                    _payoutUserInfoLiveData.postValue(result.value)
                        updateUserData(result.value)
                }

                is ResultOf.APIError -> {
                    _loaderLiveData.postValue(false)
                }

                is ResultOf.Error -> {
                    _loaderLiveData.postValue(false)
                }
            }
            _loaderLiveData.postValue(false)
        }
    }

    private fun updateUserData(userData: UserDetailResponse) {
        firstName.postValue(userData.firstName)
        lastName.postValue(userData.lastName)
        passportIdNumber.postValue(userData.idNumber)
        if (user.isEgyptianUser && userData.countryCode != EGYPT_COUNTRY_CODE) {
            phoneNumber.postValue(null)
        } else phoneNumber.postValue(userData.phoneNo)
        cityState.postValue(userData.city)
        streetAddress.postValue(userData.address)
        postalCode.postValue(userData.pincode)
//        isDataPersist.postValue(userData.persist)
        backEndDbID.postValue(userData.id)
        finalImagePath.postValue(userData.idImage)
    }
     fun submitPayoutDetails(payoutId: Int?) {
        _submitPayoutLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val compressed = finalImage?.let { podiumRepo.compressImage(it) }

            val phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber.value?:return@launch)

            when (val result =
                businessRepo.submitPayoutUserDetails(
                    idNumber = passportIdNumber.value?:"",
                    pincode = postalCode.value ?: "",
                    phoneNo = phoneNumberFormatted,
                    address = streetAddress.value ?: "",
                    countryCode = phoneCountryCode.value?:"",
                    countryCodeIso = countryCodeIso.value?:"",
                    city = cityState.value ?: "",
                    lname = lastName.value ?: "",
                    fname = firstName.value ?: "",
                    persist = isDataPersist.value ?: false,
                    file = compressed,
                    dbId = backEndDbID.value,
                    payoutId = payoutId
                )) {
                is ResultOf.Success -> {
                    result.value.let {
                        if (it.phoneVerified){
                            isPhoneNumberAlreadyVerified.postValue(true)
                        }
                        else{
                            isPhoneNumberAlreadyVerified.postValue(false)
                        }
                    }
                }

                is ResultOf.APIError -> {
                    errorEvent.postValue(result.error.message)
                }
                is ResultOf.Error -> {}
            }
            _submitPayoutLoading.postValue(false)
        }
    }
    fun validateFields(): Boolean {
        var isValid = true

        if (firstName.value.isNullOrEmpty()) {
            firstNameErrorFlag.postValue(true)
            isValid = false
        }

        if(firstName.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){
            firstNameSpecialCharErrorFlag.value = true
            isValid = false
        }

        if (!validateName(firstName.value.toString())){
            firstNameNumberErrorFlag.value = true
            isValid = false
        }

        if (lastName.value.isNullOrEmpty()) {
            lastNameErrorFlag.value = true
            isValid = false
        }
        if (lastName.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){
            lastNameSpecialCharErrorFlag.value = true
            isValid = false
        }
        if (!validateName(lastName.value.toString())){
            lastNameNumberErrorFlag.value = true
            isValid = false
        }

        if (passportIdNumber.value.isNullOrEmpty()) {
            passportIdNumberErrorFlag.value = true
            isValid = false
        }
        if (passportIdNumber.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){
            passportIdNumberSpecialCharErrorFlag.value = true
            isValid = false
        }
        if (passportIdNumber.value?.contains(" ") == true){
            passportIdNumberSpaceErrorFlag.value = true
            isValid = false
        }

        if (phoneNumber.value.isNullOrEmpty()) {
            phoneNumberErrorFlag.value = true
            isValid = false
        }
        if (isPhoneValid.value == false){
            invalidPhoneNumberErrorFlag.value = true
            isValid = false
        }

        if (phoneCountryCode.value.isNullOrEmpty()) {
            countryCodeErrorFlag.value = true
            isValid = false
        }

        if (cityState.value.isNullOrEmpty()) {
            cityStateErrorFlag.value = true
            isValid = false
        }

        if (cityState.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){
            cityStateSpecialCharErrorFlag.value = true
            isValid = false
        }

        if (streetAddress.value.isNullOrEmpty()) {
            streetAddressErrorFlag.value = true
            isValid = false
        }

        if (streetAddress.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){
            streetAddressSpecialCharErrorFlag.value = true
            isValid = false
        }

        if (postalCode.value.isNullOrEmpty()) {
            postalCodeErrorFlag.value = true
            isValid = false
        }

        if (postalCode.value?.containsSpecialCharacters() == true && LocaleUtil.getAppLocale().apiCode == AppLocale.ENGLISH.apiCode){
            postalCodeSpecialCharErrorFlag.value = true
            isValid = false
        }

        if (finalImagePath.value.isNullOrEmpty()){
            passportImageErrorFlag.value = true
            isValid = false
        }

        return isValid
    }


fun sendOTP() {
    val _phoneNumberFormatted = UserInfoUtil.sanitizePhoneNumber(phoneNumber.value ?: return)
    viewModelScope.launch(Dispatchers.IO) {
        _loaderLiveData.postValue(true)
        when (val result: ResultOf<OtpRequestResponse> = businessRepo.requestOTP(_phoneNumberFormatted, phoneCountryCode.value.toString())) {
            is ResultOf.Success -> {
                Log.i("sendOTP: ", result.toString())
                countDownWaitTime(result.value.waitTime.toLong())
            }
            is ResultOf.APIError -> {
                _loaderLiveData.postValue(false)
            }
            is ResultOf.Error -> {
                _loaderLiveData.postValue(false)
            }
        }
        _loaderLiveData.postValue(false)
    }
}


  fun verifyOtp(otp: String) {
      viewModelScope.launch(Dispatchers.IO) {
          _loaderLiveData.postValue(true)
          when (val result: ResultOf<String> = businessRepo.confirmOTP(phoneNumber.value.toString(), phoneCountryCode.value.toString(), otp.toInt())) {
              is ResultOf.Success -> {
                  Log.i("verifyOtp: ", result.toString())
                  _isPhoneNumberVerified.postValue(true)
              }
              is ResultOf.APIError -> {
                  _loaderLiveData.postValue(false)
                  errorEvent.postValue(result.error.toString())
              }
              is ResultOf.Error -> {
                  _loaderLiveData.postValue(false)
              }
          }
          _loaderLiveData.postValue(false)
      }
  }



    private fun validateName(name: String): Boolean {
        val nameRegex = Regex("^[a-zA-Z\u0600-\u06FF\u0750-\u077F\u0590-\u05FF]+$")
        return nameRegex.matches(name)
    }


    private fun countDownWaitTime(wait: Long) {
        timer?.cancel()
        if (wait <= 0) {
            _otpTimeout.postValue(0)
        }
        _otpTimeout.postValue(wait)
        _canRequestOTPResend.postValue(false)
        viewModelScope.launch(Dispatchers.Main) {
            timer = object : CountDownTimer(wait * 1000, 1000) {

                override fun onTick(millisUntilFinished: Long) {
                    _otpTimeout.postValue(millisUntilFinished / 1000)
                }

                override fun onFinish() {
                    _otpTimeout.postValue(0)
                    _canRequestOTPResend.postValue(true)
                }
            }.start()
        }
    }

}
