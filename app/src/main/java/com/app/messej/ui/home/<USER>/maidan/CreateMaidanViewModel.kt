package com.app.messej.ui.home.publictab.maidan

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.DealsBeneficiary
import com.app.messej.data.model.api.PodiumJoinErrorResponse
import com.app.messej.data.model.api.podium.challenges.CreateMaidanRequest
import com.app.messej.data.model.api.podium.challenges.MaidanEditResponse
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.model.enums.ChallengeContributionType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.PodiumRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel.PodiumKickReason
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import okhttp3.internal.toLongOrDefault
import kotlin.math.roundToInt

@OptIn(FlowPreview::class)
class CreateMaidanViewModel(application: Application) : AndroidViewModel(application) {

    private val podiumRepository = PodiumRepository(application)
    private val profileRepo = ProfileRepository(application)
    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user

    companion object {
        private const val MIN_PRICE_CONTRIBUTOR_SELF = 100
    }

    private val _createMaidanLoading = MutableLiveData(false)
    val createMaidanLoading: LiveData<Boolean> = _createMaidanLoading

    private val nickNames = profileRepo.getNickNamesFlow().stateIn(
        scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = listOf()
    )

    /**
     * Contribution Logics
     */

    var contributorSearchKeyword = MutableLiveData("")
    private val contributorSearchTerm = contributorSearchKeyword.asFlow().debounce(500L).asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    val priceAmountString = MutableLiveData<String?>(null)

    enum class InvitationType {
        PUBLIC, PRIVATE
    }

    enum class ChallengeType {
        Free, Paid
    }

    private val _invitationType = MutableLiveData<InvitationType?>(null)
    val invitationType: LiveData<InvitationType?> = _invitationType

    private val _challengeType = MutableLiveData<ChallengeType?>(null)
    val challengeType: LiveData<ChallengeType?> = _challengeType

    fun setInvitationType(type: InvitationType) {
//        priceAmountString.postValue("")
        _invitationType.postValue(type)
    }

    fun setChallengeType(type: ChallengeType) {
        _challengeType.postValue(type)
    }

    private val _contributorsSearchList = MutableLiveData<List<DealsBeneficiary>>(null)
    val contributorsSearchList: MediatorLiveData<List<DealsBeneficiary>> by lazy {
        val med = MediatorLiveData<List<DealsBeneficiary>>()
        fun update() {
            _contributorsSearchList.value?.let {
                med.postValue(it.filter { ps ->
                    ps.id != _selectedContributor.value?.id
                })
            }
        }
        med.addSource(_selectedContributor) { update() }
        med.addSource(_contributorsSearchList) { update() }
        med
    }

    fun searchContributor(searchTerm: String) {
//        val id = _podiumId.value?: return
        viewModelScope.launch(Dispatchers.IO) {
            when (val result = podiumRepository.getMaidanBeneficiaryList(keyword = searchTerm, prize = priceAmountString.value?.toIntOrNull()?:0)) {
                is ResultOf.Success -> {
                    _contributorsSearchList.postValue(result.value.users.filter { it.id != user.id }) //filter facilitator out
                }
                is ResultOf.APIError -> {}
                is ResultOf.Error -> {}
            }
        }
    }

    private val _selectedContributor = MutableLiveData<DealsBeneficiary>()
    val selectedContributor = _selectedContributor

    fun onContributorSelected(item: DealsBeneficiary) {
        _selectedContributor.postValue(item)
    }

    val onMaidanCreated = LiveEvent<Podium>()
    val onMaidanCreateError = LiveEvent<String>()
    val onLiveInAnotherPodium = LiveEvent<PodiumKickReason.LiveInAnotherPodium>()

    val enableCreateMaidan: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)
        fun update() {
            val price = priceAmountString.value.orEmpty().trim().toLongOrDefault(0)
            val isFreeChallenge = _challengeType.value == ChallengeType.Free
            val valid = when (_invitationType.value) {
                InvitationType.PUBLIC -> if (isFreeChallenge) true else (price >= MIN_PRICE_CONTRIBUTOR_SELF)
                InvitationType.PRIVATE -> if (isFreeChallenge) true else (price >= MIN_PRICE_CONTRIBUTOR_SELF && _selectedContributor.value != null)
                else -> false
            }
            med.postValue(valid)
        }
        med.addSource(_invitationType) { update() }
        med.addSource(_challengeType) { update() }
        med.addSource(priceAmountString) { update() }
        med.addSource(_selectedContributor) { update() }
        med
    }

    private var podiumIdToEdit: String? = null

    fun setEditMode(id: String) {
        podiumIdToEdit = id
    }

    fun prefillData(lastChallenge: PodiumChallenge) {
        Log.d("CMVM", "prefillData: $lastChallenge")
        priceAmountString.postValue(lastChallenge.challengeFee?.roundToInt()?.toString())
        _invitationType.postValue(
            when (lastChallenge.contributorType) {
                ChallengeContributionType.MAIDAN_PRIVATE -> InvitationType.PRIVATE
                ChallengeContributionType.MAIDAN_PUBLIC -> InvitationType.PUBLIC
                else -> null
            }
        )
    }

    fun challengeNew(existingPodiumId: String) {
        createMaidanChallenge(existingPodiumId)
    }

    fun createMaidanChallenge() {
        createMaidanChallenge(null)
    }

    fun inviteOtherUser(existingPodiumId: String) {
        editMaidanChallenge(existingPodiumId)
    }

    private fun createMaidanChallenge(existingPodiumId: String?) {
        _createMaidanLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val invitees: List<Int>? = selectedContributor.value?.id?.let { listOf(it) }
//            priceAmountString.value?.toInt()?.let {
                val request = CreateMaidanRequest(
                    prize = if (_challengeType.value == ChallengeType.Free) 0 else (priceAmountString.value ?: "0").toInt(),
                    invitees = invitees,
                    existingPodiumId = existingPodiumId
                )
                when (val result = podiumRepository.createMaidanChallenge(request)) {
                    is PodiumRepository.JoinResultOf.Success -> {
                        onMaidanCreated.postValue(result.value)
                    }

                    is PodiumRepository.JoinResultOf.APIError -> {
                        if (result.error.result?.reason== PodiumJoinErrorResponse.PodiumJoinErrorReason.LIVE_IN_OTHER_PODIUM) {
                            result.error.result.podiumId?.let { id ->
                                result.error.result.podiumName?.let { name ->
                                    onLiveInAnotherPodium.postValue(PodiumKickReason.LiveInAnotherPodium(result.error.message, id, name, result.error.result.canLeave?:false))
                                    _createMaidanLoading.postValue(false)
                                    return@launch
                                }
                            }
                        }
                        onMaidanCreateError.postValue(result.error.message)
                    }

                    is PodiumRepository.JoinResultOf.Error -> {}

                }
                _createMaidanLoading.postValue(false)
//            }
        }
    }

    val onMaidanEdited = LiveEvent<MaidanEditResponse.PrizeChange?>()

    private fun editMaidanChallenge(existingPodiumId: String) {
        _createMaidanLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            val invitees: List<Int>? = selectedContributor.value?.id?.let { listOf(it) }
//            priceAmountString.value?.toInt()?.let {
                val request = CreateMaidanRequest(
                    prize = if (_challengeType.value == ChallengeType.Free) 0 else (priceAmountString.value ?: "0").toInt(),
                    invitees = invitees,
                    existingPodiumId = existingPodiumId
                )
                when (val result = podiumRepository.editMaidanChallenge(existingPodiumId,request)) {
                    is ResultOf.Success -> {
                        onMaidanEdited.postValue(result.value.prizeChange)
                    }

                    is ResultOf.APIError -> {
                        onMaidanCreateError.postValue(result.error.message)
                    }
                    is ResultOf.Error -> {}
                }
                _createMaidanLoading.postValue(false)
//            }
        }
    }

    val onLeftOtherPodium = LiveEvent<Boolean>()

    fun leaveOtherPodium(id: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                Log.d("PodiumLVM", "trying to leave")
                when (val result = podiumRepository.leavePodium(id)) {
                    is ResultOf.Success -> {
                        Log.d("PodiumLVM", "leave success")
                        podiumRepository.checkExistingAgoraSession("0")
                        onLeftOtherPodium.postValue(true)
                    }

                    is ResultOf.APIError -> {}

                    is ResultOf.Error -> {}
                }

            } catch (e: Exception) {
                Log.d("PodiumLVM", "leave: error: ${e.message}")
            }
        }
    }
}