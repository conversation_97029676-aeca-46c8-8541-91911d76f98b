package com.app.messej.ui.home.publictab.podiums.live

import android.content.res.AssetFileDescriptor
import android.content.res.Resources
import android.media.MediaPlayer
import android.util.Log
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.app.messej.R
import com.app.messej.data.model.entity.Podium
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.ui.home.publictab.podiums.videoGift.TransparentVideo
import com.app.messej.ui.home.publictab.podiums.videoGift.TransparentVideoViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import java.time.Duration
import java.time.ZonedDateTime

class PodiumAnthemPresenter(private val holder: ComposeView, private val listener: AnthemListener) {

    interface AnthemListener {

        fun getFragment(): Fragment
        fun onFinished()
        fun onShow(show: Boolean)

        val resources: Resources
            get() = getFragment().resources
    }

    private var transparentVideoViewModel: TransparentVideoViewModel
    private var musicPlayer: MediaPlayer

    init {
        listener.getFragment().apply {
            transparentVideoViewModel = TransparentVideoViewModel(lifecycleScope, requireContext())
        }
        musicPlayer = MediaPlayer().apply {
            val afd: AssetFileDescriptor = listener.resources.openRawResourceFd(R.raw.flashat_anthem_audio) ?: return@apply
            setDataSource(afd)
            afd.close()
            prepare()
            isLooping = false
        }
    }

    private var currentlyPlayingAnthem: Podium.Anthem? = null

    fun playAnthem(anthem: Podium.Anthem) {
        if (currentlyPlayingAnthem?.anthemStartTime==anthem.anthemStartTime) return
        currentlyPlayingAnthem = anthem

        transparentVideoViewModel.initialiseMediaPlayer(R.raw.flashat_anthem_video, true)
        listener.onShow(true)
        var duration: Int? = null
        holder.apply {
            isVisible = true
            setContent {
                AnthemVideo(transparentVideoViewModel) {
                    try {
                        val position = musicPlayer.currentPosition
                        if (duration==null) duration = musicPlayer.duration
                        Pair(position, duration)
                    } catch (_: Exception) {
                        Pair(0, 1)
                    }
                }
            }
        }
        val seekTime = Duration.between(anthem.parsedAnthemStartTime, ZonedDateTime.now()).toMillis().coerceAtLeast(0)
        Log.d("TVVM", "playAnthem: seekTime: $seekTime")
        try {
            musicPlayer.let {
                it.seekTo(seekTime.toInt())
                it.start()
                it.setOnCompletionListener {
                    Log.d("TVVM", "playAnthem: finished playing")
                    listener.onFinished()
                    cleanup()
                }
            }
        } catch (_: Exception) {}
    }

    private fun stopMusic() {
        try {
            Log.d("TVVM", "playAnthem: stopMusic")
            if (musicPlayer.isPlaying) {
                musicPlayer.stop()
            }
            musicPlayer.release()

        } catch (e: Exception) { }
    }

    fun cleanup() {
        Log.d("TVVM", "playAnthem: cleanup")
        holder.setContent {  }
        transparentVideoViewModel.stopPlayback()
        stopMusic()
        listener.onShow(false)
        holder.isVisible = false
    }

}

@Composable
fun AnthemVideo(viewModel: TransparentVideoViewModel, getStatus: () -> Pair<Int,Int>) {
    val progress = remember { mutableStateOf(Pair<Int,Int>(0,2000)) }

    // Simulate progress updates
    LaunchedEffect(Unit) {
        while (isActive) {
            delay(100L)
            progress.value = getStatus.invoke()
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                TransparentVideo(viewModel = viewModel, modifier = Modifier.fillMaxSize())
            }
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth().wrapContentHeight(),
                progress = {
                    (progress.value.first.toFloat() / progress.value.second.toFloat()).coerceIn(0f..1f)
                },
                color = colorResource(R.color.colorPrimary),
                trackColor = colorResource(R.color.textColorAlwaysDarkSecondary),                 // Negative gap removes spacing
                drawStopIndicator = {}
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth().padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                val textStyle = MaterialTheme.typography.labelMedium.copy(color = colorResource(R.color.textColorAlwaysDarkSecondary))
                Text(
                    text = DateTimeUtils.formatSeconds(progress.value.first/1000),
                    style = textStyle
                )
                Text(
                    text = DateTimeUtils.formatSeconds(progress.value.second/1000),
                    style = textStyle
                )
            }
        }
    }
}