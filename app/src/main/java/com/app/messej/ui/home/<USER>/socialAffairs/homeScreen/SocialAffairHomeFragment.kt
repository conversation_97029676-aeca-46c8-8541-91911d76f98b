package com.app.messej.ui.home.publictab.socialAffairs.homeScreen

import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.PopupMenu
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.R
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.SocialActiveCaseMainTab
import com.app.messej.databinding.FragmentSocialAffairsHomeBinding
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.socialEngageDevelopmentAlert
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class SocialAffairHomeFragment : Fragment(), SocialAffairHomeScreenListeners {

    private lateinit var binding: FragmentSocialAffairsHomeBinding
    private val viewModel : SocialAffairHomeViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_social_affairs_home, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
        viewModel.getCaseCount()
    }

    private fun setup() {
        setupPromoBoard(binding.promoBar)
        setupClickListeners()
        binding.composeView.setContent {
            SocialAffairHomeComposeScreen(
                viewModel = viewModel,
                clickListeners = this
            )
        }
    }

    private fun setupClickListeners() {
        binding.apply {
            customActionBar.icSocialLogout.setOnClickListener {
                findNavController().navigateUp()
            }
            customActionBar.icSocialSettings.setOnClickListener {
                showPopupMenu(view = it)
            }
        }
    }

    private fun observe() {

    }

    private fun showPopupMenu(view: View) {
        val popup = PopupMenu(requireContext(), view)
        popup.menuInflater.inflate(R.menu.menu_social_affairs_home, popup.menu)

        popup.setOnMenuItemClickListener { item: MenuItem ->
            when(item.itemId) {
                R.id.about_social_affairs -> {
                    findNavController().navigateSafe(
                        direction = SocialAffairHomeFragmentDirections.actionGlobalSocialPolicyDocumentFragment(
                            documentType = DocumentType.ABOUT_SOCIAL_AFFAIRS
                        )
                    )
                }
            }
            return@setOnMenuItemClickListener true
        }
        popup.show()
    }

    override fun onDonateClick() {
        findNavController().navigateSafe(direction = SocialAffairHomeFragmentDirections
            .actionSocialAffairHomeFragmentToSocialAffairActiveCasesFragment(tab = SocialActiveCaseMainTab.Donate)
        )
    }

    override fun onNewCaseClick() {
        findNavController().navigateSafe(direction = SocialAffairHomeFragmentDirections
            .actionSocialAffairHomeFragmentToSocialAffairActiveCasesFragment(tab = SocialActiveCaseMainTab.NewCases)
        )
    }

    override fun onMinisterClick() {
        //Remove this alert when minister screen development completes.
        showFlashatDialog {
            setMessage(message = R.string.social_minister_development_message)
            setConfirmButtonVisible(visible = false)
        }

        // Check also MSA is appointed or not in the below if condition
        /* if (viewModel.user.citizenship.isPresident) {
            showAppointMSAAlert()
            return
        }
        findNavController().navigateSafe(
            direction = SocialAffairHomeFragmentDirections.actionSocialAffairHomeFragmentToSocialAffairMinisterFragment()
        )
         */
    }

    override fun onCommitteeClick() {
        findNavController().navigateSafe(
            direction = SocialAffairHomeFragmentDirections.actionGlobalSocialAffairCommitteeFragment()
        )
    }

    override fun onHonoursClick() {
        findNavController().navigateSafe(
            direction = SocialAffairHomeFragmentDirections.actionSocialAffairHomeFragmentToSocialAffairHonoursFragment()
        )
    }

    override fun onSocialSupportClick() {
        findNavController().navigateSafe(
            direction = SocialAffairHomeFragmentDirections.actionGlobalSocialSupportFragment()
        )
    }

    override fun onSocialEngageClick() {
        socialEngageDevelopmentAlert()
    }

    private fun showAppointMSAAlert() {
        showFlashatDialog {
            setMessage(getString(R.string.social_appoint_msa_message))
            setConfirmButton(title = R.string.social_appoint_msa_button, icon = R.drawable.ic_appoint_msa, tint = true) {
                // Navigate to Huddle Participant Screen
                true
            }
            setCloseButtonText(text = R.string.common_later)
        }
    }

}