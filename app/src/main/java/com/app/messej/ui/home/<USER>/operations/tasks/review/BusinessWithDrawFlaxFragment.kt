package com.app.messej.ui.home.businesstab.operations.tasks.review

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.widget.addTextChangedListener
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.PayoutAccountType
import com.app.messej.databinding.FragmentWithDrawBinding
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.utils.FragmentExtensions.bindFlaxRateToolbarChip
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe

class BusinessWithDrawFlaxFragment: Fragment(), BusinessWithDrawListeners {

    private val viewModel: BusinessWithDrawViewModel by activityViewModels ()
    private lateinit var  binding:FragmentWithDrawBinding
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_with_draw, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        checkEligibilityForPointsReview()
        observe()
        setUpOnClickListeners()

        binding.composeView.setContent {
            BusinessWithDrawComposeScreen(
                viewModel = viewModel,
                listener = this
            )
        }
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        bindFlaxRateToolbarChip(binding.customActionBar.flaxRateChip)
        binding.customActionBar.toolBarTitle.text = getString(R.string.title_withdraw_your_flax)
    }
    private fun setup() {
//        binding.radioWithDrawOption.isChecked=true
//        binding.partPayOut.isEnabled=false
//        binding.layoutCellFull.setBackgroundResource(R.drawable.bg_flax_review)

        viewModel.setAccountType(type = if (viewModel.payoutEligibility.value?.normalPayOutInfo == null)
            PayoutAccountType.SocialAccount else PayoutAccountType.PrimaryAccount
        )

        viewModel.withdrawErrorTypes.observe(viewLifecycleOwner) {
            Log.d("BWFF", "Withdraw error type -> $it")
        }

//        binding.radioWithDrawOption.apply {
//            setOnCheckedChangeListener { _, checked ->
//                if(checked){
//                    viewModel.setFullAmountChecked(true)
//                    binding.partPayOut.setText("")
//                    binding.partPayOut.isEnabled=false
//                    binding.radioEnterAmount.isChecked = !binding.radioEnterAmount.isChecked
//                    binding.layoutCellFull.setBackgroundResource(R.drawable.bg_flax_review)
//                }else{
//                    binding.layoutCellFull.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.transparent))
//                }
//            }
//        }

//        binding.radioEnterAmount.apply {
//            setOnCheckedChangeListener { _, checked ->
//                if(checked){
//                    viewModel.setFullAmountChecked(false)
//                    viewModel.setCustomAmount(binding.partPayOut.text.toString())
//                    binding.partPayOut.isEnabled=true
//                    binding.radioWithDrawOption.isChecked = !binding.radioEnterAmount.isChecked
//                    binding.enterAmountLayout.setBackgroundResource(R.drawable.bg_flax_review)
//                }else{
//                    binding.enterAmountLayout.setBackgroundColor(ContextCompat.getColor(requireContext(),R.color.transparent))
//                }
//            }
//        }
//        binding.partPayOut.apply {
//            addTextChangedListener { viewModel.setCustomAmount(this.text.toString())}
//        }
    }

    private fun setUpOnClickListeners() {
        binding.actionClose.setOnClickListener { findNavController().popBackStack() }
//        binding.actionLater.setOnClickListener { findNavController().popBackStack() }
//        binding.actionSubmit.setOnClickListener {
//
//            viewModel.actionPayoutRequest(binding.radioWithDrawOption.isChecked)
//        }
    }

    private fun observe() {
        viewModel.onPayoutSuccess.observe(viewLifecycleOwner){
            it?.let {
                if(it){
                    viewModel.resetValues()
//                    binding.radioWithDrawOption.isChecked=true
                    val action = HomeBusinessFragmentDirections.actionGlobalBusinessCustomerInformationFragment()
                    (activity as MainActivity).navController.navigateSafe(action)
//                    MaterialDialog(requireContext()).show {
//                        val view = DataBindingUtil.inflate<LayoutFlaxCompleteBinding>(layoutInflater, R.layout.layout_flax_complete, null, false)
//                        customView(null, view.root, dialogWrapContent = false, noVerticalPadding = true, horizontalPadding = false)
//                        cancelable(true)
//                        view.txtContent.text = getString(R.string.content_bussiness_flex)
//                        view.closeButton.setOnClickListener {
//                            dismiss()
//                            <EMAIL>()
//                        }
//                    }
                }
            }
        }

     /*   viewModel.businessOperation.observe(viewLifecycleOwner) {
            it?.let {
                viewModel.isButtonEnable()
            }
        }*/

        viewModel._errorMessage.observe(viewLifecycleOwner){
            it?.let {
                Toast.makeText(requireContext(),it,Toast.LENGTH_SHORT).show()
            }

        }

//        viewModel.withdrawErrorTypes.observe(viewLifecycleOwner){
//            binding.flaxWithDrawError.apply {
//                it?.let {
//                    when(it){
//                        BusinessWithDrawViewModel.WithDrawErrorTypes.GRATER -> text=requireContext().getString(R.string.flax_amount_error)
//                        BusinessWithDrawViewModel.WithDrawErrorTypes.LESSER -> text= requireContext().getString(R.string.flax_amount_less_error)
//                        BusinessWithDrawViewModel.WithDrawErrorTypes.RECEIVE_LESSER -> text=requireContext().getString(R.string.flax_amount_less_error)
//                        BusinessWithDrawViewModel.WithDrawErrorTypes.MAX_EXCEEDED -> {
//                            val maxAmount = viewModel.payoutEligibility.value?.maximumPP ?: 0.0
//                            text = requireContext().getString(R.string.flax_amount_max_exceeded, maxAmount.toString())
//                        }
//                        else->{}
//                    }
//                }
//            }
//        }
    }

    private fun checkEligibilityForPointsReview() {
        viewModel.payoutEligibility.value.let {
                if (it?.eligibility == true) {
                    binding.layoutNotEligible.isVisible = false
                    binding.composeView.isVisible = true
                } else {
                    binding.layoutNotEligible.isVisible = true
                    binding.composeView.isVisible = false
                    binding.pointsReviewNotEligibleTitle.text = it?.reason
                }
            }
        }

    override fun onAccept() {
        viewModel.actionPayoutRequest(isFullAmount = false)
    }

    override fun onLater() {
        findNavController().popBackStack()
    }

}
