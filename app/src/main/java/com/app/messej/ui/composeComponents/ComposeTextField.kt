package com.app.messej.ui.composeComponents

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.OutlinedTextField
import androidx.compose.material.TextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusManager
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextOverflow
import com.app.messej.R

@Composable
fun ComposeTextField(
    modifier: Modifier = Modifier,
    state: ComposeTextFieldState,
    backgroundColor: Color = colorResource(id = R.color.textInputBackground),
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Done,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    focusManager: FocusManager = LocalFocusManager.current,
    keyboardManager: SoftwareKeyboardController? = LocalSoftwareKeyboardController.current,
    placeHolderText: String? = null,
    singleLine: Boolean = true,
    totalCharacterLimit: Int? = null,
    isEnabled: Boolean = true,
    onValueChange:((String) -> Unit)? = null,
    leadingIcon: (@Composable () -> Unit)? = null,
    trailingIcon: (@Composable () -> Unit)? = null,
    onClick: (() -> Unit)? = null
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        OutlinedTextField(
            modifier = Modifier.clickable{ onClick?.let { it() } }.fillMaxWidth(),
            value  = state.text ?: "",
            textStyle = FlashatComposeTypography.defaultType.body2,
            singleLine = singleLine,
            enabled = isEnabled,
            shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.line_spacing)),
            minLines = if (singleLine) 1 else 6,
            maxLines = if (singleLine) 1 else 6,
            placeholder = {
                Text(
                    text = placeHolderText ?: "",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = FlashatComposeTypography.defaultType.body2,
                    color = colorResource(id = R.color.textColorSecondaryLight)
                )
            },
            keyboardActions = KeyboardActions(
                onDone = {
                    keyboardManager?.hide()
                    focusManager.clearFocus()
                },
            ),
            colors = TextFieldDefaults.outlinedTextFieldColors(
                backgroundColor = backgroundColor,
                textColor = colorResource(id = R.color.textColorPrimary),
                cursorColor = colorResource(id = R.color.colorPrimary),
                focusedBorderColor = colorResource(id = R.color.transparent),
                unfocusedBorderColor = colorResource(id = R.color.transparent),
                disabledBorderColor = colorResource(id = R.color.transparent)
            ),
            leadingIcon = leadingIcon,
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType, imeAction = imeAction),
            visualTransformation = visualTransformation,
            trailingIcon = trailingIcon,
            onValueChange = { value ->
                //Using limited value because when total character limit is not null
                // and we copy a paragraph of text and which is exceeding the total character limit
                //the trimmed value should be shown in the edit text
                val limitedValue = totalCharacterLimit?.let {
                    if (value.length > it) value.take(totalCharacterLimit)
                    else value
                } ?: value
                state.setTextValue(limitedValue)
                onValueChange?.let { it(limitedValue) }
            }
        )
        totalCharacterLimit?.let {
            Text(
                text = "${state.text?.length} / $it",
                modifier = Modifier
                    .padding(top = dimensionResource(id = R.dimen.element_spacing))
                    .align(alignment = Alignment.End),
                style = FlashatComposeTypography.overLineSmaller,
                color = colorResource(id = R.color.textColorPrimary)
            )
        }
    }

}