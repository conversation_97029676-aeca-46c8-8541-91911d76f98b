package com.app.messej.ui.home.publictab.socialAffairs.activeCases

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuProvider
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.enums.DocumentType
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.databinding.FragmentSocialActiveCasesBinding
import com.app.messej.ui.home.promobar.CommonPromoBarUtil.setupPromoBoard
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showSocialErrorAlert
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showVotingConfirmationAlert
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.socialEngageDevelopmentAlert
import com.app.messej.ui.home.publictab.socialAffairs.donateBottomSheet.SocialDonateBottomSheetFragment.Companion.SOCIAL_DONATE_UPDATE_REQUEST
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.gson.Gson

class SocialActiveCasesFragment : Fragment(), MenuProvider, SocialActiveCaseClickListeners {

    private lateinit var binding: FragmentSocialActiveCasesBinding
    private val viewModel : SocialActiveCasesViewModel by viewModels()
    private val args: SocialActiveCasesFragmentArgs by navArgs()

    companion object {
        const val SOCIAL_ACTIVE_CASE_UPDATE_REQUEST = "social_active_case_update_request"
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_social_active_cases, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(toolBar = this, customNavIcon = R.drawable.ic_social_back_button)
                setNavigationOnClickListener { findNavController().navigateUp() }
            }
        }
        binding.customActionBar.toolBarTitle.text = getString(R.string.social_active_cases)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addAsMenuHost()
        observe()
    }

    private fun setup() {
        viewModel.setActiveCaseTab(tab = args.tab, isFromNavArgs = true)
        setupPromoBoard(binding.promoBar)
        binding.composeView.setContent {
            SocialActiveCaseComposeScreen(
                viewModel = viewModel,
                listeners = this
            )
        }
    }

    private fun observe() {
        viewModel.onError.observe(viewLifecycleOwner) { msg ->
            msg?.let { showToast(message = it) }
        }

        viewModel.voteError.observe(viewLifecycleOwner) {
            it ?: return@observe
            showSocialErrorAlert(error = it)
        }

        setFragmentResultListenerOnActivity(requestKey = SOCIAL_DONATE_UPDATE_REQUEST) { _, _ ->
            viewModel.setReloadCaseList()
        }

        setFragmentResultListenerOnActivity(requestKey = SOCIAL_ACTIVE_CASE_UPDATE_REQUEST) { _, _ ->
            viewModel.setReloadCaseList()
        }
    }

    override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
        return menuInflater.inflate(R.menu.meu_social_affairs_donate_about, menu)
    }

    override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
        when (menuItem.itemId) {
            R.id.social_affairs_about_donate -> {
                findNavController().navigateSafe(
                    direction = SocialActiveCasesFragmentDirections.actionGlobalSocialPolicyDocumentFragment(
                        documentType = DocumentType.SOCIAL_AFFAIRS_ABOUT_DONATE
                    )
                )
            }
        }
        return true
    }

    override fun onVote(action: SocialVoteAction, caseId: Int?) {
        //Un comment if needed to show voting confirmation dialog
//        showVotingConfirmationAlert(
//            onConfirm = {
                viewModel.vote(caseId = caseId, voteAction = action)
//            }
//        )
    }

    override fun onDonate(case: SocialCaseInfo) {
        findNavController().navigateSafe(
            direction = SocialActiveCasesFragmentDirections.actionSocialDonateBottomSheetFragment(
                socialCase = Gson().toJson(case)
            )
        )
    }

    override fun onSocialEngageTabClick() {
        socialEngageDevelopmentAlert()
    }

    override fun onUserDPClick(userId: Int) {
        findNavController().navigateSafe(
            direction = SocialActiveCasesFragmentDirections.actionGlobalPublicUserProfileFragment(id = userId)
        )
    }

    override fun onClick(case: SocialCaseInfo?) {
        if (case?.id == null) return
        findNavController().navigateSafe(
            direction = SocialActiveCasesFragmentDirections.actionGlobalSocialCaseInfoFragment(id = case.id, isUpgradeSupport = case.isUpgradeRequest)
        )
    }
}