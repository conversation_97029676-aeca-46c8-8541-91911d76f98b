package com.app.messej.ui.home.businesstab.operations.tasks.review

import android.content.Context
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.app.messej.R
import com.app.messej.ui.composeComponents.ComposeTextField
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.app.messej.ui.composeComponents.FlashatComposeTypography
import com.app.messej.ui.composeComponents.HorizontalDashedLine
import com.app.messej.ui.home.publictab.myETribe.contactETribe.CustomHorizontalSpacer
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros

@Composable
fun WithdrawAccountView(
    amountTextFieldState: ComposeTextFieldState,
    isSelected: Boolean,
    @DrawableRes icon: Int,
    title: String,
    noteText: String,
    errorText: String?,
    onValueChange: (String)-> Unit,
    onClick: () -> Unit
) {
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
    val source = remember { MutableInteractionSource() }
    Column(
        modifier = Modifier
            .shadow(elevation = dimensionResource(id = R.dimen.line_spacing), shape = shape)
            .clip(shape = shape)
            .background(color = colorResource(id = if (isSelected) R.color.colorSurfaceSecondaryDark else R.color.colorSocialSurfaceSecondary))
            .padding(all = 12.dp)
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .clickable(indication = null, interactionSource = source) { onClick() }
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = icon),
                modifier = Modifier.size(size = dimensionResource(id = R.dimen.extra_margin)),
                tint = Color.Unspecified,
                contentDescription = null
            )
            CustomHorizontalSpacer(
                space = dimensionResource(id = R.dimen.element_spacing)
            )
            Text(
                text = title,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(end = dimensionResource(id = R.dimen.element_spacing))
                    .weight(weight = 1F),
                style = FlashatComposeTypography.defaultType.subtitle1,
                color = colorResource(id = R.color.textColorPrimary)
            )
            RoundSelectedView(
                isSelected = isSelected
            )
        }
        AnimatedVisibility(visible = isSelected) {
            Column(
                modifier = Modifier
                    .padding(top = dimensionResource(id = R.dimen.element_spacing))
                    .fillMaxWidth()
            ) {
                Text(
                    text = noteText,
                    modifier = Modifier.fillMaxWidth(),
                    style = FlashatComposeTypography.defaultType.overline,
                    color = colorResource(id = R.color.textColorPrimary)
                )
                Row(
                    modifier = Modifier
                        .padding(top = dimensionResource(id = R.dimen.element_spacing))
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(id = R.string.title_enter_amount),
                        style = FlashatComposeTypography.defaultType.overline,
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                    ComposeTextField(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = dimensionResource(id = R.dimen.activity_margin))
                            .weight(weight = 1F),
                        onValueChange = onValueChange,
                        backgroundColor = colorResource(id = R.color.colorSurface),
                        keyboardType = KeyboardType.NumberPassword,
                        state = amountTextFieldState
                    )
                    Text(
                        text = stringResource(id = R.string.common_flix),
                        style = FlashatComposeTypography.defaultType.body2.copy(fontStyle = FontStyle.Italic),
                        color = colorResource(id = R.color.textColorPrimary)
                    )
                }
                AnimatedVisibility(visible = !errorText.isNullOrEmpty()) {
                    Text(
                        text = errorText ?: "",
                        modifier = Modifier
                            .padding(top = dimensionResource(id = R.dimen.element_spacing))
                            .fillMaxWidth(),
                        style = FlashatComposeTypography.defaultType.overline,
                        color = colorResource(id = R.color.colorError)
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun WithdrawAccountPreview() {
    WithdrawAccountView(
        icon = R.drawable.im_flashat_logo,
        title = "Primary Account",
        noteText = "NOTE : You can offer a maximum of 70% of your FLiX Balance for Sale.",
        errorText = "Please enter a valid amount within the range 100 to 500",
        amountTextFieldState = ComposeTextFieldState(),
        isSelected = true,
        onValueChange = {},
        onClick = {}
    )
}

@Preview(showBackground = true)
@Composable
fun PayoutProcedureView(
    haveTwoAccounts: Boolean = true
) {
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
    Column(
        modifier = Modifier
            .background(color = colorResource(id = R.color.colorSocialSurfaceSecondary), shape = shape)
            .border(width = 1.dp, shape = shape, color = colorResource(id = R.color.colorSocialSurfaceSecondaryLight))
            .padding(top = 10.dp)
            .padding(all = 12.dp)
            .fillMaxWidth()
    ) {
        ProcedureTaskSingleItem(text = stringResource(id = R.string.business_task_details_step_1))
        if (haveTwoAccounts) {
            ProcedureTaskSingleItem(text = stringResource(id = R.string.payout_select_one_account))
        }
        ProcedureTaskSingleItem(text = stringResource(id = R.string.business_task_details_step_2))
        ProcedureTaskSingleItem(text = stringResource(id = R.string.business_task_details_step_3))
        ProcedureTaskSingleItem(text = stringResource(id = R.string.business_task_details_step_4))
        ProcedureTaskSingleItem(text = stringResource(id = R.string.business_task_details_step_5))
        ProcedureTaskSingleItem(text = stringResource(id = R.string.business_task_details_step_6), isLast = true)
    }
}

@Composable
private fun ProcedureTaskSingleItem(text: String, isLast: Boolean = false) {
    Row(
        modifier = Modifier
            .height(intrinsicSize = IntrinsicSize.Max)
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.offset(y = 4.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            //Dot View
            Box(
                modifier = Modifier
                    .clip(shape = CircleShape)
                    .background(color = colorResource(id = R.color.colorPrimary))
                    .size(size = dimensionResource(id = R.dimen.element_spacing))
            )
            if (!isLast) {
                //Vertical Line View
                Box(
                    modifier = Modifier
                        .width(width = 1.dp)
                        .background(color = colorResource(id = R.color.colorPrimary))
                        .fillMaxHeight()
                )
            }
        }
        CustomHorizontalSpacer(space = 12.dp)
        //Text
        Text(
            text = text,
            modifier = Modifier.padding(bottom = 12.dp),
            color = colorResource(id = R.color.textColorPrimary),
            style = FlashatComposeTypography.defaultType.overline
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun ProcedureTaskSingleItemPreview() {
    ProcedureTaskSingleItem(text = "Complete the TASKS and fulfill the conditions", isLast = false)
}

@Composable
fun PayoutRedeemOfferView(
    isEgyptUser: Boolean,
    redeemedAmount: Double?,
    processingFee: Double?,
    transferFee: Double?,
    egyptOfficeFee: Double?,
    flixRateToday: Double?,
    receivableAmount: Double?
) {
    val shape = RoundedCornerShape(size = dimensionResource(id = R.dimen.element_spacing))
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .shadow(elevation = dimensionResource(id = R.dimen.line_spacing), shape = shape)
                .clip(shape = shape)
                .background(color = colorResource(id = R.color.colorSocialSurfaceSecondary))
                .padding(top = 12.dp)
                .fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(space = dimensionResource(id = R.dimen.element_spacing))
        ) {
            RedeemOfferTitleAmountSingleView(
                title = stringResource(id = R.string.title_redeem_flax),
                amount = stringResource(id = R.string.common_flix_with_value,redeemedAmount.formatDecimalWithRemoveTrailingZeros())
            )
            RedeemOfferTitleAmountSingleView(
                title = stringResource(id = R.string.title_processing_charge),
                amount = stringResource(id = R.string.common_flix_with_value, processingFee.formatDecimalWithRemoveTrailingZeros())
            )
            RedeemOfferTitleAmountSingleView(
                title = stringResource(id = R.string.title_transfer_charge),
                amount = stringResource(id = R.string.common_flix_with_value, transferFee.formatDecimalWithRemoveTrailingZeros())
            )
            if (isEgyptUser) {
                RedeemOfferTitleAmountSingleView(
                    title = stringResource(id = R.string.title_egypt_office_fees),
                    amount = stringResource(id = R.string.common_flix_with_value, egyptOfficeFee.formatDecimalWithRemoveTrailingZeros())
                )
            }
            RedeemOfferTitleAmountSingleView(
                title = stringResource(id = R.string.title_flax_rate_today),
                amount = stringResource(id = R.string.common_flix_with_value, flixRateToday.formatDecimalWithRemoveTrailingZeros())
            )
            HorizontalDashedLine(
                modifier = Modifier.padding(horizontal = dimensionResource(id = R.dimen.element_spacing)),
                color = colorResource(id = R.color.textColorSecondaryLight)
            )
        }

        Column(
            modifier = Modifier
                .shadow(elevation = dimensionResource(id = R.dimen.line_spacing), shape = shape)
                .clip(shape = shape)
                .background(color = colorResource(id = R.color.colorSocialSurfaceSecondary))
                .padding(vertical = 12.dp)
                .fillMaxWidth()
        ) {
            RedeemOfferTitleAmountSingleView(
                title = stringResource(id = R.string.title_receivable),
                amount = receivableAmount.formatDecimalWithRemoveTrailingZeros()
            )
            Text(
                text = stringResource(id = R.string.title_receivable_footer),
                modifier = Modifier.padding(start = 12.dp),
                style = FlashatComposeTypography.defaultType.overline,
                color = colorResource(id = R.color.textColorSecondaryLight)
            )
        }
    }
}

@Preview
@Composable
private fun PayoutRedeemOfferPreview() {
    PayoutRedeemOfferView(
        isEgyptUser = true,
        redeemedAmount = 101.00,
        processingFee = 200.00,
        transferFee = 15.00,
        egyptOfficeFee = 151.00,
        flixRateToday = 10.00,
        receivableAmount = 10.00
    )
}

@Composable
private fun RedeemOfferTitleAmountSingleView(
    title: String,
    amount: String
) {
    Row(
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            modifier = Modifier
                .fillMaxWidth()
                .weight(weight = 1F)
                .padding(end = dimensionResource(id = R.dimen.line_spacing)),
            style = FlashatComposeTypography.defaultType.overline,
            color = colorResource(id = R.color.textColorSecondary)
        )
        Text(
            text = amount,
            style = FlashatComposeTypography.defaultType.body2.copy(fontStyle = FontStyle.Italic),
            color = colorResource(id = R.color.textColorPrimary)
        )
    }
}

@Composable
private fun RoundSelectedView(
    isSelected: Boolean
) {
    Box(
        modifier = Modifier
            .clip(shape = CircleShape)
            .background(color = colorResource(
                id = if (isSelected) R.color.colorSurface
                else R.color.colorSurfaceSecondaryDarker)
            )
            .size(size = dimensionResource(id = R.dimen.extra_margin)),
        contentAlignment = Alignment.Center
    ) {
        if(isSelected) {
            Box(
                modifier = Modifier
                    .clip(shape = CircleShape)
                    .background(color = colorResource(id = R.color.colorPrimary))
                    .size(size = 12.dp)
            )
        }
    }
}

@Preview
@Composable
private fun RoundSelectedPreView() {
    Column {
        RoundSelectedView(isSelected = true)
        RoundSelectedView(isSelected = false)
    }
}

fun BusinessWithDrawViewModel.WithDrawErrorTypes.setError(context: Context, minimumAmount: String?, maximumAmount: String?) : String? = when(this) {
    BusinessWithDrawViewModel.WithDrawErrorTypes.GRATER,
    BusinessWithDrawViewModel.WithDrawErrorTypes.LESSER,
    BusinessWithDrawViewModel.WithDrawErrorTypes.RECEIVE_LESSER,
    BusinessWithDrawViewModel.WithDrawErrorTypes.EMPTY,
    BusinessWithDrawViewModel.WithDrawErrorTypes.MAX_EXCEEDED -> context.getString(R.string.payout_error_message,minimumAmount, maximumAmount)
    BusinessWithDrawViewModel.WithDrawErrorTypes.NONE -> null
}