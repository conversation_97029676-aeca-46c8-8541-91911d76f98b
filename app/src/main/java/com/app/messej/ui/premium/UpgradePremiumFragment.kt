package com.app.messej.ui.premium

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.MarginPageTransformer
import com.android.billingclient.api.AcknowledgePurchaseParams
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClient.BillingResponseCode
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingFlowParams.ProductDetailsParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.QueryPurchasesParams
import com.app.messej.BuildConfig
import com.app.messej.NavGraphHomeDirections
import com.app.messej.R
import com.app.messej.data.model.SubscriptionProfile
import com.app.messej.data.utils.AuthUtil
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.databinding.FragmentUpgradePremiumBinding
import com.app.messej.databinding.LayoutAlreadySubscribedBinding
import com.app.messej.ui.enforcements.EnforcementExtensions.ensureInteractionAllowed
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.Executors
import kotlin.math.abs


class UpgradePremiumFragment : Fragment() {
    private var subProductDetails: ProductDetails?=null
    private lateinit var binding: FragmentUpgradePremiumBinding

    private val viewModel: UpgradePremiumViewModel by activityViewModels()
    private lateinit var billingClient: BillingClient
    var price :String?=null
    var sku :String?=null
    var isSuccess=false



    private val navigation: NavController by lazy {
        findNavController()
    }

    override fun onStart() {
        super.onStart()
        getSubscriptionDetails()
        viewModel.flixSubscriptionDetails()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_upgrade_premium, container, false)
        binding.viewModel = viewModel
        binding.lifecycleOwner = viewLifecycleOwner
        setup()
        observe()
        return binding.root
    }

    fun setup() {
       Log.d("FLIX_TOTAL","${viewModel.user.activePoints}")
        binding.back.setOnClickListener {
            findNavController().popBackStack()
        }
        billingClient = BillingClient.newBuilder(requireContext()).setListener(purchasesUpdatedListener).enablePendingPurchases().build()

        val transformer = CompositePageTransformer()
        transformer.addTransformer(MarginPageTransformer(40))
        transformer.addTransformer { page, position ->
            val r = 1 - abs(position)
            page.scaleY = 0.85f + r * 0.14f
        }
        binding.viewPager.apply {
            adapter = UpgradePremiumViewPagerAdapter()
            clipToPadding = false
            clipChildren = false
            offscreenPageLimit = 3
            getChildAt(0).overScrollMode = (RecyclerView.OVER_SCROLL_NEVER)
            setPageTransformer(transformer)
            binding.dotIndicator.attachTo(this)
            
        }
        binding.buttonUpgrade.setOnClickListener {
            ensureInteractionAllowed {
                Log.d("REQFLIX","${viewModel.hasRequiredFlix}")
                if (viewModel.hasRequiredFlix){
                    billingClient.endConnection()
                    findNavController().navigateSafe(NavGraphHomeDirections.actionGlobalSelectSubcriptionTypeFragment())
                }else {
                    billingClient.startConnection(object : BillingClientStateListener {
                        override fun onBillingSetupFinished(billingResult: BillingResult) {
                            if (billingResult.responseCode == BillingResponseCode.OK) {
                                getProductDetails()
                            }
                        }

                        override fun onBillingServiceDisconnected() {
                            // Try to restart the connection on the next request to
                            // Google Play by calling the startConnection() method.
                        }
                    })
                }
            }
        }

    }

    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult, purchases ->
            when (billingResult.responseCode) {

                BillingResponseCode.OK -> {
                    for (purchase in purchases!!) {
                        handlePurchase(purchase)
                    }
                }

                BillingResponseCode.ITEM_ALREADY_OWNED->{
                    isSuccess=true
                    requireActivity().runOnUiThread{
                    }

                }
                BillingResponseCode.USER_CANCELED -> {
                    requireActivity().runOnUiThread {
                    }
                }
                else -> {
                    // Handle any other error codes.
                    requireActivity().runOnUiThread {
                        Toast.makeText(requireActivity(), billingResult.debugMessage, Toast.LENGTH_SHORT).show()
                    }
                }
            }
        }

    private fun getProductDetails() {
        billingClient.queryPurchasesAsync(QueryPurchasesParams.newBuilder().setProductType(BillingClient.ProductType.SUBS).build()) { result, purchseList ->
            if (purchseList.isEmpty()) {
                    viewModel.setAlreadySubscribed(false)
                } else {
                    purchseList.map {purchase->
                        purchase.products.map {product->
                           if(product.equals(SUBSCRIPTION_ID)){
                               viewModel.setAlreadySubscribed(true)
                           }else{
                               viewModel.setAlreadySubscribed(false)
                           }
                        }
                    }
                }
            }
    }

    fun observe() {
        viewModel.alreadySubscribed.observe(viewLifecycleOwner){subscribed->
            subscribed?.let{
                if(!it) {
                    val queryProductDetailsParams = QueryProductDetailsParams.newBuilder().setProductList(
                        listOf(
                            QueryProductDetailsParams.Product.newBuilder().setProductId(SUBSCRIPTION_ID).setProductType(BillingClient.ProductType.SUBS).build()
                        )
                    ).build()

                    val user = viewModel.user
                    billingClient.queryProductDetailsAsync(queryProductDetailsParams) { billingResult, productDetailsList ->
                        for (productDetails in productDetailsList) {
                            if (productDetails.productId == SUBSCRIPTION_ID) {
                                subProductDetails = productDetails
                                val offerToken = productDetails.subscriptionOfferDetails?.get(0)?.offerToken
                                val productDetailsParamsList = listOf(
                                    ProductDetailsParams.newBuilder().setProductDetails(productDetails).setOfferToken(offerToken!!).build()
                                )
                                val userJsonEncoded = AuthUtil.encodeToBase64(
                                    Gson().toJson(
                                        SubscriptionProfile(
                                            userId = user.id,
                                            timezone = DateTimeUtils.getTimeZoneFromEpochTime(System.currentTimeMillis()),
                                            paymentFor = 1
                                        )
                                    )
                                )
                                val billingFlowParams = BillingFlowParams.newBuilder().setProductDetailsParamsList(productDetailsParamsList).setObfuscatedAccountId(BuildConfig.BUILD_TYPE)
                                    .setObfuscatedProfileId(userJsonEncoded).build()
                                billingClient.launchBillingFlow(requireActivity(), billingFlowParams)
                                viewModel.productDetails.postValue(productDetails)
                            }
                        }
                    }
                }else{
                    val binding = LayoutAlreadySubscribedBinding.inflate(LayoutInflater.from(requireContext()))
                    val customView = binding.root
                    val materialAlertDialog = MaterialAlertDialogBuilder(requireContext()).setView(customView).setCancelable(true).create()
                    materialAlertDialog.show()
                }
            }

        }

        viewModel.pricingPhase.observe(viewLifecycleOwner){
            requireActivity().runOnUiThread {
//                binding.amount.text = it?.formattedPrice
                binding.amount.text = getString(R.string.upgrade_premium_price,it?.formattedPrice)
            }

        }
        viewModel.subscriptionError.observe(viewLifecycleOwner) {
            it?.let {
                Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
            }
        }
        viewModel.subscription.observe(viewLifecycleOwner) {
            it?.let {
                if(it){
                    Toast.makeText(requireContext(), getString(R.string.title_subscription_sucess), Toast.LENGTH_SHORT).show()
                    findNavController().navigateSafe(UpgradePremiumFragmentDirections.actionUpgradePremiumFragmentToAlreadySubscribedFragment(true))
                }
            }
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            if (!purchase.isAcknowledged) {
                val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                    .setPurchaseToken(purchase.purchaseToken)
                lifecycleScope.launch {
                    withContext(Dispatchers.IO) {
                        billingClient.acknowledgePurchase(acknowledgePurchaseParams.build()) {
                            if(it.responseCode==BillingResponseCode.OK){
                                viewModel.setPurchase(purchase)
                            }
                        }
                    }
                }
            }
        }

    }


    private fun getSubscriptionDetails(){
        billingClient.startConnection(object :BillingClientStateListener{

            override fun onBillingServiceDisconnected() {
                requireActivity().runOnUiThread{
                    Toast.makeText(requireContext(),"Disconnected",Toast.LENGTH_SHORT).show()
                }
            }
            override fun onBillingSetupFinished(result: BillingResult) {
                val executerService=Executors.newSingleThreadExecutor()
                executerService.execute {
                    val productList= listOf(QueryProductDetailsParams.Product.newBuilder().setProductId(SUBSCRIPTION_ID).setProductType(BillingClient.ProductType.SUBS).build())
                    val params=QueryProductDetailsParams.newBuilder().setProductList(productList)
                    billingClient.queryProductDetailsAsync(params.build()) { billingResult, productDetailsList ->
                        for (productDetails in productDetailsList) {
                            if (productDetails.productId == SUBSCRIPTION_ID) {
                                productDetails.subscriptionOfferDetails?.get(0)?.pricingPhases?.pricingPhaseList?.get(0)?.let { viewModel.setPricingPhase(it) }
                            }
                        }
                    }
                }
            }
        })
    }

    override fun onPause() {
        super.onPause()
        billingClient.endConnection()
    }

    override fun onDestroy() {
        super.onDestroy()
        billingClient.endConnection()
    }
    companion object
    {
        const val SUBSCRIPTION_ID = "flashat.premium.autorenew"
        const val SUBSCRIPTION_KEY = "subscription"
    }
}