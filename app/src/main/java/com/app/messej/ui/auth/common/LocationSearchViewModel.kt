package com.app.messej.ui.auth.common

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.PlacesAutoCompleteResponse
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@OptIn(FlowPreview::class)
class LocationSearchViewModel(application: Application) : AndroidViewModel(application) {

    private val profileRepo = ProfileRepository(getApplication())

    var searchKeyWord = MutableLiveData<String>(null)

    private val _suggestions: MutableLiveData<MutableList<PlacesAutoCompleteResponse.Predictions>> = MutableLiveData(null)
    val suggestions: MutableLiveData<MutableList<PlacesAutoCompleteResponse.Predictions>> = _suggestions

    init {
        viewModelScope.launch {
            searchKeyWord.asFlow().debounce(300L).collect {
                it?: return@collect
                Log.d("LSVM", "search keyword is: $it")
                initiateSearch(it)
            }
        }
    }

    private fun initiateSearch(s: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PlacesAutoCompleteResponse> =
                profileRepo.getPlacesAutoComplete(s)) {
                is ResultOf.Success -> {
                    withContext(Dispatchers.Main) {
                        _suggestions.value = result.value.predictions
                    }
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

}