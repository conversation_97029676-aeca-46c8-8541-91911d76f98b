package com.app.messej.ui.home.publictab.socialAffairs.caseInfo

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.bundleOf
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.api.socialAffairs.SocialCaseInfo
import com.app.messej.data.model.enums.MySocialSupportActionMenuItem
import com.app.messej.data.model.enums.SocialVoteAction
import com.app.messej.databinding.FragmentSocialCaseInfoBinding
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showSocialConfirmAlertDialog
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showSocialErrorAlert
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showVotingConfirmationAlert
import com.app.messej.ui.home.publictab.socialAffairs.activeCases.SocialActiveCasesFragment.Companion.SOCIAL_ACTIVE_CASE_UPDATE_REQUEST
import com.app.messej.ui.home.publictab.socialAffairs.committee.SocialCommitteeFragmentDirections
import com.app.messej.ui.home.publictab.socialAffairs.donateBottomSheet.SocialDonateBottomSheetFragment.Companion.SOCIAL_DONATE_UPDATE_REQUEST
import com.app.messej.ui.home.publictab.socialAffairs.questions.SocialAskedQuestionsFragment.Companion.SOCIAL_QUESTIONS_COUNT
import com.app.messej.ui.home.publictab.socialAffairs.questions.SocialAskedQuestionsFragment.Companion.SOCIAL_QUESTIONS_COUNT_UPDATE
import com.app.messej.ui.home.publictab.socialAffairs.requestPersonalSupport.RequestPersonalSupportFragment.Companion.SOCIAL_SUPPORT_REQUEST_KEY
import com.app.messej.ui.utils.FragmentExtensions.addAsMenuHost
import com.app.messej.ui.utils.FragmentExtensions.setFragmentResultListenerOnActivity
import com.app.messej.ui.utils.FragmentExtensions.showToast
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.gson.Gson

class SocialCaseInfoFragment : Fragment(), SocialCaseInfoListeners {

    private lateinit var binding: FragmentSocialCaseInfoBinding
    private val viewModel : SocialCaseInfoViewModel by viewModels()
    private val args: SocialCaseInfoFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_social_case_info, container,false)
        binding.lifecycleOwner = viewLifecycleOwner
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).apply {
            binding.customActionBar.toolbar.apply {
                setupActionBar(toolBar = this, customNavIcon = R.drawable.ic_social_back_button)
                setNavigationOnClickListener { findNavController().navigateUp() }
            }
        }
        binding.customActionBar.toolBarTitle.text = getString(R.string.social_case_info_title)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        addAsMenuHost()
        observe()
    }

    private fun setup() {
        viewModel.setCaseId(id = args.id, isUpgradeSupport = args.isUpgradeSupport)
        binding.composeView.setContent {
            SocialCaseInfoComposeScreen(
                viewModel = viewModel,
                listener = this
            )
        }
    }

    private fun observe() {
        viewModel.errorMessage.observe(viewLifecycleOwner) { msg ->
            msg?.let { showToast(message = it) }
        }

        viewModel.onVoteError.observe(viewLifecycleOwner) {
            it ?: return@observe
            showSocialErrorAlert(error = it)
        }

        viewModel.onVoteSuccess.observe(viewLifecycleOwner) {
            if (it) {
                loadCaseDetailAndSetFragmentResultForLoadActiveCaseList()
            }
        }

        viewModel.onCaseApproved.observe(viewLifecycleOwner) {
            if (it) {
                loadCaseDetailAndSetFragmentResultForLoadActiveCaseList()
            }
        }

        setFragmentResultListenerOnActivity(requestKey = SOCIAL_QUESTIONS_COUNT_UPDATE) { _, bundle ->
            val updatedCount = bundle.getInt(SOCIAL_QUESTIONS_COUNT)
            Log.d("SAQ", "Updated Count Pass to Case Info Fragment")
            viewModel.updateQuestionsCount(newQuestionsCount = updatedCount)
        }

        setFragmentResultListenerOnActivity(requestKey = SOCIAL_DONATE_UPDATE_REQUEST) { _, _ ->
            viewModel.getCaseInfoDetail()
            requireActivity().supportFragmentManager.setFragmentResult(SOCIAL_ACTIVE_CASE_UPDATE_REQUEST, bundleOf())
        }

        viewModel.isCaseStatusChanged.observe(viewLifecycleOwner) {
            requireActivity().supportFragmentManager.setFragmentResult(SOCIAL_SUPPORT_REQUEST_KEY, bundleOf())
        }

        viewModel.onCaseDeletedMessage.observe(viewLifecycleOwner) { msg ->
            showToast(message = msg)
        }

    }

    private fun loadCaseDetailAndSetFragmentResultForLoadActiveCaseList() {
        requireActivity().supportFragmentManager.setFragmentResult(SOCIAL_ACTIVE_CASE_UPDATE_REQUEST, bundleOf())
    }

    override fun onVote(action: SocialVoteAction, caseId: Int?) {
        //Uncomment if needed to show vote confirmation dialog
//        showVotingConfirmationAlert(
//            onConfirm = {
                viewModel.vote(caseId = caseId, voteAction = action)
//            }
//        )
    }

    override fun onArchive() {
        showSocialConfirmAlertDialog(
            message = getString(R.string.social_support_archive_confirmation),
            onConfirm = {
                viewModel.changePersonalCaseStatus(newStatus = MySocialSupportActionMenuItem.Archive)
            }
        )
    }

    override fun onUnArchive() {
        viewModel.changePersonalCaseStatus(newStatus = MySocialSupportActionMenuItem.UnArchive)
    }

    override fun onDelete(caseInfo: SocialCaseInfo?) {
        if (caseInfo == null) return
        showSocialConfirmAlertDialog(
            message = getString(if (caseInfo.isNewCase) R.string.social_support_delete_confirmation else R.string.social_support_archive_delete_confirmation),
            onConfirm = {
                viewModel.changePersonalCaseStatus(newStatus = MySocialSupportActionMenuItem.Delete)
            }
        )
    }

    override fun onViewVotersList() {
        findNavController().navigateSafe(
            direction = SocialCaseInfoFragmentDirections.actionSocialAffairCaseInfoFragmentToSocialAffairVotersFragment(caseId = args.id)
        )
    }

    override fun onViewAskedQuestions(caseId: Int) {
        findNavController().navigateSafe(
            direction = SocialCaseInfoFragmentDirections.actionSocialAffairCaseInfoFragmentToSocialAffairAskedQuestionsFragment(caseId = caseId)
        )
    }

    override fun onDonate(case: SocialCaseInfo) {
        findNavController().navigateSafe(
            direction = SocialCaseInfoFragmentDirections.actionSocialDonateBottomSheetFragment(
                socialCase = Gson().toJson(case),
                isFromCaseInfo = true
            )
        )
    }

    override fun onUserDpClick(userId: Int) {
        findNavController().navigateSafe(
            direction = SocialCommitteeFragmentDirections.actionGlobalPublicUserProfileFragment(id = userId)
        )
    }

    override fun onHoldCase() {
        findNavController().navigateUp()
    }
}