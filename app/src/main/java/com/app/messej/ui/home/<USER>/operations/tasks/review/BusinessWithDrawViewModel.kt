package com.app.messej.ui.home.businesstab.operations.tasks.review

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.api.PayoutRequest
import com.app.messej.data.model.api.business.PayoutEligibility
import com.app.messej.data.model.api.subscription.Subscription
import com.app.messej.data.model.entity.BusinessOperation
import com.app.messej.data.model.entity.BusinessStatement
import com.app.messej.data.model.enums.PayoutAccountType
import com.app.messej.data.model.enums.UserSubscriptionStatus
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.BusinessRepository
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import com.app.messej.ui.composeComponents.ComposeTextFieldState
import com.app.messej.ui.utils.DataFormatHelper.formatDecimalWithRemoveTrailingZeros
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.math.BigDecimal
import java.math.RoundingMode

class BusinessWithDrawViewModel(application: Application): AndroidViewModel(application) {


    private val accountRepo = AccountRepository(application)
    val user: CurrentUser get() = accountRepo.user


    val isEgyptianUser: Boolean get() = user.isEgyptianUser

    // Add reset method to clear values when navigating between screens
    fun resetValues() {
        _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
        _isFullAmount.postValue(false)
        _redeemFlax.postValue(0.0)
        _processingFee.postValue(0.0)
        _transferFee.postValue(0.0)
        _payOutValue.postValue(0.0)
        _sendValue.postValue(0.0)
        _errorMessage.postValue(null)
    }

    private var businessRepo: BusinessRepository = BusinessRepository(application)
    val businessStatement: LiveData<BusinessStatement?> = businessRepo.getStatements()
    val businessOperation: LiveData<BusinessOperation?> = businessRepo.getOperations()
    private val profileRepo = ProfileRepository(application)

    val onFlaxSellRequest = LiveEvent<Boolean>()

    val onPayoutSuccess=LiveEvent<Boolean?>()
    val _backEndDbID=MutableLiveData<Int?>(null)


    val _errorMessage = LiveEvent<String?>()
//    val errorMessage: LiveData<String?> = _errorMessage

    private val _isLoading = MutableLiveData(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isEligibilityLoading = MutableLiveData(false)
    val isEligibilityLoading: LiveData<Boolean> = _isEligibilityLoading

    private val _isFullAmount = MutableLiveData<Boolean?>(false)
    private val isFullAmount: LiveData<Boolean?> = _isFullAmount

    private val _withdrawErrorTypes = MutableLiveData(WithDrawErrorTypes.NONE)
   val withdrawErrorTypes: LiveData<WithDrawErrorTypes?> = _withdrawErrorTypes

    private val _redeemFlax = MutableLiveData(0.0)
    val redeemFlax: LiveData<Double?> = _redeemFlax

    private val _processingFee = MutableLiveData(0.0)
    val processingFee: LiveData<Double?> = _processingFee


    private val _flaxRateToday = MutableLiveData(0.0)
    val flaxRateToday: LiveData<Double?> = _flaxRateToday

    private val _egyptOfficeFee = MutableLiveData(0.0)
    val egyptOfficeFee: LiveData<Double?> = _egyptOfficeFee

    private val _selectedAccountType = MutableLiveData(PayoutAccountType.PrimaryAccount)
    val selectedAccountType: LiveData<PayoutAccountType> = _selectedAccountType

    val primaryAccountTextFieldState = ComposeTextFieldState()
    val socialAccountTextFieldState = ComposeTextFieldState()

    fun setAccountType(type: PayoutAccountType) {
        _selectedAccountType.postValue(type)
        setFullAmountChecked(checked = true, selectedAccountType = type)
        primaryAccountTextFieldState.text = payoutEligibility.value?.normalPayOutInfo?.ppValue.formatDecimalWithRemoveTrailingZeros()
        socialAccountTextFieldState.text = payoutEligibility.value?.socialPayOutInfo?.ppValue.formatDecimalWithRemoveTrailingZeros()
    }

    init {
    getSubscriptionDetails()
    }

    val submitButtonEnable: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        fun check() {
            med.postValue(tncAccepted.value == true)
        }
        med.addSource(tncAccepted) { check() }
        med
    }

    private val _transferFee = MutableLiveData(0.0)
    val transferFee: LiveData<Double?> = _transferFee

    private val _payOutValue = MutableLiveData(0.0)
     val payOutValue: LiveData<Double?> = _payOutValue

    private val _sendValue = MutableLiveData(0.0)
    private val sendValue: LiveData<Double?> = _sendValue


    val tncAccepted = MutableLiveData<Boolean>(false)

    var payoutEligibility = LiveEvent<PayoutEligibility?>()
    private val _eligibilityCheckApiFail = MutableLiveData<String?>(null)
    private  val eligibilityCheckApiFail: LiveData<String?> = _eligibilityCheckApiFail
enum class WithDrawErrorTypes{GRATER,LESSER,RECEIVE_LESSER,NONE,EMPTY,MAX_EXCEEDED}
     val isSubmitButtonEnabled: MediatorLiveData<Boolean> by lazy {
         val med = MediatorLiveData<Boolean>()
         med.addSource(_isFullAmount) { showError() }
         med.addSource(_withdrawErrorTypes) { showError() }
         med.addSource(_payOutValue) { showError() }
         med
    }

    val isShowAmountError: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData<Boolean>()
        med.addSource(_isFullAmount) { showAmountError() }
        med.addSource(_withdrawErrorTypes) { showAmountError() }
        med.addSource(_payOutValue) { showAmountError() }
        med
    }

    private fun showAmountError() {
        if(isFullAmount.value == true){
           isShowAmountError.postValue(false)
        }else{
            if(_withdrawErrorTypes.value!=WithDrawErrorTypes.NONE &&  _withdrawErrorTypes.value!=WithDrawErrorTypes.EMPTY){
                isShowAmountError.postValue(true)
            }else{
                isShowAmountError.postValue(false)
            }
        }
    }

    private fun showError() {
//        if(_isFullAmount.value==true){
//            isSubmitButtonEnabled.postValue(true)
//        }else{
            if (_withdrawErrorTypes.value==WithDrawErrorTypes.NONE && (_payOutValue.value ?: 0.0) > 0.0 ){
                isSubmitButtonEnabled.postValue(true)
            }else{
                isSubmitButtonEnabled.postValue(false)
            }
//        }
    }

    private fun payoutRequest() {

        viewModelScope.launch(Dispatchers.IO) {
            _isLoading.postValue(true)
            when (val result: ResultOf<PayoutRequest> = businessRepo.payoutRequest(points = sendValue.value?:0.0, isSocialPayout = _selectedAccountType.value == PayoutAccountType.SocialAccount)) {
                is ResultOf.APIError -> {
                    onPayoutSuccess.postValue(false)
                    _errorMessage.postValue(result.error.message)
                }
                is ResultOf.Error -> {
                    onPayoutSuccess.postValue(false)
                }
                is ResultOf.Success -> {
                    _backEndDbID.postValue(result.value.payoutId)
                    businessRepo.getFlashAtActivityDetails()
                    onPayoutSuccess.postValue(true)
                }
            }
            _isLoading.postValue(false)
        }
    }

    fun setFullAmountChecked(checked: Boolean, selectedAccountType: PayoutAccountType?) {
        val ppValue = if (selectedAccountType == PayoutAccountType.PrimaryAccount) payoutEligibility.value?.normalPayOutInfo?.ppValue else payoutEligibility.value?.socialPayOutInfo?.ppValue
        Log.d("BWV", "PP Value -> $ppValue")
        Log.d("BWV", "Selected account Type -> ${_selectedAccountType.value}")
        _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
        _isFullAmount.value=checked
        if(checked){
            _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
            _redeemFlax.postValue(ppValue)
            val percentage = (payoutEligibility.value?.processingFee?.div(100))?.times(ppValue ?: 0.0)
            val roundedPercentage = BigDecimal(percentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
            val transferFeePercentage = (payoutEligibility.value?.transferFee?.div(100))?.times(ppValue ?: 0.0)
            val roundedTransferFeePercentage = BigDecimal(transferFeePercentage?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
            
            // Calculate Egypt office fees if user is Egyptian
            val roundedEgyptFeePercentage = if (isEgyptianUser) {
                val egyptFeePercentage = (payoutEligibility.value?.menaFeesPercentage?.div(100))?.times(ppValue ?: 0.0)
                BigDecimal(egyptFeePercentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
            } else {
                0.0
            }
            _egyptOfficeFee.postValue(roundedEgyptFeePercentage)
            
            _transferFee.postValue(roundedTransferFeePercentage)
            _processingFee.postValue(roundedPercentage)
            val roundedPP = BigDecimal(ppValue ?: 0.0)
                .subtract(BigDecimal(roundedPercentage))
                .subtract(BigDecimal(roundedTransferFeePercentage))
                .subtract(BigDecimal(roundedEgyptFeePercentage)) // Use direct value instead of LiveData
                .setScale(2, RoundingMode.HALF_UP)
                .toDouble()
            val payoutValue = BigDecimal(roundedPP * (flaxRateToday.value?.div(100) ?: 0.0)).setScale(2, RoundingMode.HALF_UP).toDouble()
            _payOutValue.postValue(payoutValue)
            _sendValue.postValue(ppValue)
        }else{
            _withdrawErrorTypes.postValue(WithDrawErrorTypes.EMPTY)
        }
    }

    fun eligibilityCheck() {
        _isEligibilityLoading.postValue(true)
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<PayoutEligibility> = businessRepo.payOutEligibility()) {
                is ResultOf.Success -> {
                    _isEligibilityLoading.postValue(false)
                    payoutEligibility.postValue(result.value)
                    val ppValue = if (_selectedAccountType.value == PayoutAccountType.PrimaryAccount) result.value.normalPayOutInfo?.ppValue else result.value.socialPayOutInfo?.ppValue
                    val percentage = (result.value.processingFee?.div(100))?.times(ppValue ?: 0.0)
                    val roundedPercentage = BigDecimal(percentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                    val transferFeePercentage = (result.value.transferFee?.div(100))?.times(ppValue ?: 0.0)
                    val roundedTransferFeePercentage = BigDecimal(transferFeePercentage?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                    
                    // Calculate Egypt office fees if user is Egyptian
                    val roundedEgyptFeePercentage = if (isEgyptianUser) {
                        val egyptFeePercentage = (result.value.menaFeesPercentage?.div(100))?.times(ppValue ?: 0.0)
                        BigDecimal(egyptFeePercentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                    } else {
                        0.0
                    }
                    _egyptOfficeFee.postValue(roundedEgyptFeePercentage)
                    
                    _transferFee.postValue(roundedTransferFeePercentage)
                    _processingFee.postValue(roundedPercentage)
                    _flaxRateToday.postValue(result.value.flaxRate)
                    _sendValue.postValue(ppValue)
                    val roundedPP = BigDecimal(ppValue ?: 0.0)
                        .subtract(BigDecimal(roundedPercentage))
                        .subtract(BigDecimal(roundedTransferFeePercentage))
                        .subtract(BigDecimal(roundedEgyptFeePercentage)) // Use direct value instead of LiveData
                        .setScale(2, RoundingMode.HALF_UP)
                        .toDouble()
                    setPayoutValue(roundedPP)
                }
                is ResultOf.APIError->{
                    _isEligibilityLoading.postValue(false)
                    _eligibilityCheckApiFail.postValue(result.error.message)
                }
                is ResultOf.Error->{
                    _isEligibilityLoading.postValue(false)
                    _eligibilityCheckApiFail.postValue(result.exception.message)
                }

            }
        }
    }

    private fun setPayoutValue(amount:Double){
        _payOutValue.postValue(amount)
        val ppValue = if (_selectedAccountType.value == PayoutAccountType.PrimaryAccount) payoutEligibility.value?.normalPayOutInfo?.ppValue else payoutEligibility.value?.socialPayOutInfo?.ppValue
        _redeemFlax.postValue(ppValue)
    }

    fun actionPayoutRequest(isFullAmount: Boolean) {
        if(isFullAmount) {setPayoutValue(_payOutValue.value?:0.0)
            val ppValue = if (_selectedAccountType.value == PayoutAccountType.PrimaryAccount) payoutEligibility.value?.normalPayOutInfo?.ppValue else payoutEligibility.value?.socialPayOutInfo?.ppValue
            _sendValue.postValue(ppValue)
        }
        payoutRequest()
    }

    fun setCustomAmount(customAmount: String) {
        setFullAmountChecked(checked = false, selectedAccountType = _selectedAccountType.value)
        val ppValue = if (_selectedAccountType.value == PayoutAccountType.PrimaryAccount) payoutEligibility.value?.normalPayOutInfo?.ppValue else payoutEligibility.value?.socialPayOutInfo?.ppValue
        if (customAmount.isEmpty()){
            if(_isFullAmount.value==true){
                _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
            }else{
                _redeemFlax.postValue(0.0)
                _processingFee.postValue(0.0)
                _transferFee.postValue(0.0)
                _egyptOfficeFee.postValue(0.0)
                _payOutValue.postValue(0.0)
                _sendValue.postValue(0.0)
                _withdrawErrorTypes.postValue(WithDrawErrorTypes.EMPTY)
            }
        }else{
            if(_isFullAmount.value == false) {
                _redeemFlax.value=customAmount.toDoubleOrNull()?:0.0
                
                // Check if amount exceeds maximumPP for Egyptian users
                if (isEgyptianUser && (customAmount.toDoubleOrNull() ?: 0.0) > (payoutEligibility.value?.maximumPP ?: 0.0)) {
                    _withdrawErrorTypes.postValue(WithDrawErrorTypes.MAX_EXCEEDED)
                    _payOutValue.postValue(0.0)
                    _sendValue.postValue(0.0)
                    _processingFee.postValue(0.0)
                    _transferFee.postValue(0.0)
                    _egyptOfficeFee.postValue(0.0)
                    return
                }
                
                val percentage = (payoutEligibility.value?.processingFee?.div(100))?.times(customAmount.toDoubleOrNull()?: 0.0)
                val roundedPercentage = BigDecimal(percentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                val transferFeePercentage = (payoutEligibility.value?.transferFee?.div(100))?.times(customAmount.toDoubleOrNull()?: 0.0)
                val roundedTransferFeePercentage = BigDecimal(transferFeePercentage?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                
                // Calculate Egypt office fees if user is Egyptian
                val roundedEgyptFeePercentage = if (isEgyptianUser) {
                    val egyptFeePercentage = (payoutEligibility.value?.menaFeesPercentage?.div(100))?.times(customAmount.toDoubleOrNull()?: 0.0)
                    BigDecimal(egyptFeePercentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                } else {
                    0.0
                }
                _egyptOfficeFee.postValue(roundedEgyptFeePercentage)
                
                _processingFee.value=roundedPercentage
                _transferFee.value=roundedTransferFeePercentage
                if ((customAmount.toDoubleOrNull() ?: 0.0) > (ppValue ?: 0.0)) {
                    _payOutValue.postValue(0.0)
                    _sendValue.postValue(0.0)
                    _processingFee.postValue(0.0)
                    _transferFee.postValue(0.0)
                    _egyptOfficeFee.postValue(0.0)
                    _withdrawErrorTypes.postValue(WithDrawErrorTypes.GRATER)
                } else {
                    _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
                    if(redeemFlax.value!! < (payoutEligibility.value?.minimumPP ?: 0.0)){
                        _withdrawErrorTypes.postValue(WithDrawErrorTypes.LESSER)
                        _payOutValue.postValue(0.0)
                        _sendValue.postValue(0.0)
                        _processingFee.postValue(0.0)
                        _transferFee.postValue(0.0)
                        _egyptOfficeFee.postValue(0.0)
                    }else{
                        if(redeemFlax.value!! < (payoutEligibility.value?.minimumPP ?: 0.0)){
                            _withdrawErrorTypes.postValue(WithDrawErrorTypes.RECEIVE_LESSER)
                            _payOutValue.postValue(0.0)
                            _sendValue.postValue(0.0)
                            _processingFee.postValue(0.0)
                            _transferFee.postValue(0.0)
                            _egyptOfficeFee.postValue(0.0)
                        }else{
                            _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
                            _processingFee.postValue(roundedPercentage)
                            _sendValue.postValue(_redeemFlax.value)
                            val payoutValue = BigDecimal(((_redeemFlax.value ?: 0.0) - roundedPercentage - roundedTransferFeePercentage - 
                                roundedEgyptFeePercentage) * // Use direct value instead of LiveData
                                (flaxRateToday.value?.div(100) ?: 0.0)).setScale(2, RoundingMode.HALF_UP).toDouble()
                            _payOutValue.postValue(payoutValue)
                        }
                    }
                }
            }else{
                _withdrawErrorTypes.postValue(WithDrawErrorTypes.NONE)
                _isFullAmount.postValue(true)
                _redeemFlax.postValue(ppValue)
                val percentage = (payoutEligibility.value?.processingFee?.div(100))?.times(ppValue ?: 0.0)
                val roundedPercentage = BigDecimal(percentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                val transferFeePercentage = (payoutEligibility.value?.transferFee?.div(100))?.times(ppValue ?: 0.0)
                val roundedTransferFeePercentage = BigDecimal(transferFeePercentage?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                
                // Calculate Egypt office fees if user is Egyptian
                val roundedEgyptFeePercentage = if (isEgyptianUser) {
                    val egyptFeePercentage = (payoutEligibility.value?.menaFeesPercentage?.div(100))?.times(ppValue ?: 0.0)
                    BigDecimal(egyptFeePercentage ?: 0.0).setScale(2, RoundingMode.HALF_UP).toDouble()
                } else {
                    0.0
                }
                _egyptOfficeFee.postValue(roundedEgyptFeePercentage)
                
                _processingFee.postValue(roundedPercentage)
                _transferFee.postValue(roundedTransferFeePercentage)
                _sendValue.postValue(ppValue)
                val payoutValue = BigDecimal((customAmount.toDouble() - roundedPercentage - roundedTransferFeePercentage - 
                    roundedEgyptFeePercentage) * // Use direct value instead of LiveData
                    (flaxRateToday.value?.div(100) ?: 0.0)).setScale(2, RoundingMode.HALF_UP).toDouble()
                _payOutValue.postValue(payoutValue)
            }
        }
    }

    fun setOnFlaxSellRequest(value: Boolean) {
        onFlaxSellRequest.postValue(value)
    }

    private val _isActive = MutableLiveData<UserSubscriptionStatus?>(null)
    val isActive: LiveData<UserSubscriptionStatus?> = _isActive

    private fun getSubscriptionDetails() {

        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<Subscription> =
                profileRepo.getSubscriptionDetails()) {
                is ResultOf.Success -> {
                    if(result.value.status.equals("active")){
                        _isActive.postValue(UserSubscriptionStatus.ACTIVE)
                    }else{
                        _isActive.postValue(UserSubscriptionStatus.EXPIRED)
                    }
                }
                is ResultOf.APIError -> {
                    _isActive.postValue(UserSubscriptionStatus.FREE)
                }
                is ResultOf.Error -> {
                }
            }
        }
    }

}
