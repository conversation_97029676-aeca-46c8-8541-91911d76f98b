package com.app.messej.ui.chat

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.app.messej.data.model.api.NearbyPlacesCompleteResponse
import com.app.messej.data.repository.ProfileRepository
import com.app.messej.data.utils.ResultOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class LocationAttachViewModel(application: Application) : AndroidViewModel(application) {

    private val profileRepo = ProfileRepository(application)

    private val _nearbyPlaces: MutableLiveData<MutableList<NearbyPlacesCompleteResponse.Results>?> = MutableLiveData(null)
    val nearbyPlaces: MutableLiveData<MutableList<NearbyPlacesCompleteResponse.Results>?> = _nearbyPlaces

    val searchLocationName = MutableLiveData<String?>(null)

    fun searchNearbyPlaces(lat: String, long: String) {
        viewModelScope.launch(Dispatchers.IO) {
            when (val result: ResultOf<NearbyPlacesCompleteResponse> =
                profileRepo.getNearbyPlaces("$lat,$long")) {
                is ResultOf.Success -> {
                    withContext(Dispatchers.Main) {
                        _nearbyPlaces.value = null
                        _nearbyPlaces.value = result.value.results
                    }
                }
                is ResultOf.APIError -> {
                }
                is ResultOf.Error -> {
                }
            }
        }
    }
}