package com.app.messej.ui.legal.report

import android.app.Application
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.annotation.DrawableRes
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.app.messej.MainApplication
import com.app.messej.data.model.CurrentUser
import com.app.messej.data.model.MediaTransfer
import com.app.messej.data.model.ReportPackage
import com.app.messej.data.model.ReportPackage.ReportPackageSense
import com.app.messej.data.model.ReportProofMedia
import com.app.messej.data.model.api.ReportCategoryResponse
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.ReportContentType
import com.app.messej.data.model.enums.ReportType
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.LegalAffairsRepository
import com.app.messej.data.repository.worker.MediaUploadListener
import com.app.messej.data.utils.MediaUtils
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.VideoEncoderUtil
import com.app.messej.ui.utils.CountryListUtil
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.gson.Gson
import com.hadilq.liveevent.LiveEvent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

class ReportViewModel(application: Application): AndroidViewModel(application) {

    private val repo = LegalAffairsRepository(getApplication())
    protected val accountRepo = AccountRepository(application)

    val user: CurrentUser get() = accountRepo.user

    companion object {
        const val COMMENT_MAX_LENGTH = 300
    }

    init {
        initCountryList()
    }

    private var _countryList: Map<String, Int>? = null

    private fun initCountryList() {
        viewModelScope.launch(Dispatchers.IO) {
            _countryList = CountryListUtil.getCustomCountryMap()
        }
    }

    @DrawableRes
    fun getCountryFlag(code: String?): Int {
        code ?: return 0
        return _countryList?.get(code)?:0
    }

    val onInvalidData = LiveEvent<Boolean>()

    fun setReportRequest(reportJson: String) {
        val preview = parseReport(reportJson)
        if (preview == null) {
            onInvalidData.postValue(true)
            return
        }
        getReportCategories()
    }

    private fun parseReport(json: String): ReportPackage? {
        try {
            Log.e("RPF", "parsing: $json")
            val base = Gson().fromJson(json, ReportPackageSense::class.java)
            Log.e("RPF", "base: $base")
            val preview = when (base.contentType) {
                ReportContentType.USER -> Gson().fromJson(json, ReportPackage.User::class.java)
                ReportContentType.HUDDLE_POST -> Gson().fromJson(json, ReportPackage.HuddlePost::class.java)
                ReportContentType.HUDDLE_COMMENT -> Gson().fromJson(json, ReportPackage.HuddlePostComment::class.java)
                ReportContentType.FLASH -> Gson().fromJson(json, ReportPackage.Flash::class.java)
                ReportContentType.FLASH_COMMENT -> Gson().fromJson(json, ReportPackage.FlashComment::class.java)
                ReportContentType.POSTAT -> Gson().fromJson(json, ReportPackage.Postat::class.java)
                ReportContentType.POSTAT_COMMENT -> Gson().fromJson(json, ReportPackage.PostatComment::class.java)
                ReportContentType.PODIUM -> Gson().fromJson(json, ReportPackage.PodiumReport::class.java)
            }
            Log.e("RPF", "parsed: $preview")
            preview.countryFlag = getCountryFlag(preview.user?.countryCode)
            _reportPackage.postValue(preview)
            return preview
        } catch (e: Exception) {
            Log.e("RPF", "parseReport", e)
            return null
        }
    }

    private val _reportPackage = MutableLiveData<ReportPackage?>(null)
    val reportPackage: LiveData<ReportPackage?> = _reportPackage

    val reportContentType = _reportPackage.map {
        it?: return@map null
        Pair(it.contentType,it.reportType)
    }.distinctUntilChanged()

    val isUserReport = _reportPackage.map {
        it is ReportPackage.User || it is ReportPackage.PodiumReport
    }

    val isBan = _reportPackage.map { it?.reportType == ReportType.BAN }

    private val _reportCategories : MutableLiveData<List<ReportCategoryResponse.ReportCategory>?> = MutableLiveData(null)
    val reportCategories: LiveData<List<ReportCategoryResponse.ReportCategory>?> = _reportCategories

    private fun getReportCategories(){
        viewModelScope.launch(Dispatchers.IO){
            when(val result = repo.getReportCategories()){
                is ResultOf.Success -> {
                    _reportCategories.postValue(result.value.categories)
                }
                is ResultOf.APIError -> { }
                is ResultOf.Error -> { }
            }
        }
    }

    val reason = MutableLiveData<ReportCategoryResponse.ReportCategory?>(null)
    val comment = MutableLiveData<String>()

    val commentValid = comment.map {
        return@map it.trim().length <= COMMENT_MAX_LENGTH
    }

    private val commentIsNotEmpty = comment.map {
        return@map it.trim().isNotEmpty()
    }

    // Proof

    private val _proofMedia = MutableLiveData<MutableList<ReportProofMedia>>(mutableListOf())

    val canAddProof = _proofMedia.map {
        return@map it.size < 5
    }

    fun MutableLiveData<MutableList<ReportProofMedia>>.add(item: ReportProofMedia) {
        val list = value?: mutableListOf()
        if (list.find { it.uriString == item.uriString }!=null) {
            // Duplicate item
            return
        }
        list.add(item)
        postValue(list)
    }

    fun MutableLiveData<MutableList<ReportProofMedia>>.remove(item: ReportProofMedia) {
        val list = value?: mutableListOf()
        list.remove(item)
        postValue(list)
    }

    fun addMedia(uri: Uri) {
        val contentResolver = getApplication<MainApplication>().contentResolver
        val mime = MediaUtils.getMimeTypeFromUri(uri,contentResolver).orEmpty()

        val meta = MediaUtils.getFileNameAndSizeFromUri(uri, contentResolver)
        /*
        By default, the system grants your app access to media files until the device is restarted or until your app stops.
        If your app performs long-running work, such as uploading a large file in the background,
        you might need this access to be persisted for a longer period of time
         */
        try {
            contentResolver.takePersistableUriPermission(uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        } catch (_: Exception) {
        }
        if (mime.startsWith("image")) {
            viewModelScope.launch(Dispatchers.IO) {
//                val file = repo.storeImageUriToTempFile(uri)
                _proofMedia.add(
                    ReportProofMedia(
                    uri = uri,
                    name = meta?.first.orEmpty(),
                    mimeType = mime
                )
                )
            }
        }
        if (mime.startsWith("video")) {
            viewModelScope.launch(Dispatchers.IO) {
//                val file = repo.storeImageUriToTempFile(uri)
                _proofMedia.add(
                    ReportProofMedia(
                    uri = uri,
                    name = meta?.first.orEmpty(),
                    mimeType = mime
                )
                )
            }
        }
    }

    fun removeMedia(item: ProofMediaUIModel) {
        _proofMedia.remove(item.proof)
    }

    // Submit

    private val _reportValid: MediatorLiveData<Boolean> by lazy {
        val med = MediatorLiveData(false)

        fun validate() {
            _reportValid.postValue(
//                commentValid.value == true && reason.value!=null && commentIsNotEmpty.value == true && if (isUserReport.value == true) !_proofMedia.value.isNullOrEmpty() else true
                reason.value!=null && if (isUserReport.value == true) !_proofMedia.value.isNullOrEmpty() else true
            )
        }
//        med.addSource(commentValid) {validate()}
        med.addSource(reason) {validate()}
        med.addSource(_proofMedia) {validate()}
//        med.addSource(commentIsNotEmpty) {validate()}
        med
    }

    val reportValid: LiveData<Boolean> = _reportValid

    private var reportVideoOngoingJob: Job? = null

    private val _reportSending = MutableLiveData(false)
    val reportSending: LiveData<Boolean> = _reportSending

    val onReportMessageComplete = LiveEvent<ReportContentType>()
    val onReportError = LiveEvent<String>()

    private fun setAllowProofDelete(allow: Boolean) {
        proofMedia.value?.forEach {
            it.allowDelete = allow
        }
    }

    fun reportMessage() {
        reportVideoOngoingJob?.let {
            if (it.isActive) {
                Log.w("RPF", "reportVideoOngoingJob is active")
                return
            }
        }
        viewModelScope.launch(Dispatchers.IO){
            fun exit() {
                _reportSending.postValue(false)
                setAllowProofDelete(true)
            }
            val req = _reportPackage.value?.getRequest()?: return@launch
            req.categoryId = reason.value!!.categoryId
            req.explanation = comment.value.orEmpty()
            Log.w("RPF", "sending request: $req")
            _reportSending.postValue(true)
            setAllowProofDelete(false)
            val proofs = _proofMedia.value.orEmpty()
            proofs.forEach {
                val uiModel = proofMedia.value?.find { pm -> pm.proof.uuid == it.uuid }
                uiModel?.processing = true
                if (!it.isProcessed) {
                    try {
                        Log.w("RPF", "process media: $it")
                        processMedia(it)
                    } catch(e: Exception) {
                        uiModel?.processing = false
                        exit()
                        return@launch
                    }
                }
                if (it.mediaUploaded) return@forEach

                Log.w("RPF", "upload media: $it")
                when(repo.uploadMedia(it)) {
                    is ResultOf.Success -> { }
                    else -> {
                        // Upload failed.
                        uiModel?.processing = false
                        exit()
                        return@launch
                    }
                }
                uiModel?.processing = false
            }
            if (!proofs.all { it.isProcessed } || !proofs.all { it.mediaUploaded }) {
                // Error in media processing. TODO show an error toast maybe.
                exit()
                return@launch
            }
            req.proofFiles = proofs.map { it.s3UploadMedia.key }
            val result = repo.reportContent(req)
            when(result){
                is ResultOf.APIError -> {
                    onReportError.postValue(result.error.message)
                }
                is ResultOf.Error -> { }
                is ResultOf.Success -> {
                    onReportMessageComplete.postValue(req.contentType)
                }
            }
            _reportSending.postValue(false)
        }
    }

    private suspend fun processMedia(med: ReportProofMedia) = withContext(Dispatchers.IO) {
        reportVideoOngoingJob?.let {
            reportVideoOngoingJob = null
            if (it.isActive) it.cancelAndJoin()
        }
        launch {
            try {
                val transfer = MediaTransfer(med.uuid)
                _mediaEncode.postValue(transfer)
                if (med.processedFile != null) return@launch
                med.processedFile = when (med.mediaType) {
                    MediaType.IMAGE -> processImage(med)
                    MediaType.VIDEO -> processVideo(med, object : VideoEncoderUtil.MediaProcessingListener {
                        override fun onProgress(progress: Int) {
                            Log.d("ENCODE", "Progress: $progress%")
                            transfer.progress = progress
                        }

                        override fun onProcessingFinished(success: Boolean) {
                            Log.d("ENCODE", "Encode Done ($success) as per VM")
                        }
                    })

                    else -> null
                }
                _mediaEncode.postValue(null)
            } catch (e: Throwable) {
                Log.w("ENCODE", "ProcessVideo cancelled", e)
                Firebase.crashlytics.recordException(e)
                reportVideoOngoingJob = null
                _mediaEncode.postValue(null)
                throw Exception("media processing cancelled")
            }
        }.apply {
            reportVideoOngoingJob = this
            join()
        }
    }

    private suspend fun processVideo(med: ReportProofMedia, listener: VideoEncoderUtil.MediaProcessingListener): File = withContext(Dispatchers.IO) {

        Firebase.crashlytics.log("Starting Video compress")
        Log.d("ENCODE", "Starting Video compress")
        try {
            val result = repo.processVideo(med, listener)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "ProcessVideo cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("Video processing cancelled")
        }
    }

    private suspend fun processImage(med: ReportProofMedia): File = withContext(Dispatchers.IO) {
        Firebase.crashlytics.log("Starting image compress")
        Log.d("ENCODE", "Starting image compress")
        try {
            val imageFile = repo.storeImageUriToTempFile(med.uri)
            val result = repo.compressImage(imageFile)
            Log.d("ENCODE", "processed ${med.uri} to ${result.path}")
            Firebase.crashlytics.log("processed ${med.uri} to ${result.path}")
            return@withContext result
        } catch (e: Throwable) {
            Log.w("ENCODE", "Process image cancelled", e)
            Firebase.crashlytics.recordException(e)
            throw Exception("image processing cancelled")
        }

    }

    private val _mediaEncode = MutableLiveData<MediaTransfer?>(null)

    private val _mediaUpload: LiveData<List<MediaTransfer>?> = MediaUploadListener.uploadProgressFlow.map { prog ->
        val proofs = _proofMedia.value.orEmpty().map { it.uuid }
        return@map prog.filter { it.key in proofs }.map {
            MediaTransfer(it.key).apply {
                progress = it.value
            }
        }
    }.asLiveData(Dispatchers.Default + viewModelScope.coroutineContext)

    data class ProofMediaUIModel(
        val proof: ReportProofMedia
    ): BaseObservable() {
        private val progressBias: Float
            get() = if (proof.mediaType==MediaType.VIDEO) 0.3f else 0f

        @get:Bindable
        var processing: Boolean = false
            set(value) {
                field = value
                notifyPropertyChanged(BR.processing)
            }

        @get:Bindable
        var encodeProgress: Int = -1
            set(value) {
                field = value
                notifyPropertyChanged(BR.encodeProgress)
                updateTotalProgress()
            }

        @get:Bindable
        var uploadProgress: Int = -1
            set(value) {
                field = value
                notifyPropertyChanged(BR.uploadProgress)
                updateTotalProgress()
            }

        @get:Bindable
        var allowDelete: Boolean = true
            set(value) {
                field = value
                notifyPropertyChanged(BR.allowDelete)
            }

        private fun updateTotalProgress() {
            if(proof.mediaUploaded) {
                totalProgress = 100
                return
            }
            val ep = if(proof.isProcessed) 100 else encodeProgress.coerceAtLeast(0)
            val up = uploadProgress.coerceAtLeast(0)
            val prog = if (encodeProgress==-1 && uploadProgress==-1) null
            else (ep * progressBias) + (up * (1 - progressBias))
            totalProgress = prog?.toInt()
        }

        @get:Bindable
        var totalProgress: Int? = -1
            private set(value) {
                field = value
                notifyPropertyChanged(BR.totalProgress)
            }
    }

    val proofMedia: LiveData<List<ProofMediaUIModel>> by lazy {
        val med = MediatorLiveData<List<ProofMediaUIModel>>()
        fun update() {
            val _encode = _mediaEncode.value
            val uploads = _mediaUpload.value
            val current = proofMedia.value

            med.postValue(_proofMedia.value?.map { proof ->
                val uiModel = current?.find { ui -> ui.proof.uuid == proof.uuid }?: ProofMediaUIModel(proof)
                val encode = if (_encode?.messageId==proof.uuid) _encode else null
                val upload = uploads?.find { it.messageId == proof.uuid }
                uiModel.encodeProgress = encode?.progress?:-1
                uiModel.uploadProgress = upload?.progress?:-1
                Log.d("RPF", "proofMedia: ${uiModel.proof.name} | processing: ${uiModel.processing} | isProcessed: ${uiModel.proof.isProcessed} | encode: ${uiModel.encodeProgress} | uploaded: ${uiModel.proof.mediaUploaded} | upload: ${uiModel.uploadProgress} | total: ${uiModel.totalProgress}")
                return@map uiModel
            })
        }
        med.addSource(_proofMedia) { update() }
        med.addSource(_mediaEncode) { update() }
        med.addSource(_mediaUpload) { update() }
        med.distinctUntilChanged()
    }
}