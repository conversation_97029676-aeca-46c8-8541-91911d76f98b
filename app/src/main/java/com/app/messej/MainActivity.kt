package com.app.messej

import android.Manifest
import android.annotation.SuppressLint
import android.app.UiModeManager
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.LocaleList
import android.os.Looper
import android.os.Parcelable
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.DrawableRes
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.appcompat.widget.Toolbar
import androidx.appcompat.widget.TooltipCompat
import androidx.compose.runtime.livedata.observeAsState
import androidx.core.content.ContextCompat
import androidx.core.os.LocaleListCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.forEach
import androidx.core.view.updatePadding
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.afollestad.materialdialogs.MaterialDialog
import com.app.messej.data.Constants
import com.app.messej.data.model.api.podium.challenges.PodiumChallenge
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.enums.ChallengeType
import com.app.messej.data.model.enums.ForwardSource
import com.app.messej.data.model.enums.MediaType
import com.app.messej.data.model.enums.NightModeSetting
import com.app.messej.data.model.enums.PayFineType
import com.app.messej.data.model.enums.PodiumKind
import com.app.messej.data.model.enums.UserCitizenship
import com.app.messej.data.model.socket.SentGiftPayload
import com.app.messej.data.utils.CurrentTimeDisplay
import com.app.messej.data.utils.DateTimeUtils
import com.app.messej.data.utils.LogCatReader
import com.app.messej.databinding.ActivityMainBinding
import com.app.messej.ui.enforcements.EnforcementsViewModel
import com.app.messej.ui.home.CommonChallengeViewModel
import com.app.messej.ui.home.CommonGiftViewModel
import com.app.messej.ui.home.CommonHomeViewModel
import com.app.messej.ui.home.businesstab.HomeBusinessFragmentDirections
import com.app.messej.ui.home.publictab.flash.PublicFlashBaseFragment
import com.app.messej.ui.home.publictab.myETribe.showETribeSupperStarAlert
import com.app.messej.ui.home.publictab.podiums.live.ActivePodiumTracker
import com.app.messej.ui.home.publictab.socialAffairs.SocialAffairUtils.showPendingCaseAlertDialog
import com.app.messej.ui.utils.DataFormatHelper
import com.app.messej.ui.utils.FlashatDialog.Companion.showFlashatDialog
import com.app.messej.ui.utils.FragmentExtensions.adjustForBottomNavDot
import com.app.messej.ui.utils.FragmentExtensions.showInsufficientBalanceAlert
import com.app.messej.ui.utils.FragmentExtensions.showSnackbar
import com.app.messej.ui.utils.LocaleUtil
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.dynamiclinks.PendingDynamicLinkData
import com.google.firebase.dynamiclinks.ktx.dynamicLinks
import com.google.firebase.ktx.Firebase
import io.github.hyuwah.draggableviewlib.DraggableView
import io.github.hyuwah.draggableviewlib.setupDraggable
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    private var doubleBackToExitPressedOnce = false

    private lateinit var appBarConfig: AppBarConfiguration
    private lateinit var viewModel: CommonHomeViewModel
    private lateinit var giftViewModel: CommonGiftViewModel
    private lateinit var challengeViewModel: CommonChallengeViewModel
    private lateinit var enforcementViewModel: EnforcementsViewModel

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(newBase)
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            LocaleUtil.getPrimaryLocale()?.let { locale ->
                AppCompatDelegate.setApplicationLocales(LocaleListCompat.create(locale))
                val configuration = newBase?.resources?.configuration
                configuration?.setLocales(LocaleList(locale))
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        viewModel = ViewModelProvider(this)[CommonHomeViewModel::class.java]
        giftViewModel = ViewModelProvider(this)[CommonGiftViewModel::class.java]
        challengeViewModel = ViewModelProvider(this)[CommonChallengeViewModel::class.java]
        enforcementViewModel = ViewModelProvider(this)[EnforcementsViewModel::class.java]
        setTheme()
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = DataBindingUtil.setContentView(this, R.layout.activity_main)
        registerUpdateResult()
        setupExitWatcher()
        setupNavigation()
        observe()
        handleDynamicLinks()
        handleExternalData()
        setupLogCaptureUI()
    }

    private fun setupLogCaptureUI() {
        binding.logCapture.setContent {
            val isEnabled = viewModel.loggingEnabled.observeAsState(false)

            if (isEnabled.value && BuildConfig.BUILD_TYPE != Constants.BuildType.RELEASE.value) {
                CurrentTimeDisplay {
                    if (LogCatReader.isLogging) {
                        LogCatReader.stopLogging(this) { pair ->
                            binding.root.showSnackbar(LogCatReader.resultMessage(pair.second)) {
                                setAction("Open") {
                                    try {
                                        context.startActivity(pair.first)
                                    } catch (e: ActivityNotFoundException) {
                                        Toast.makeText(this@MainActivity, "No app can open this folder", Toast.LENGTH_SHORT).show()
                                    }
                                }
                            }
                        }
                    } else LogCatReader.startLogging(this)
                }
            }
        }

        binding.logCapture.setupDraggable().build().apply {
            sticky = DraggableView.Mode.STICKY_X
        }

        ViewCompat.setOnApplyWindowInsetsListener(binding.logCapture) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.updatePadding(top = insets.top, bottom = insets.bottom)
            windowInsets
        }
    }

    private fun handleExternalData() {
        if (intent?.action == Intent.ACTION_SEND) {
            if (viewModel.user == null) {
                navController.navigateSafe(NavGraphHomeDirections.actionGlobalNavGraphLogin())
                navController.popBackStack(R.id.nav_graph_login, true)
            } else {
                val text = intent.getStringExtra(Intent.EXTRA_TEXT)
                when (intent.type) {
                    "text/plain" -> {
                        text?.let {
                            navController.navigateSafe(NavGraphHomeDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.EXTERNAL, textMessage = it))
                        }
                    }

                    null -> {}

                    else -> handleMedia(intent.type, text)

                }
                intent.data = null
                intent.action = null
                intent.type = null
            }
        }
    }

    private fun handleMedia(type: String?, text: String?) {
        type?.let {
            val uri = getParcelableUri()
            val mediaType = when {
                it.startsWith("image/") -> MediaType.IMAGE
                it.startsWith("video/") -> MediaType.VIDEO
                it.startsWith("audio/") -> MediaType.DOCUMENT
                it.startsWith("application/") -> MediaType.DOCUMENT
                else -> null
            }
            Log.w("SHAREXT", "handleMedia: ${uri.toString()} | $text")
            mediaType?.let { mt ->
                navController.navigateSafe(NavGraphHomeDirections.actionGlobalForwardHomeFragment(srcType = ForwardSource.EXTERNAL, mediaType = mt.code, mediaUri = uri.toString(), textMessage = text))
            }
        }
    }

    private fun getParcelableUri(): Parcelable? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent?.getParcelableExtra(Intent.EXTRA_STREAM, Parcelable::class.java)
        } else {
            @Suppress("DEPRECATION") intent?.getParcelableExtra(Intent.EXTRA_STREAM)
        }
    }

    val navController
        get() = (supportFragmentManager.findFragmentById(R.id.nav_host_fragment_content_main) as NavHostFragment).navController

    private fun setTheme() {
        Log.d("MNACT", "setTheme: user is premium? ${viewModel.themeConfig}")
        viewModel.themeConfig?.also { config ->
            if (config.allowDayNightSetting) {
                viewModel.appSettings.value?.appNightMode?.let {
                    setDayNightMode(it)
                }
            } else setDayNightMode(NightModeSetting.FORCE_LIGHT)
            if (config.showPremiumTheme) setTheme(R.style.Theme_Flashat_Premium) else setTheme(R.style.Theme_Flashat_Free)

            WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars = !config.showPremiumTheme
        } ?: run {
            setDayNightMode(NightModeSetting.FORCE_LIGHT)
            setTheme(R.style.Theme_Flashat_Premium)
            WindowCompat.getInsetsController(window, window.decorView).isAppearanceLightStatusBars = true
        }
    }

    private var dayNightDialog: MaterialDialog? = null

    override fun onDestroy() {
        super.onDestroy()
        dayNightDialog?.dismiss()
        dayNightDialog = null
        updateFlowResultLauncher?.apply {
            unregister()
            updateFlowResultLauncher = null
        }
    }


    private fun setDayNightMode(night: NightModeSetting) {
        val uiModeManager = getSystemService(Context.UI_MODE_SERVICE) as UiModeManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val mode = when (night) {
                NightModeSetting.FOLLOW_SYSTEM -> UiModeManager.MODE_NIGHT_AUTO
                NightModeSetting.FORCE_LIGHT -> UiModeManager.MODE_NIGHT_NO
                NightModeSetting.FORCE_DARK -> UiModeManager.MODE_NIGHT_YES
            }
            uiModeManager.nightMode
            uiModeManager.setApplicationNightMode(mode)
        }
        val mode = when (night) {
            NightModeSetting.FOLLOW_SYSTEM -> AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM
            NightModeSetting.FORCE_LIGHT -> AppCompatDelegate.MODE_NIGHT_NO
            NightModeSetting.FORCE_DARK -> AppCompatDelegate.MODE_NIGHT_YES
        }
        AppCompatDelegate.setDefaultNightMode(mode)
    }

    override fun onResume() {
        super.onResume()
        checkForUpdates()
        viewModel.setAppLockTime()
        giftViewModel.readyToPlayGifts()
    }

    override fun onStop() {
        super.onStop()
        viewModel.setAppLockTime()
        giftViewModel.readyToPlayGifts(false)
    }

    private fun observe() {

        enforcementViewModel.enforcementsLiveData.observe(this) {
            Log.d("Enforcements", "User Enforcements -> $it")
            val isBlackListFragmentVisible = navController.currentDestination?.id == R.id.blackListFragment
            if (it?.enforcementsStatus?.blacklisted == true && !isBlackListFragmentVisible) {
                navController.navigateSafe(
                    NavGraphHomeDirections.actionGlobalBlackListFragment()
                )
            }
        }

        viewModel.isUpgradeSupportViolationDetected.observe(this) { isViolated ->
            val isBlackListFragmentVisible = navController.currentDestination?.id == R.id.blackListFragment
            if (isViolated == true && !isBlackListFragmentVisible) {
            Log.d("MAINACT", "isSocialViolated -> $isViolated")
                navController.navigateSafe(direction = NavGraphHomeDirections.actionGlobalBlackListFragment(payFineType = PayFineType.SOCIAL_PAY_FINE))
            }
        }

        giftViewModel.onGiftReceived.observe(this) {
            Log.d("QQQ", "" + it.toString())
            it.let { payload ->
                if (payload.hasVideo) {
                    giftViewModel.showGiftVideo(payload)
                } else {
                    payload.senderId?.let { senderId ->
                        navController.navigateSafe(NavGraphHomeDirections.actionGlobalNotificationLottieBottomSheetFragment(payload.id, senderId, true))
                    }
                }
            }
        }

        challengeViewModel.onChallengeContributorRequest.observe(this) {
            if(ActivePodiumTracker.isInPodiumScreen) return@observe
            Log.d("MAINACT", "onChallengeContributorRequest: $it")
            showChallengeContributorRequestAlert(it)
        }

        challengeViewModel.onMaidanSupportRequest.observe(this) {
            if (ActivePodiumTracker.isActive(ActivePodiumTracker.ActivePodiumScreen.PodiumRoom(it.podiumId))) return@observe
            if (ActivePodiumTracker.isAnthemPlaying) return@observe
            navController.navigateSafe(NavGraphHomeDirections.actionGlobalPodiumMaidanSupportBottomSheetFragment(it.invitedBy.userId,it.invitedBy.name,it.podiumId))
        }

        giftViewModel.onGiftVideoAvailable.observe(this) {
            navController.navigateSafe(NavGraphHomeDirections.actionGlobalGiftAlertVideoBottomSheetFragment())
        }

        viewModel.flaxTransfer.observe(this) {
            navController.navigateSafe(NavGraphHomeDirections.actionGlobalReceivedFlaxBottomSheetFragment())
        }
        viewModel.onNewpPresidentCrowned.observe(this){
//            navController.navigateSafe(NavGraphHomeDirections.actionGlobalPresidentBottomSheetFragment(true)) //president
            it.currentPresidentId?.let { president ->
                viewModel.setPresidentIdFromSocket(president.toString())
                navController.navigateSafe(NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(president))
            }
        }


        viewModel.citizenshipLevelUpgradeEvent.observe(this){ /** This event works for both own user and other user User-Level upgrades **/
            if (ActivePodiumTracker.isAnthemPlaying) return@observe
            if (it.citizenship in listOf(
                    UserCitizenship.VISITOR, UserCitizenship.RESIDENT, UserCitizenship.CITIZEN
                ) || it.purchased == true
            ) return@observe

            val userId = it.upgradedUserId
            navController.navigateSafe(
                NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(userId)
            )
            viewModel.user?.id?.let { userId -> viewModel.sendCitizenshipViewedAcknowledgement(userId, it.upgradedUserId) }
        }
        viewModel.adminAddedFlixCoisEvent.observe(this) {
            viewModel.updateAccount()
        }



        lifecycleScope.launch {
            viewModel.eTribeSuperstarMessageEvent.collect { event ->
                showETribeSupperStarAlert(
                    superStarName = event.superstarName,
                    message = event.message ?: "",
                    context = this@MainActivity
                )
            }
        }

        lifecycleScope.launch {
            viewModel.onSocialCasePendingApprovalEvent.collect { event ->
                showPendingCaseAlertDialog(data = event)
            }
        }

        viewModel.eTribeSuperStarMessages.observe(this) { message->
            val firstItem = message?.firstOrNull() ?: return@observe
            showETribeSupperStarAlert(
                superStarName = firstItem.superstarName,
                message = firstItem.message ?: "",
                context = this
            )
        }

//        viewModel.userLevelUpgradeEvent.observe(this) {
//            viewModel.levelUpgradationEvent.value?.apply {
//                navController.navigateSafe(NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(upgradedUserId,true))
//            }
//        }

        viewModel.userFlow.observe(this) {
            Log.d("USERPROFILE", "observe: ${it?.profile}")
            it?.let {
                checkNotificationPermission()
            }
        }

        viewModel.isPremiumUser.observe(this) {
            setBottomNav(it)
        }
        viewModel.onLevelUpgrades.observe(this) { levelUpdates ->
            if (ActivePodiumTracker.isAnthemPlaying) return@observe
            if (levelUpdates.first.upgradedUsersList?.isNotEmpty() == true) {
                (levelUpdates.first.upgradedUsersList?:emptyList()).forEach {
                    navController.navigateSafe(NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(it))
                    viewModel.user?.id?.let { userId -> viewModel.sendCitizenshipViewedAcknowledgement(userId, it) }
                }
            }

            if (levelUpdates.second.isNullOrEmpty()) {
                if (levelUpdates.first.animationForPresident?.isNotEmpty() == true) {
                    if (viewModel.user?.id != levelUpdates.first.currentPresidentId) {
//                    navController.navigateSafe(NavGraphHomeDirections.actionGlobalPresidentBottomSheetFragment()) //president
                        levelUpdates.first.currentPresidentId?.let {
                            navController.navigateSafe(NavGraphHomeDirections.actionGlobalLevelUpgradationBottomSheetFragment(it))
                        }

                    }
                }
            }
        }
        viewModel.onUserBirthday.observe(this) { birthday ->
            if (ActivePodiumTracker.isAnthemPlaying) return@observe
            if (birthday.isCurrentUserBday==true) {
                viewModel.user?.premium?.let { premium ->
                    if (premium) {
                        giftViewModel.showBirthdayResultVideo(birthday.birthdayAnimationUrlAndroid!!) //visible only if current user have birthday and also user is a premium
                    }
                }
            } else {
                if (!birthday.otherUserBirthdays.isNullOrEmpty()) {
                    birthday.otherUserBirthdays.getOrNull(0)?.userId?.let { id ->
                        navController.navigateSafe(NavGraphHomeDirections.actionGlobalBirthdayAlertBottomSheetFragment(currentBirthday = true)) //visible only if other users have birthday
                    }
                }
            }

        }

        viewModel.unreadPublicHuddles.observe(this) {
            if (binding.bottomNav.menu.findItem(R.id.homePublicFragment) != null) {
                binding.bottomNav.getOrCreateBadge(R.id.homePublicFragment).apply {
                    isVisible = it > 0
                    adjustForBottomNavDot(this@MainActivity)
                }
            }
            if (binding.bottomNav.menu.findItem(R.id.publicHuddlesStandaloneFragment) != null) {
                binding.bottomNav.getOrCreateBadge(R.id.publicHuddlesStandaloneFragment).apply {
                    isVisible = it > 0
                    adjustForBottomNavDot(this@MainActivity)
                }
            }
        }

        viewModel.unreadPrivate.observe(this) {
            if (binding.bottomNav.menu.findItem(R.id.homePrivateFragment) != null) {
                binding.bottomNav.getOrCreateBadge(R.id.homePrivateFragment).apply {
                    isVisible = it > 0
                    adjustForBottomNavDot(this@MainActivity)
                }
            }
        }

        viewModel.businessOperation.observe(this) {
            binding.bottomNav.getOrCreateBadge(R.id.homeBusinessFragment).apply {
                if (it == null) {
                    isVisible = false
                    return@observe
                }
                isVisible = true
                val color = DataFormatHelper.businessColorToColorRes(it.badgeColor, this@MainActivity)
                adjustForBottomNavDot(color)
            }
        }

        viewModel.onLoggedOut.observe(this) {
            viewModelStore.clear()
            Log.d("N2LG", "observe: onLoggedOut")
            navController.navigateSafe(NavGraphHomeDirections.actionGlobalNavGraphLogin())
            recreate()
        }

        viewModel.onSwitchTheme.observe(this) {
            Log.d("THEME", "onSwitchTheme: $it")
            recreate()
        }

        viewModel.checkBiometric.observe(this) {
            if (it) {
                navController.navigateSafe(NavGraphHomeDirections.actionGlobalBiometricAuthFragment())
            }
        }

        viewModel.appSettings.observe(this) {
            setUpBiometrics()
        }

        challengeViewModel.onContributorRequestResponded.observe(this) {
            if (it.second) Toast.makeText(this, getString(R.string.podium_challenge_contributor_coins_debited, it.first.roundToInt().toString()), Toast.LENGTH_SHORT).show()
            else Toast.makeText(this, R.string.common_rejected, Toast.LENGTH_SHORT).show()
        }
        challengeViewModel.onContributorRequestRespondError.observe(this) {
            Toast.makeText(this,it, Toast.LENGTH_SHORT).show()
        }
        challengeViewModel.onMaidanAnotherUserJoinedError.observe(this) {
            showMaidanAnotherUserJoinAlert(message = it)
        }

        challengeViewModel.onAcceptedMaidanInvite.observe(this) {
            if ((it.challenge?.competitorFee ?: 0.0) != 0.0) {
                Toast.makeText(this, getString(R.string.podium_maidan_fee_debit_toast, it.challenge?.competitorFee?.roundToInt().toString().orEmpty()), Toast.LENGTH_LONG).show()
            }
            navController.navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(it.id, kind = PodiumKind.MAIDAN.ordinal))
        }

        challengeViewModel.onInsufficientBalance.observe(this) {
            showInsufficientBalanceAlert(R.string.podium_challenge_insufficient_balance_text)
        }

        viewModel.onMaidanLive.observe(this) { id ->
            val builder = MaterialAlertDialogBuilder(this).apply {
                setMessage(getString(R.string.podium_maidan_live_prompt))
                setPositiveButton(resources.getString(R.string.common_proceed)) { dialog, which ->
                    dialog.dismiss()
                    navController.navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(podiumId = id, kind = PodiumKind.MAIDAN.ordinal))
                }
                setNegativeButton(resources.getString(R.string.podium_maidan_exit_prompt_ok)) { dialog, which ->
                    dialog.dismiss()
                    challengeViewModel.exitMaidan(id)
                }
            }
            builder.show()
        }

        viewModel.loggingEnabled.observe(this) {
            Log.w("LCR", "observe: $it")

        }
    }

    /**
     * Implementation of press back again to exit
     */
    @SuppressLint("RestrictedApi")
    private fun setupExitWatcher() {
        Log.w("NAVC", "onBackPressedDispatcher.addCallback: MainActivity")
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                Log.d("NAVC", "handleOnBackPressed: activity - backQ size is ${navController.currentBackStack.value.size}")
                if (navController.currentBackStack.value.size > 2) {
                    navController.popBackStack()
                    doubleBackToExitPressedOnce = false
                    // fragment nav worked. Don't do anything
                } else if (doubleBackToExitPressedOnce) {
                    finishAffinity()
                } else {
                    doubleBackToExitPressedOnce = true
                    Toast.makeText(applicationContext, resources.getString(R.string.main_exit_warning), Toast.LENGTH_SHORT).show()
                    Handler(Looper.myLooper()!!).postDelayed(
                        { doubleBackToExitPressedOnce = false }, 2000
                    )
                }
            }
        })
    }

    private fun setBottomNav(isPremium: Boolean) {
        binding.bottomNav.apply {
            menu.clear()
            itemIconTintList = null
            inflateMenu(if (isPremium) R.menu.menu_bottom_nav_premium else R.menu.menu_bottom_nav_free)
            menu.forEach {
                TooltipCompat.setTooltipText(findViewById(it.itemId), null)
            }
        }
    }

    @SuppressLint("RestrictedApi")
    private fun setupNavigation() {
        val isPremium = viewModel.isPremiumUser.value?:false
        Log.w("NAVC", "setupNavigation: premium: $isPremium", )
        val homeSet = setOf(
            R.id.homePrivateFragment,
            R.id.homePublicFragment,
            R.id.homeBusinessFragment,
            R.id.publicPodiumStandaloneFragment,
            R.id.publicHuddlesStandaloneFragment,
            R.id.publicFlashStandaloneFragment,
            R.id.publicPostatStandaloneFragment,
            R.id.authoritiesPremiumFragment,
            R.id.socialPolicyDocumentFragment,
            R.id.socialAffairSocialSupportFragment
        )

        setBottomNav(isPremium)
        appBarConfig = AppBarConfiguration(homeSet)

        binding.bottomNav.apply {
            setupWithNavController(navController)
            setOnItemReselectedListener { }
            setOnItemSelectedListener { item ->
                when (item.itemId) {
                    // FREE
                    R.id.publicPodiumStandaloneFragment -> navController.navigateSafe(NavGraphHomeDirections.actionGlobalPublicPodiumStandaloneFragment())
                    R.id.publicFlashStandaloneFragment -> {
                        PublicFlashBaseFragment.triggerPlayer(supportFragmentManager)
                        navController.navigateSafe(NavGraphHomeDirections.actionGlobalPublicFlashStandaloneFragment())
                    }
                    R.id.publicHuddlesStandaloneFragment -> {
                        navController.navigateSafe(NavGraphHomeDirections.actionGlobalPublicHuddlesStandaloneFragment())
                    }
                    R.id.publicPostatStandaloneFragment -> navController.navigateSafe(NavGraphHomeDirections.actionGlobalPublicPostatStandaloneFragment())
                    // Premium
                    R.id.homePrivateFragment -> navController.navigateSafe(NavGraphHomeDirections.actionGlobalHomePrivateFragment())
                    R.id.homePublicFragment -> navController.navigateSafe(NavGraphHomeDirections.actionGlobalHomePublicFragment())
                    R.id.homeBusinessFragment -> navController.navigateSafe(NavGraphHomeDirections.actionGlobalHomeBusinessFragment(destination = -1))
                    R.id.authoritiesPremiumFragment -> navController.navigateSafe(NavGraphHomeDirections.actionPremiumAuthoritiesFragment())
                }
                true
            }
        }
        navController.addOnDestinationChangedListener { controller, destination, _ ->
            Log.d("NAVC", "onDestinationChanged: backstack: ${controller.currentBackStack.value.joinToString(" -> ") { it.destination.displayName.split("/").last() }}")
            Log.w("NAVC", "onDestinationChanged: ${destination.displayName}")
            Firebase.crashlytics.log("onDestinationChanged: ${destination.displayName}")
            if (homeSet.contains(destination.id)) {
                binding.bottomNav.visibility = View.VISIBLE
            } else {
                binding.bottomNav.visibility = View.GONE
            }
        }
    }


    private fun setUpBiometrics() {
        if (viewModel.appSettings.value?.isBioMetricEnabled == true) {
            viewModel.checkBiometricTime()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        val navControllerHandledBack = navController.navigateUp(appBarConfig)
        Log.d("NAVC", "onSupportNavigateUp: navControllerHandledBack - $navControllerHandledBack")
        return navControllerHandledBack || super.onSupportNavigateUp()
    }

    private val actionBarListener = NavController.OnDestinationChangedListener { controller, destination, _ ->
        Log.d("NAVC", "setupHomeIcon: addOnDestinationChangedListener")
        val navIcon = destination.setSocialAffairNavIcon()
        setupHomeIcon(customNavIcon = navIcon)
    }

    @DrawableRes
    private fun NavDestination.setSocialAffairNavIcon(): Int? {
        val socialFragmentList = listOf(
            R.id.socialAffairHomeFragment,
            R.id.socialAffairActiveCasesFragment,
            R.id.socialAffairCaseInfoFragment,
            R.id.socialAffairMinisterFragment,
            R.id.socialAffairCommitteeFragment,
            R.id.socialAffairHonoursFragment,
            R.id.socialAffairSocialSupportFragment,
            R.id.socialAffairDraftsFragment,
            R.id.socialDonateBottomSheetFragment,
            R.id.upgradeSupportFragment,
            R.id.socialAffairAskedQuestionsFragment,
            R.id.socialAffairVotersFragment,
            R.id.socialAffairRequestPersonalSupport
        )
        return if (this.id in socialFragmentList) R.drawable.ic_social_back_button else null
    }

    /**
     * call from fragment to setup the fragment provided toolbar with the nav graph
     * @param toolBar the toolbar defined in the fragment
     */
    @SuppressLint("ResourceType")
    fun setupActionBar(toolBar: Toolbar, config: AppBarConfiguration? = null, customBackButton: Boolean = true, @DrawableRes customNavIcon: Int? = null) {
        setSupportActionBar(toolBar)
        setupActionBarWithNavController(navController, config ?: appBarConfig)
        navController.removeOnDestinationChangedListener(actionBarListener)
        navController.addOnDestinationChangedListener(actionBarListener)
        setupHomeIcon(customBackButton, customNavIcon)
    }

    private var customBackButtonEnabled: Boolean = true

    private fun setupHomeIcon(customBackButton: Boolean? = null, @DrawableRes customNavIcon: Int? = null) {
        customBackButtonEnabled = customBackButton ?: customBackButtonEnabled
        supportActionBar?.apply {
            if (navController.currentDestination?.let { appBarConfig.isTopLevelDestination(it) || it.id == R.id.settingsBottomSheetFragment } == true) {
                setDisplayHomeAsUpEnabled(true)
                val a = theme.obtainStyledAttributes(intArrayOf(R.attr.toolbarDrawerIcon))
                val attributeResourceId = a.getResourceId(0, 0)
                setHomeAsUpIndicator(attributeResourceId)
            }
            Log.d("NAVC", "setupHomeIcon: customBackButtonEnabled $customBackButtonEnabled")
            if (customBackButtonEnabled) {
                val a = theme.obtainStyledAttributes(intArrayOf(R.attr.toolbarNavIcon))
                val attributeResourceId = a.getResourceId(0, 0)
                setHomeAsUpIndicator(customNavIcon ?: attributeResourceId)
            }
        }

    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (navController.currentDestination?.let { appBarConfig.isTopLevelDestination(it) } == true && item.itemId == android.R.id.home) {
            navController.navigateSafe(NavGraphHomeDirections.actionGlobalSettingsBottomSheetFragment())
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    fun setHomeIcon(@DrawableRes icon: Int) {
        supportActionBar?.apply {
            setHomeAsUpIndicator(icon)
        }
    }

    fun showHomeButton(show: Boolean) {
        supportActionBar?.apply {
            this.setDisplayHomeAsUpEnabled(show)
        }
    }

    /**
     * Required for the transparent status bar to work. Please be careful when changing this
     */

    // Declare the launcher at the top of your Activity/Fragment:
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            Log.d("MAINACT", "notification permission: granted")
            // FCM SDK (and your app) can post notifications.
        } else {
            Log.d("MAINACT", "notification permission: not granted")
            // TODO: Inform user that that your app will not show notifications.
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun showPermissionAlert() {
        val title = R.string.chat_attach_camera_denied_heading
        val message = R.string.chat_attach_camera_denied_text
        MaterialAlertDialogBuilder(this).setTitle(getText(title)).setMessage(getText(message)).setPositiveButton(R.string.common_ok) { dialog, _ ->
            requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            dialog.dismiss()
        }.setNegativeButton(R.string.common_cancel) { dialog, _ ->
            dialog.dismiss()
        }.show()
    }

    private fun checkNotificationPermission() {
        // This is only necessary for API level >= 33 (TIRAMISU)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
                Log.d("MAINACT", "notification permission: granted")
                // FCM SDK (and your app) can post notifications.
            } else if (shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                // Need to reenable after figuring out the best approach
//                showPermissionAlert()
            } else {
                // Directly ask for the permission
                requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        }
    }

    private fun handleDynamicLinks() {
        Log.d("DEEPLINK", "handleDynamicLinks: start $this")
        Firebase.dynamicLinks.getDynamicLink(intent).addOnSuccessListener(this) { pendingDynamicLinkData: PendingDynamicLinkData? ->
            // Get deep link from result (may be null if no link is found)
            var deepLink: Uri? = null
            if (pendingDynamicLinkData != null) {
                Log.d("DEEPLINK", "pendingDynamicLinkData: ${pendingDynamicLinkData.link}")
                deepLink = pendingDynamicLinkData.link
            }

            deepLink ?: return@addOnSuccessListener

            Log.d("DEEPLINK", "handleDynamicLinks: got dynamic link: ${deepLink.host}")
            if(viewModel.user==null && deepLink.host!=BuildConfig.APP_INVITE_LINK) return@addOnSuccessListener
            when(deepLink.host) {
                BuildConfig.APP_INVITE_LINK -> {
                    val inviteCode = deepLink.getQueryParameter("code")
                    Log.d("DEEPLINK", "handleDynamicLinks: got app invite: $inviteCode")
                    if (inviteCode != null) {
                        viewModel.registerAppInviteCode(inviteCode)
                    }
                }
                BuildConfig.HUDDLE_INVITE_LINK -> {
                    val huddleId = deepLink.getQueryParameter("huddle_id")?.toInt()
                    val private = deepLink.getQueryParameter("private")?.toBoolean() ?: false
                    val messageId = deepLink.getQueryParameter("message_id").toString()
                    Log.d("DEEPLINK", "handleDynamicLinks: got huddle invite: $huddleId")

                    if (messageId.isEmpty()) {
                        huddleId?.let {
                            val action = if (private) NavGraphHomeDirections.actionGlobalNavigationChatGroup(huddleId)
                            else NavGraphHomeDirections.actionGlobalNavChatHuddle(huddleId)
                            navController.navigateSafe(action)
                        }
                    } else {
                        huddleId?.let {
                            val action = NavGraphHomeDirections.actionGlobalNavChatHuddle(huddleId, messageId)
                            navController.navigateSafe(action)
                        }
                    }
                }

                BuildConfig.PODIUM_INVITE_LINK -> {
                    val podiumId = deepLink.getQueryParameter("podium_id")
                    val podiumRequiredRating = deepLink.getQueryParameter("required_user_rating")?.toDoubleOrNull() ?: 0.0
                    val userRating = viewModel.user?.userRatingPercent?.toDoubleOrNull() ?: 0.0

                    Log.d("DEEPLINK", "handleDynamicLinks: got flash link: $podiumId")
                    Log.d("DEEPLINK", "handleDynamicLinks: podium rating: $podiumRequiredRating")
                    Log.d("DEEPLINK", "handleDynamicLinks: user rating: $userRating")

                    if (podiumRequiredRating == 100.0 && userRating != 100.0) {
                        showPodiumRatingJoiningError(
                            message = getString(R.string.podium_join_with_rating_with_hundred),
                            userRating = "$userRating",
                            isPremium = viewModel.user?.premium
                        )
                        return@addOnSuccessListener
                    }

                    if (podiumRequiredRating > userRating) {
                        showPodiumRatingJoiningError(
                            message = getString(R.string.podium_join_with_rating_ninety_or_above),
                            userRating = "$userRating",
                            isPremium = viewModel.user?.premium
                        )
                        return@addOnSuccessListener
                    }

                    podiumId?.let {
                        navController.navigateSafe(NavGraphHomeDirections.actionGlobalNavLivePodium(podiumId))
                    }

                }
                BuildConfig.FLASH_VIDEO_LINK -> {
                    val flashId = deepLink.getQueryParameter("flash_id")
                    Log.d("DEEPLINK", "handleDynamicLinks: got flash link: $flashId")
                    flashId?.let {
                        navController.navigateSafe(NavGraphHomeDirections.actionGlobalFlashSinglePlayerFragment(it))
                    }
                }
            }

        }.addOnFailureListener(this) { e -> Log.w("DEEPLINK", "getDynamicLink:onFailure", e) }
    }

    private fun showPodiumRatingJoiningError(message: String, userRating: String?, isPremium: Boolean?) {
        showFlashatDialog {
            setTitle(getString(R.string.title_podium_alert_rating, userRating))
            setMessage(message)
            setIcon(R.drawable.ic_rating_less)
            setConfirmButtonVisible(isPremium == true)
            setConfirmButton(R.string.restore_rating_header, R.drawable.ic_restore_rating, false) {
                navController.navigateSafe(HomeBusinessFragmentDirections.actionHomeBusinessFragmentToRestoreRatingFragment(false))
                true
            }
        }
    }

    private var updateFlowResultLauncher: ActivityResultLauncher<IntentSenderRequest>? = null

    private fun registerUpdateResult() {
        Firebase.crashlytics.log("Registering updateFlowResultLauncher")
        updateFlowResultLauncher = registerForActivityResult(ActivityResultContracts.StartIntentSenderForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                // Handle successful app update
            }
        }
    }

    private fun checkForUpdates() {
        if (BuildConfig.BUILD_TYPE == Constants.BuildType.RELEASE.value) {
            try {
                Firebase.crashlytics.log("Starting checkForUpdates")
                val appUpdateManager = AppUpdateManagerFactory.create(this)

                // Returns an intent object that you use to check for an update.
                val appUpdateInfoTask = appUpdateManager.appUpdateInfo

                // Checks that the platform will allow the specified type of update.
                appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
                    updateFlowResultLauncher?.let {
                        Firebase.crashlytics.log("Contract is not null")
                        if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE && appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE)) {
                            // Request the update.
                            appUpdateManager.startUpdateFlowForResult(
                                // Pass the intent that is returned by 'getAppUpdateInfo()'.
                                appUpdateInfo,
                                // an activity result launcher registered via registerForActivityResult
                                it,
                                // Or pass 'AppUpdateType.FLEXIBLE' to newBuilder() for
                                // flexible updates.
                                AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build()
                            )
                        } else if (appUpdateInfo.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS) {
                            // If an in-app update is already running, resume the update.
                            appUpdateManager.startUpdateFlowForResult(
                                appUpdateInfo, it, AppUpdateOptions.newBuilder(AppUpdateType.IMMEDIATE).build()
                            )
                        }
                    }
                }
            } catch (e: Exception) {

            }
        }
    }

    fun showGiftVideo(gift: SentGiftPayload) {
        giftViewModel.showGiftVideo(gift)
    }

    fun showChallengeResultVideo(winner: Boolean) {
        giftViewModel.showChallengeResultVideo(winner)
    }

    fun showBirthdayVideo(birthdayUrl: String){
        giftViewModel.showBirthdayResultVideo(birthdayUrl)
    }


    private fun showChallengeContributorRequestAlert(challenge: PodiumChallenge) {
        if (challenge.contributorRequestedTimeRemaining.seconds <= 0L) return
        var timerJob: Job? = null
        val contributor = challenge.contributors.first()
        val podiumName = challenge.podiumName
        val managerName = challenge.facilitator?.name
        val isMaidan = challenge.challengeType == ChallengeType.MAIDAN
        val message = if(isMaidan) {
            if ((challenge.competitorFee ?: 0.0) == 0.0) getString(R.string.podium_free_maidan_invitation_message, managerName)
            else getString(R.string.podium_maidan_invite_message, managerName, challenge.competitorFee?.roundToInt().toString())
        } else getString(R.string.podium_challenge_external_contributor_alert_text, contributor.coinsSpent.toString(), challenge.challengeType.name, podiumName)
        val alert = MaterialAlertDialogBuilder(this)
            .setMessage(message)
            .setCancelable(false)
            .setPositiveButton(getString(if (isMaidan) R.string.podium_maidan_challenge else R.string.common_accept)) { dialog, _ ->
                challengeViewModel.respondToContributorRequest(challenge,true)
                dialog.dismiss()
                timerJob?.cancel()
            }
            .setNegativeButton(getString(if (isMaidan) R.string.common_decline else R.string.common_cancel)) { dialog, _ ->
                challengeViewModel.respondToContributorRequest(challenge,false)
                dialog.dismiss()
                timerJob?.cancel()
            }.show()

        timerJob = DateTimeUtils.countDownTimerFlow(challenge.contributorRequestedTimeRemaining.seconds*1000).onCompletion {
            if (alert.isShowing) {
                alert.dismiss()
            }
        }.launchIn(lifecycleScope)
    }

    fun setPresidentNotification(response: UserBirthdayResponse) {
       viewModel.setPresidentNotification(response)
    }


    private fun showMaidanAnotherUserJoinAlert(message: String?) {
        showFlashatDialog {
            setTitle(getString(R.string.podium_maidan_bad_luck_title))
            setMessage(message ?: "")
            setIcon(R.drawable.ic_sad)
            setConfirmButtonVisible(true)
            setConfirmButton(R.string.podium_maidan_bad_luck_find_another_maidan, R.drawable.ic_podium_maidan_fist_filled, tint = false, iconPadding = false) {
                navController.navigateSafe(NavGraphHomeDirections.actionGlobalPublicMaidanFragment())
                true
            }
        }
    }
}