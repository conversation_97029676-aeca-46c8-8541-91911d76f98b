package com.app.messej.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.media.RingtoneManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.app.messej.R
import com.app.messej.data.Constants.EXTERNAL_CONTRIBUTOR_NOTIFICATION_ID
import com.app.messej.data.model.api.auth.UnreadItemsResponse
import com.app.messej.data.model.api.profile.UserBirthdayResponse
import com.app.messej.data.model.enums.BroadcastTab
import com.app.messej.data.model.enums.PushNotificationType
import com.app.messej.data.model.notification.BaseNotificationPayload
import com.app.messej.data.model.notification.BirthdayNotification
import com.app.messej.data.model.notification.BroadcastNotification
import com.app.messej.data.model.notification.DefaultNotification
import com.app.messej.data.model.notification.ExternalContributorNotification
import com.app.messej.data.model.notification.FlashCommentNotification
import com.app.messej.data.model.notification.GiftNotificationPayLoad
import com.app.messej.data.model.notification.HuddleInviteNotification
import com.app.messej.data.model.notification.HuddleMessageCommentNotification
import com.app.messej.data.model.notification.HuddleMessageNotification
import com.app.messej.data.model.notification.HuddleRemoveNotification
import com.app.messej.data.model.notification.JoinRequestNotification
import com.app.messej.data.model.notification.NewFollowerNotification
import com.app.messej.data.model.notification.PodiumNotification
import com.app.messej.data.model.notification.PollInvitationNotification
import com.app.messej.data.model.notification.PollManagerNotification
import com.app.messej.data.model.notification.PostAtNotification
import com.app.messej.data.model.notification.PrivateMessageNotification
import com.app.messej.data.model.notification.ProfileNotification
import com.app.messej.data.model.notification.SendFlaxNotification
import com.app.messej.data.repository.AccountRepository
import com.app.messej.data.repository.AuthenticationRepository
import com.app.messej.data.repository.HuddlesRepository
import com.app.messej.data.repository.datastore.FlashatDatastore
import com.app.messej.data.socket.ChatSocketRepository
import com.app.messej.data.socket.repository.PrivateChatEventRepository
import com.app.messej.data.utils.JsonUtil.fromJson
import com.app.messej.ui.chat.ActiveChatTracker
import com.app.messej.ui.home.HomeFragment
import com.app.messej.ui.home.publictab.podiums.live.ActivePodiumTracker
import com.app.messej.ui.utils.DeepLinkUtil
import com.app.messej.ui.utils.TextFormatUtils.applyMarkdownFormatting
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import com.google.gson.JsonParser
import jp.wasabeef.transformers.glide.CropCircleTransformation
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.takeWhile
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking


class FlashatFirebaseMessagingService: FirebaseMessagingService() {

    private val job = SupervisorJob()

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "onNewToken: $token")
        CoroutineScope(job).launch {
            AuthenticationRepository(application).registerFCMToken(token)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        job.cancel()
    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Check if message contains a data payload.
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            remoteMessage.data["app_data"]?.let {
                if (!AccountRepository(application).loggedIn) return
                processPushPayload(it)
            }
        }

        // Check if message contains a notification payload.
        remoteMessage.notification?.let {
            if (!AccountRepository(application).loggedIn) return
            Log.d(TAG, "Message Notification Body: ${it.body}")
        }

        // Also if you intend on generating your own notifications as a result of a received FCM
        // message, here is where that should be initiated. See sendNotification method below.
    }

    val gson = Gson()

    private fun processPushPayload(json: String) {
        val base = gson.fromJson<BaseNotificationPayload>(json)
        val type = base.type?.let {
            base.type
        } ?: run {
            PushNotificationType.OTHERS
        }
        val channelId = createChannel(type)
        when (type) {
            PushNotificationType.NEW_PRIVATE_MESSAGE -> {
                val data = gson.fromJson<PrivateMessageNotification>(json)
                Log.d(TAG, "processPushPayload: NEW_PRIVATE_MESSAGE: received")
                if (data.senderId == AccountRepository(application).user.id) return
                if (ActiveChatTracker.isActive(ActiveChatTracker.ActiveChatScreen.PrivateChat(data.roomId))) return
                val deepLink = DeepLinkUtil.toPrivateChat(application, data.roomId, data.senderId)
                showNotification(
                    title = data.name, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail, media = data.mediaMeta?.thumbnail
                )
                try {
                    CoroutineScope(job).launch {
//                        val db = FlashatDatabase.getInstance(MainApplication.applicationContext())
//                        db.getPrivateChatDao().insert(
//                            PrivateChat(
//                                id = data.roomId,
//                                sender = data.senderId,
//                                receiver = data.userId,
//                                receiverDetails = data.de,
//                                roomId = msg.roomId,
//                                unreadCount = 0,
//                                followedByMe = followedByEach ?: false,
//                                _lastMessage = msg,
//                                type = if(isRequest) PrivateChat.ChatType.REQUEST else PrivateChat.ChatType.PRIVATE,
//                                privateMessageTab = if (followedByEach == true) PrivateChat.PrivateMessageTabType.BUDDIES else PrivateChat.PrivateMessageTabType.INTRUDER
//                        )
//                        )
                        Log.d(TAG, "processPushPayload: NEW_PRIVATE_MESSAGE: listening for connection")
                        ChatSocketRepository.connectionStateFlow.takeWhile {
                            Log.d(TAG, "processPushPayload: NEW_PRIVATE_MESSAGE: connected: $it")
                            it
                        }
                        PrivateChatEventRepository.setDeliveredStatus(data.roomId,data.messageId)
                        Log.d(TAG, "processPushPayload: NEW_PRIVATE_MESSAGE: set delivered")
                    }
                    Log.d(TAG, "processPushPayload: NEW_PRIVATE_MESSAGE: start socket")
                    ChatSocketRepository.start()
                } catch (_: Exception) { }
            }

            PushNotificationType.NEW_HUDDLE_MESSAGE -> {
                val data = gson.fromJson<HuddleMessageNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val huddle = HuddlesRepository(application).getPublicHuddle(data.huddleId)
                if (ActiveChatTracker.isActive(ActiveChatTracker.ActiveChatScreen.GroupChat(data.huddleId))) return
                val deepLink = if (data.private) {
                    DeepLinkUtil.toPrivateGroup(application, data.huddleId)
                } else {
                    DeepLinkUtil.toPublicHuddle(application, data.huddleId)
                }
                if (huddle != null) {
                    if (!huddle.muted)
                    // TODO not good practice. Sending unnecessary notifications can trigger rate limits and permission removals
                        showNotification(
                            title = data.name, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail, media = data.mediaMeta?.thumbnail
                        )
                }
            }

            PushNotificationType.NEW_HUDDLE_COMMENT -> {
                val data = gson.fromJson<HuddleMessageCommentNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                if (ActiveChatTracker.isActive(ActiveChatTracker.ActiveChatScreen.HuddlePostComment(data.huddleId, data.messageId))) return
                val deepLink = DeepLinkUtil.toPublicHuddleComment(application, data.huddleId, data.messageId)
                showNotification(
                    title = data.name, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.PARTICIPANT_INVITED -> {
                val data = gson.fromJson<HuddleInviteNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (data.private) {
                    DeepLinkUtil.toPrivateGroup(application, data.huddleId)
                } else {
                    DeepLinkUtil.toPublicHuddle(application, data.huddleId)
                }
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.PARTICIPANT_REMOVED -> {
                val data = gson.fromJson<HuddleRemoveNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (data.private) {
                    DeepLinkUtil.toPrivateGroupList(application)
                } else {
                    DeepLinkUtil.toPublicHuddleList(application)
                }
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.NEW_BROADCAST -> {
                val data = gson.fromJson<BroadcastNotification>(json)
                if (data.broadcasterId == AccountRepository(application).user.id) return
                if (ActiveChatTracker.isActive(ActiveChatTracker.ActiveChatScreen.BroadCastChat(data.broadcasterId))) return
                val deepLink = DeepLinkUtil.toStarBroadcast(application, data.broadcasterId)
                showNotification(
                    title = data.name,
                    message = data.message,
                    id = data.notificationId,
                    deepLink = deepLink,
                    channel = channelId,
                    largeIcon = data.mediaMeta?.thumbnail,
                    media = data.mediaMeta?.thumbnail
                )
            }

            PushNotificationType.NEW_FOLLOWER, PushNotificationType.NEW_FAN, PushNotificationType.NEW_DEAR -> {
                val data = gson.fromJson<NewFollowerNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val tab = when (type) {
                    PushNotificationType.NEW_FAN -> BroadcastTab.TAB_FANS
                    PushNotificationType.NEW_DEAR -> BroadcastTab.TAB_DEARS
                    else -> BroadcastTab.TAB_LIKERS
                }
                val deepLink = DeepLinkUtil.toBroadcastList(application, tab)
                showNotification(
                    title = data.message!!, id = data.notificationId!!, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.JOIN_REQUEST_ACCEPTED, PushNotificationType.JOIN_REQUEST_RECEIVED, PushNotificationType.ADMIN_INVITE_RECEIVED -> {
                val data = gson.fromJson<JoinRequestNotification>(json)
                if (ActiveChatTracker.isActive(ActiveChatTracker.ActiveChatScreen.GroupChat(data.huddleId!!)) && type == PushNotificationType.ADMIN_INVITE_RECEIVED) return
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (data.private == null || data.private == false) {
                    DeepLinkUtil.toPublicHuddle(application, data.huddleId!!)
                } else {
                    DeepLinkUtil.toPrivateGroup(application, data.huddleId!!)
                }
                showNotification(
                    title = data.message!!, message = data.message!!, id = data.notificationId!!, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.HUDDLE_DELETED -> {
                val data = gson.fromJson<JoinRequestNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (data.private == null || data.private == false) {
                    DeepLinkUtil.toPublicHuddle(application, data.huddleId!!)
                } else {
                    DeepLinkUtil.toPrivateGroup(application, data.huddleId!!)
                }
                showNotification(
                    title = data.message!!, message = data.message!!, id = data.notificationId!!, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.UNREAD_COUNT_UPDATE -> {
                val data = JsonParser.parseString(json).asJsonObject
                if (data.has("counts")) {
                    val obj = data.getAsJsonObject("counts")
                    val counts = gson.fromJson<UnreadItemsResponse>(obj.toString())
                    runBlocking {
                        FlashatDatastore().saveUnreadCounts(counts)
                    }
                }
            }

            PushNotificationType.POLL_INVITE -> {
                val data = gson.fromJson<PollInvitationNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (data.private != true) {
                    DeepLinkUtil.toPublicHuddle(application, data.huddleId)
                } else {
                    DeepLinkUtil.toPrivateGroup(application, data.huddleId)
                }
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )

            }

            PushNotificationType.DEALS -> {
                val data = gson.fromJson<SendFlaxNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = DeepLinkUtil.toBusinessDeals(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.FLASH_COMMENT_RECEIVED -> {
                val data = gson.fromJson<FlashCommentNotification>(json)
                val deepLink = DeepLinkUtil.toMyFlashComments(application, data.flashId, data.receiverId)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId
                )
            }
            PushNotificationType.FLASH_COMMENT_REPLY_RECEIVED -> {
                val data = gson.fromJson<FlashCommentNotification>(json)
                val deepLink = DeepLinkUtil.toMyFlashComments(application, data.flashId, data.receiverId)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId
                )
            }

            PushNotificationType.FLASH_COMMENT_REPORTED -> {
                val data = gson.fromJson<FlashCommentNotification>(json)
                val deepLink = DeepLinkUtil.toMyFlashComments(application, data.flashId, data.receiverId)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId
                )
            }

            PushNotificationType.POLL_NOTIFY_MANAGER -> {
                val data = gson.fromJson<PollManagerNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (data.private != true) {
                    DeepLinkUtil.toPublicHuddle(application, data.huddleId)
                } else {
                    DeepLinkUtil.toPrivateGroup(application, data.huddleId)
                }
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.PODIUM, PushNotificationType.PODIUM_INVITE -> {
                val data = gson.fromJson<PodiumNotification>(json)
                val deepLink = DeepLinkUtil.toPodiumLive(application, data.podiumId)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId
                )
            }

            PushNotificationType.GIFT_NOTIFY_MANAGER -> {
                val data = gson.fromJson<GiftNotificationPayLoad>(json)
                val deepLink = DeepLinkUtil.toGift(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.FORCED_USERNAME_CHANGE, PushNotificationType.FORCED_GENDER_CHANGE, PushNotificationType.FORCED_DOB_CHANGE, PushNotificationType.USER_PROFILE_EDITED -> {
                val data = gson.fromJson<ProfileNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = DeepLinkUtil.toProfile(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.NEW_USERNAME_SUGGESTION -> {
                val data = gson.fromJson<ProfileNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = DeepLinkUtil.toNotifications(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.CHALLENGE_CONTRIBUTION_INVITE -> {
                val data = gson.fromJson<ExternalContributorNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                if (ActivePodiumTracker.isActive(ActivePodiumTracker.ActivePodiumScreen.PodiumRoom(data.podiumId))) return
                val deepLink = DeepLinkUtil.toHome(application, event = HomeFragment.EVENT_CONTRIBUTOR_REQUEST, data = data.podiumId)
                showNotification(
                    title = data.message, message = data.message, id = EXTERNAL_CONTRIBUTOR_NOTIFICATION_ID, deepLink = deepLink, channel = channelId
                )
                val notificationManagerHelper = NotificationManagerHelper(this)
                notificationManagerHelper.removeNotificationAfterDelay(notificationId = EXTERNAL_CONTRIBUTOR_NOTIFICATION_ID)

            }

            PushNotificationType.MAIDAN_SUPPORTER_INVITE -> {
                val data = gson.fromJson<ExternalContributorNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                if (ActivePodiumTracker.isActive(ActivePodiumTracker.ActivePodiumScreen.PodiumRoom(data.podiumId))) return
                val deepLink = DeepLinkUtil.toPodiumLive(application, data.podiumId)
                showNotification(
                    title = data.message, message = data.message, id = EXTERNAL_CONTRIBUTOR_NOTIFICATION_ID, deepLink = deepLink, channel = channelId
                )
            }

            PushNotificationType.OTHERS -> {
                val data = gson.fromJson<DefaultNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = DeepLinkUtil.toNotifications(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }
            PushNotificationType.BIRTHDAY_NOTIFICATION -> {
                val data = gson.fromJson<BirthdayNotification>(json)
                if (data.senderId == AccountRepository(application).user.id) return
                val deepLink = if (!data.otherData.animationUrlAndroid.isNullOrBlank()) DeepLinkUtil.toHome(application, event = HomeFragment.EVENT_BIRTHDAY_VIDEO, data = data.otherData.animationUrlAndroid)
                else DeepLinkUtil.toHome(application, event = HomeFragment.EVENT_BIRTHDAY, data = data.otherData.userId.toString())
                showNotification(
                    title = data.message, message = data.message, id = BIRTHDAY_NOTIFICATION_ID, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }

            PushNotificationType.POSTAT_COMMENT -> {
                val data = gson.fromJson<PostAtNotification>(json)
                if (data.sender_id == AccountRepository(application).user.id) return
                val deepLink = DeepLinkUtil.toPublicPostat(application)

                showNotification(
                    title = data.message, message = data.message, id = data.notification_id, deepLink = deepLink, channel = channelId, largeIcon = null)
            }
            PushNotificationType.POSTAT_MENTION -> {
                val data = gson.fromJson<PostAtNotification>(json)
                if (data.sender_id == AccountRepository(application).user.id) return
                val deepLink = DeepLinkUtil.toPublicPostat(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notification_id, deepLink = deepLink, channel = channelId, largeIcon = null
                )
            }
            PushNotificationType.PRESIDENT -> {
                val data = gson.fromJson<UserBirthdayResponse>(json)
                val deepLink = DeepLinkUtil.toHome(application, event = HomeFragment.EVENT_PRESIDENT_CROWNED, data = data.currentPresidentId.toString())
                showNotification(
                    title = "", message = null, id = data.userId?:0, deepLink = deepLink, channel = channelId, largeIcon =null
                )

            }

            PushNotificationType.REPORTED_PODIUM_WINNER -> {
                val data = gson.fromJson<GiftNotificationPayLoad>(json)
                val deepLink = DeepLinkUtil.toGift(application)
                showNotification(
                    title = data.message, message = data.message, id = data.notificationId, deepLink = deepLink, channel = channelId, largeIcon = data.thumbnail
                )
            }
        }
    }

    companion object {
        private const val TAG = "FIREBASE_SERVICE"

        private const val CHANNEL_DEFAULT = "default"
        private const val BIRTHDAY_NOTIFICATION_ID = 1223

        fun createChannel(manager: NotificationManager, id: String, name: String, importance: Int = NotificationManager.IMPORTANCE_HIGH): NotificationChannel {
            val mChannel = NotificationChannel(id, name, importance)
            // Register the channel with the system. You can't change the importance
            // or other notification behaviors after this.
//            Log.d("CMW", "creating channel: $mChannel")
            manager.createNotificationChannel(mChannel)
            return mChannel
        }
    }

    private fun createChannel(type: PushNotificationType): String {
        var channelId = CHANNEL_DEFAULT
        val name = when (type) {
            PushNotificationType.NEW_PRIVATE_MESSAGE -> {
                channelId = "private_message"
                "Private Messages"
            }
            PushNotificationType.NEW_HUDDLE_MESSAGE, PushNotificationType.NEW_HUDDLE_COMMENT, PushNotificationType.DEALS,
            PushNotificationType.PARTICIPANT_INVITED, PushNotificationType.PARTICIPANT_REMOVED,
            PushNotificationType.JOIN_REQUEST_ACCEPTED, PushNotificationType.JOIN_REQUEST_RECEIVED,
            PushNotificationType.HUDDLE_DELETED,
            PushNotificationType.ADMIN_INVITE_RECEIVED, PushNotificationType.POLL_INVITE, PushNotificationType.POLL_NOTIFY_MANAGER, PushNotificationType.GIFT_NOTIFY_MANAGER,
                -> {
                channelId = "huddle_message"
                "Huddle/Group Messages"
            }
            PushNotificationType.NEW_BROADCAST -> {
                channelId = "broadcast_message"
                "Broadcast Messages"
            }
            PushNotificationType.NEW_FOLLOWER, PushNotificationType.NEW_DEAR, PushNotificationType.NEW_FAN->{
                channelId="users"
                "Users"
            }
            PushNotificationType.UNREAD_COUNT_UPDATE -> null
            PushNotificationType.FLASH_COMMENT_RECEIVED, PushNotificationType.FLASH_COMMENT_REPORTED,PushNotificationType.FLASH_COMMENT_REPLY_RECEIVED -> {
                channelId="flash"
                "Flash Videos"
            }
            PushNotificationType.PODIUM, PushNotificationType.PODIUM_INVITE, PushNotificationType.CHALLENGE_CONTRIBUTION_INVITE, PushNotificationType.MAIDAN_SUPPORTER_INVITE -> {
                channelId="podium"
                "Podium"
            }
            PushNotificationType.FORCED_USERNAME_CHANGE, PushNotificationType.FORCED_GENDER_CHANGE, PushNotificationType.FORCED_DOB_CHANGE, PushNotificationType.USER_PROFILE_EDITED -> {
                channelId="profile"
                "Profile"
            }
            PushNotificationType.NEW_USERNAME_SUGGESTION -> {
                channelId="username_suggestion"
                "Username Suggestion"
            }
            PushNotificationType.OTHERS -> {
                channelId="others"
                "Others"
            }
            PushNotificationType.BIRTHDAY_NOTIFICATION -> {
                channelId="birthday"
                "Birthday"
            }

            PushNotificationType.POSTAT_COMMENT, PushNotificationType.POSTAT_MENTION -> {
                channelId="postat"
                "PostAt"
            }
            PushNotificationType.PRESIDENT -> {
                channelId="president"
                "President"
            }
            PushNotificationType.REPORTED_PODIUM_WINNER -> {
                channelId="gift_file"
                "Gift File"
            }

        }
        name?: return ""
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        return Companion.createChannel(notificationManager,channelId,name).id
    }

    private fun showNotification(title: String, message: String? = null, id: Int = 0, deepLink: PendingIntent? = null, channel: String, largeIcon: String? = null, media: String? = null) {
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        val notificationBuilder =
            NotificationCompat.Builder(this, channel).setContentTitle(title).setAutoCancel(true).setSmallIcon(R.drawable.ic_notification_small).setSound(defaultSoundUri)
                .setContentIntent(deepLink)
        message?.let { if (message!=title) notificationBuilder.setContentText(message.applyMarkdownFormatting(this)) }

        media?.let {
            try {
                val futureTarget = Glide.with(this).asBitmap().load(it).submit()

                val bitmap = futureTarget.get()
                notificationBuilder.setStyle(
                    NotificationCompat.BigPictureStyle().bigPicture(bitmap)
                )
            } catch (e: Exception) {
                Log.e(TAG, "showNotification: Error loading media", e)
            }
        }

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        largeIcon?.let {
            try {
                val futureTarget = Glide.with(this).asBitmap().load(it).apply(RequestOptions.bitmapTransform(CropCircleTransformation())).submit()

                val bitmap = futureTarget.get()
                notificationBuilder.setLargeIcon(bitmap)
            } catch (e: Exception) {
                Log.e(TAG, "showNotification: Error loading thumbnail", e)
            }
        }

        notificationManager.notify(id, notificationBuilder.build())
    }
}