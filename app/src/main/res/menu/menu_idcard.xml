<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <group android:id="@+id/id_card_send_gift_group">
        <item android:id="@+id/id_card_send_gift" android:title="@string/title_send_gift" />
    </group>

    <group android:id="@+id/id_card_send_flax_group">
        <item android:id="@+id/id_card_send_flax" android:title="@string/title_send_flax" />
    </group>

    <group android:id="@+id/id_card_follow_group">
        <item android:id="@+id/id_card_follow" android:title="@string/user_action_follow" />
    </group>

    <group android:id="@+id/id_card_block_group">
        <item android:id="@+id/id_card_block" android:title="@string/user_action_block" />
    </group>

    <group android:id="@+id/id_card_restrict_group">
        <item android:id="@+id/id_card_restrict" android:title="@string/private_message_restrict" />
    </group>


    <group android:id="@+id/id_card_manage_notifications_group">
        <item android:id="@+id/id_card_manage_notifications" android:title="@string/user_action_manage_notifications" />
    </group>


    <group android:id="@+id/id_card_send_private_message_group">
        <item android:id="@+id/id_card_send_private_message" android:title="@string/huddle_send_private_message" />
    </group>

    <group android:id="@+id/id_card_remove_broadcast_group">
        <item android:id="@+id/id_card_remove_broadcast" android:title="@string/user_action_remove_from_broadcast" />
    </group>

    <group android:id="@+id/id_card_edit_nick_name_group">
        <item android:id="@+id/id_card_edit_nick_name" android:title="@string/title_dialogue_edit_nickname" />
    </group>
    <group android:id="@+id/id_card_block_from_app_group">
        <item android:id="@+id/id_card_block_from_app" android:title="@string/user_action_block_from_app" />
    </group>

    <group android:id="@+id/id_card_report_group">
        <item android:id="@+id/id_card_report" android:title="@string/report_user"
            android:icon="@drawable/ic_report_outline_cut"
            app:iconTint="@color/colorError" />
    </group>
    <group android:id="@+id/id_card_ban_group">
        <item android:id="@+id/id_card_ban" android:title="@string/report_user_ban"
            android:icon="@drawable/ic_ban_user_round"
            app:iconTint="@color/colorError"/>
    </group>


</menu>