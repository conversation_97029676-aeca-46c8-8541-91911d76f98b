<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph_home"
    app:startDestination="@id/SplashFragment">
    <fragment android:id="@+id/authOTPFragment"
        android:name="com.app.messej.ui.auth.AuthOTPFragment"
        android:label="@string/register_page_title_otp_verify"
        tools:layout="@layout/fragment_register_otp">
        <argument android:name="requestMode" app:argType="com.app.messej.data.model.enums.OTPRequestMode" />
        <argument android:name="countryCode" app:argType="string" app:nullable="true" />
        <argument android:name="phoneNumber" app:argType="string" app:nullable="true" />
        <argument android:name="email" app:argType="string" app:nullable="true" />
    </fragment>
    <action android:id="@+id/action_global_authOTPFragment" app:destination="@id/authOTPFragment" />
    <fragment android:id="@+id/SplashFragment" android:name="com.app.messej.ui.splash.SplashFragment" android:label="fragment_splash" tools:layout="@layout/fragment_splash">
        <action android:id="@+id/action_SplashFragment_to_onboardingFragment" app:destination="@id/onboardingFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="false" />
    </fragment>
    <fragment android:id="@+id/onboardingFragment" android:name="com.app.messej.ui.auth.onboarding.OnboardingFragment" android:label="fragment_onboarding" tools:layout="@layout/fragment_onboarding" />
    <navigation android:id="@+id/nav_graph_login" app:startDestination="@id/LoginFragment">
        <fragment android:id="@+id/LoginFragment" android:name="com.app.messej.ui.auth.login.LoginFragment" android:label="@string/login_page_title" tools:layout="@layout/fragment_login" />
    </navigation>
    <navigation android:id="@+id/nav_forgot_password" app:startDestination="@id/forgotPasswordFragment">
        <fragment android:id="@+id/forgotPasswordFragment"
            android:name="com.app.messej.ui.auth.forgotPassword.ForgotPasswordFragment"
            android:label="@string/forgot_password_label"
            tools:layout="@layout/fragment_forgot_password">
            <argument android:name="recoveryType" app:argType="com.app.messej.data.model.enums.OTPRequestMode" />
            <action android:id="@+id/action_forgotPasswordFragment_to_createPasswordFragment" app:destination="@id/createPasswordFragment" />
        </fragment>

        <fragment android:id="@+id/createPasswordFragment"
            android:name="com.app.messej.ui.auth.profile.RegisterPasswordFragment"
            android:label="@string/register_create_password_nav_label"
            tools:layout="@layout/fragment_register_create_password">
            <argument android:name="countryCode" app:argType="string" />
            <argument android:name="phoneNumber" app:argType="string" />
            <argument android:name="email" app:argType="string" />
            <argument android:name="otpRequestMode" app:argType="com.app.messej.data.model.enums.OTPRequestMode" />
            <action android:id="@+id/action_createPasswordFragment_to_createProfileFragment"
                app:destination="@id/createProfileFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
        </fragment>
    </navigation>

    <navigation android:id="@+id/nav_graph_register" app:startDestination="@id/registrationEmailFragment">
        <fragment android:id="@+id/registrationFragment"
            android:name="com.app.messej.ui.auth.register.RegisterMobileNumberFragment"
            android:label="Registration"
            tools:layout="@layout/fragment_register_mobile_number">
            <action android:id="@+id/action_registrationFragment_to_registerMobileConfirmBottomSheetFragment" app:destination="@id/registerMobileConfirmBottomSheetFragment" />
            <action android:id="@+id/action_registrationFragment_to_createPasswordFragment"
                app:destination="@id/createPasswordFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_createProfileFragment"
                app:destination="@id/createProfileFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_createUsernameFragment"
                app:destination="@id/createUsernameFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_registerSuperstarFragment"
                app:destination="@id/navigation_register_superstar"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_registerLocationFragment"
                app:destination="@id/registerLocationFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
        </fragment>
        <fragment android:id="@+id/registrationEmailFragment"
            android:name="com.app.messej.ui.auth.register.RegisterEmailFragment"
            android:label="Registration"
            tools:layout="@layout/fragment_register_email">
            <action android:id="@+id/action_registrationFragment_to_registerMobileConfirmBottomSheetFragment" app:destination="@id/registerMobileConfirmBottomSheetFragment" />
            <action android:id="@+id/action_registrationFragment_to_createPasswordFragment"
                app:destination="@id/createPasswordFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_createProfileFragment"
                app:destination="@id/createProfileFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_createUsernameFragment"
                app:destination="@id/createUsernameFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_registerSuperstarFragment"
                app:destination="@id/navigation_register_superstar"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_registrationFragment_to_registerLocationFragment"
                app:destination="@id/registerLocationFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
        </fragment>

        <dialog android:id="@+id/registerMobileConfirmBottomSheetFragment"
            android:name="com.app.messej.ui.auth.register.RegisterMobileConfirmFragment"
            android:label="RegisterMobileConfirmBottomSheetFragment"
            tools:layout="@layout/fragment_register_mobile_confirm_bottom_sheet">
            <argument android:name="countryCode" app:argType="string" />
            <argument android:name="phoneNumber" app:argType="string" />
            <argument android:name="email" app:argType="string" />
        </dialog>
        <fragment android:id="@+id/createPasswordFragment"
            android:name="com.app.messej.ui.auth.profile.RegisterPasswordFragment"
            android:label="@string/register_create_password_nav_label"
            tools:layout="@layout/fragment_register_create_password">
            <argument android:name="countryCode" app:argType="string" />
            <argument android:name="phoneNumber" app:argType="string" />
            <argument android:name="email" app:argType="string" />
            <argument android:name="otpRequestMode" app:argType="com.app.messej.data.model.enums.OTPRequestMode" />
            <action android:id="@+id/action_createPasswordFragment_to_createProfileFragment"
                app:destination="@id/createProfileFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />

        </fragment>
        <fragment android:id="@+id/createProfileFragment"
            android:name="com.app.messej.ui.auth.profile.RegisterProfileFragment"
            android:label="@string/register_create_profile_nav_label"
            tools:layout="@layout/fragment_register_create_profile">
            <action android:id="@+id/action_createProfileFragment_to_createUsernameFragment" app:destination="@id/createUsernameFragment" />
        </fragment>
        <fragment android:id="@+id/createUsernameFragment"
            android:name="com.app.messej.ui.auth.profile.RegisterUsernameFragment"
            android:label="@string/register_create_username_nav_label"
            tools:layout="@layout/fragment_register_create_username">
            <action android:id="@+id/action_createUsernameFragment_to_registerUsernameConfirmFragment" app:destination="@id/registerUsernameConfirmFragment" />
            <action android:id="@+id/action_createUsernameFragment_to_registerLocationFragment"
                app:destination="@id/registerLocationFragment"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <action android:id="@+id/action_createUsernameFragment__to_navigation_register_superstar"
                app:destination="@id/navigation_register_superstar"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
        </fragment>
        <dialog android:id="@+id/registerUsernameConfirmFragment"
            android:name="com.app.messej.ui.auth.profile.RegisterUsernameConfirmFragment"
            android:label="fragment_register_username_confirm"
            tools:layout="@layout/fragment_register_username_confirm">
            <argument android:name="username" app:argType="string" />
        </dialog>

        <fragment android:id="@+id/registerLocationFragment"
            android:name="com.app.messej.ui.auth.profile.RegisterLocationFragment"
            android:label="fragment_register_location"
            tools:layout="@layout/fragment_register_location">
            <action android:id="@+id/action_registerLocationFragment_to_navigation_register_superstar"
                app:destination="@id/navigation_register_superstar"
                app:popUpTo="@id/nav_graph_register"
                app:popUpToInclusive="true" />
            <argument android:name="standalone" app:argType="boolean" />
        </fragment>
        <navigation android:id="@+id/navigation_register_superstar" app:startDestination="@id/registerSuperstarFragment">
            <fragment android:id="@+id/registerSuperstarFragment"
                android:name="com.app.messej.ui.auth.profile.RegisterSuperstarFragment"
                android:label="@string/register_choose_superstar_nav_label"
                tools:layout="@layout/fragment_register_superstar_select">
                <action android:id="@+id/action_registerSuperstarFragment_to_registerSuperstarFilterFragment" app:destination="@id/registerSuperstarFilterFragment" />
                <action android:id="@+id/action_registerSuperstarFragment_to_registerSuperstarConfirmFragment" app:destination="@id/registerSuperstarConfirmFragment" />
                <action android:id="@+id/action_registerSuperstarFragment_to_registerUpgradeSuperstarFragment" app:destination="@id/registerUpgradeSuperstarFragment" />
            </fragment>
            <fragment android:id="@+id/registerSuperstarFilterFragment"
                android:name="com.app.messej.ui.auth.profile.RegisterSuperstarFilterFragment"
                android:label="@string/register_upgrade_superstar_filter"
                tools:layout="@layout/fragment_register_superstar_filter" />
            <fragment android:id="@+id/registerUpgradeSuperstarFragment"
                android:name="com.app.messej.ui.auth.profile.RegisterUpgradeSuperstarFragment"
                android:label="fragment_register_upgrade_superstar"
                tools:layout="@layout/fragment_register_upgrade_superstar">
                <argument android:name="name" app:argType="string" />
            </fragment>
            <dialog android:id="@+id/registerSuperstarConfirmFragment"
                android:name="com.app.messej.ui.auth.profile.RegisterSuperstarConfirmFragment"
                android:label="RegisterSuperstarConfirmFragment"
                tools:layout="@layout/fragment_register_superstar_confirm">
                <argument android:name="name" app:argType="string" />
                <argument android:name="image" app:argType="string" app:nullable="true" />
                <argument android:name="premium" app:argType="boolean" />
            </dialog>
        </navigation>
    </navigation>
    <fragment android:id="@+id/locationSearchFragment"
        android:name="com.app.messej.ui.auth.common.LocationSearchFragment"
        android:label="fragment_register_search_location"
        tools:layout="@layout/fragment_register_search_location"/>
    <action android:id="@+id/action_global_registerLocationFragment" app:destination="@id/registerLocationFragment"/>
    <action android:id="@+id/action_global_SplashFragment" app:destination="@id/SplashFragment" />
    <action android:id="@+id/action_global_buyflaxFragment" app:destination="@id/buyCoinsFragment"/>
    <action android:id="@+id/action_global_nav_graph_login" app:destination="@id/nav_graph_login" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_giftFragment" app:destination="@id/giftListFragment" />
    <action android:id="@+id/action_global_flax_send_flax" app:destination="@id/sendFlaxFragment" />
    <action android:id="@+id/action_global_giftLowBalance" app:destination="@id/giftLowBalance" />
    <action android:id="@+id/action_global_PublicBroadcastStandaloneFragment" app:destination="@id/publicBroadcastStandaloneFragment" />
    <action android:id="@+id/action_global_publicStarsStandaloneFragment" app:destination="@id/publicStarsStandaloneFragment" />
    <action android:id="@+id/action_global_nav_graph_register" app:destination="@id/nav_graph_register" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_forgot_password" app:destination="@id/nav_forgot_password" app:popUpToInclusive="true" />
    <fragment android:id="@+id/homePrivateFragment" android:name="com.app.messej.ui.home.privatetab.HomePrivateFragment" tools:layout="@layout/fragment_home_private">
        <argument android:name="tab" app:argType="integer" android:defaultValue="-1" />
        <action android:id="@+id/action_homePrivateFragment_to_privateMessagePendingListFragment" app:destination="@id/privateMessagePendingListFragment" />
        <action android:id="@+id/action_homePrivateFragment_to_homePrivateGroupSearchFragment" app:destination="@id/homePrivateGroupSearchFragment" />
        <action android:id="@+id/action_homePrivateFragment_to_homePrivateMessagesSearchFragment" app:destination="@id/homePrivateMessagesSearchFragment" />
        <action android:id="@+id/action_homePrivateFragment_to_privateMessagesPrivacyFragment" app:destination="@id/privateMessagesPrivacyFragment" />
    </fragment>

    <dialog
        android:id="@+id/giftListFragment"
        tools:layout="@layout/fragment_gift_bottom_sheet"
        android:name="com.app.messej.ui.home.gift.GiftListBottomSheetFragment">
        <argument android:name="receiverId" app:argType="integer"  />
<!--        <argument android:name="podiumId" app:argType="string" app:nullable="true" android:defaultValue="" />-->
        <argument android:name="giftContextId" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="giftContext" app:argType="com.app.messej.data.model.enums.GiftContext" />
        <argument android:name="challengeId" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="challengeEndTimeStampUTC" app:argType="long" android:defaultValue="0L" />
        <argument android:name="managerId" app:argType="integer" android:defaultValue="-1"/>
        <argument android:name="birthday" app:argType="boolean" android:defaultValue="false"/>
        <argument android:name="userLevelCongrats" app:argType="boolean" android:defaultValue="false"/>
        <action android:id="@+id/action_global_giftLowBalance" app:destination="@id/giftLowBalance" />
    </dialog>


    <dialog android:id="@+id/giftLowBalance"
        android:name="com.app.messej.ui.home.gift.GiftLowFragment"
        android:label="fragment_gift_low_balance"
        tools:layout="@layout/fragment_gift_low_balance_premium" >
        <argument android:name="currencyType" app:argType="com.app.messej.data.model.enums.CurrencyType" />
    </dialog>
    <fragment android:id="@+id/homePrivateGroupSearchFragment" android:name="com.app.messej.ui.home.privatetab.PrivateGroupSearchFragment" tools:layout="@layout/fragment_private_group_search">
    </fragment>
    <fragment android:id="@+id/homePrivateMessagesSearchFragment"
        android:name="com.app.messej.ui.home.privatetab.messages.PrivateMessageSearchFragment"
        tools:layout="@layout/fragment_private_message_search" />

    <fragment
        android:id="@+id/blackListFragment"
        android:name="com.app.messej.ui.enforcements.BlackListedFragment">
        <argument android:name="payFineType" app:argType="com.app.messej.data.model.enums.PayFineType" android:defaultValue="LEGAL_PAY_FINE" />
    </fragment>

    <action android:id="@+id/action_global_black_list_fragment" app:destination="@id/blackListFragment" />

    <fragment android:id="@+id/homePublicFragment" android:name="com.app.messej.ui.home.publictab.HomePublicFragment" tools:layout="@layout/fragment_home_public">
        <argument android:name="tab" app:argType="integer" android:defaultValue="-1" />
        <action android:id="@+id/action_homePublicFragment_to_publicHuddleSearchFragment" app:destination="@id/publicHuddleSearchFragment" />
        <argument android:name="subTab" app:argType="integer" android:defaultValue="-1" />
        <action android:id="@+id/action_homePublicFragment_to_publicHuddleSuggestionSearchFragment" app:destination="@id/publicHuddleSuggestionSearchFragment" />
        <action android:id="@+id/action_homePublicFragment_to_myFlashFragment" app:destination="@id/nav_my_flash" />
        <action android:id="@+id/action_homePublicFragment_to_flashPlayerFragment" app:destination="@id/flashPlayerFragment" />
        <action android:id="@+id/action_homePublicFragment_to_flashSearchFragment" app:destination="@id/nav_flash_search" />
        <action android:id="@+id/action_homePublicFragment_to_podiumSearchFragment" app:destination="@id/podiumSearchFragment" />
        <action android:id="@+id/action_homePublicFragment_to_sellHuddleListFragment" app:destination="@id/sellHuddleListFragment"/>
        <action android:id="@+id/action_homePublicFragment_to_myPostatFeedFragment" app:destination="@id/myPostatFeedFragment" />
        <action android:id="@+id/action_homePublicFragment_to_postatIgnoredUsersFragment" app:destination="@id/postatIgnoredUsersFragment" />
        <action android:id="@+id/action_homePublicFragment_to_userPostatFeedFragment" app:destination="@id/userPostatFeedFragment" />

    </fragment>
    <fragment android:id="@+id/publicHuddleSearchFragment" android:name="com.app.messej.ui.home.publictab.huddles.PublicHuddlesSearchFragment" tools:layout="@layout/fragment_public_huddles_search">
        <argument android:name="tab" app:argType="com.app.messej.data.model.enums.HuddleTab" />
    </fragment>
    <fragment android:id="@+id/publicHuddleSuggestionSearchFragment" android:label="" android:name="com.app.messej.ui.home.publictab.huddles.PublicHuddleSuggestionSearchFragment" tools:layout="@layout/fragment_public_huddle_suggestion">
    </fragment>
    <fragment android:id="@+id/homePublicBroadcastSearchFragment" android:name="com.app.messej.ui.home.publictab.broadcast.PublicBroadcastSearchFragment" tools:layout="@layout/fragment_public_broadcast_search" android:label="">
        <argument android:name="tab" app:argType="com.app.messej.data.model.enums.BroadcastTab" />
    </fragment>
    <fragment android:id="@+id/publicStarsSearchFragment" android:name="com.app.messej.ui.home.publictab.stars.search.PublicStarsSearchFragment" tools:layout="@layout/fragment_public_stars_search"/>
    <fragment android:id="@+id/homeBusinessFragment" android:name="com.app.messej.ui.home.businesstab.HomeBusinessFragment" tools:layout="@layout/fragment_home_business" >
        <argument android:name="destination" app:argType="integer" />
        <argument android:name="tab" app:argType="integer" android:defaultValue="-1" />
        <action android:id="@+id/action_homeBusinessFragment_to_businessOperationsDearsList" app:destination="@id/businessOperationsDearsList" />
    </fragment>

    <fragment
        android:id="@+id/payFineFragment"
        android:name="com.app.messej.ui.home.publictab.authorities.legalAffairs.payFine.PayFineFragment"
        android:label="">
        <argument android:name="reportId" app:argType="integer" android:defaultValue="0" />
        <argument android:name="fineCategory" app:argType="string" app:nullable="true" />
    </fragment>
    <action android:id="@+id/action_global_payFineFragment" app:destination="@id/payFineFragment" />

    <action android:id="@+id/action_premium_authoritiesFragment" app:destination="@id/authoritiesPremiumFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_settingsBottomSheetFragment_to_AuthoritiesStandAloneFragment" app:destination="@id/authoritiesStandAloneFragment" />

    <action android:id="@+id/action_authoritiesFragment_to_stateAffairsFragment" app:destination="@id/nav_graph_state_affairs" />

    <navigation android:id="@+id/nav_graph_state_affairs" app:startDestination="@+id/stateAffairsFragment">
        <fragment
            android:id="@+id/stateAffairsFragment"
            android:name="com.app.messej.ui.home.publictab.authorities.stateAffairs.StateAffairsMainFragment"
            android:label="@string/state_affair_title">
            <action android:id="@+id/action_stateAffairsFragment_to_stateAffairListViewAllFragment" app:destination="@id/stateAffairListViewAllFragment" />
        </fragment>

        <fragment android:id="@+id/stateAffairListViewAllFragment"
            android:name="com.app.messej.ui.home.publictab.authorities.stateAffairs.StateAffairListViewAllFragment"
            android:label="StateAffairListViewAllFragment" >
            <argument android:name="stateAffairtype" app:argType="com.app.messej.data.model.enums.StateAffairsTypes" />
        </fragment>

    </navigation>


    <action android:id="@+id/action_global_to_taskThreeDialog" app:destination="@id/navigation_task_one" />
    <action android:id="@+id/action_global_to_publicBroadcastStandaloneFragment" app:destination="@id/publicBroadcastStandaloneFragment" />
    <action android:id="@+id/action_global_to_taskInformativeMessageDialogFragment" app:destination="@id/taskInformativeMessageDialogFragment" />
    <action android:id="@+id/action_global_to_rateApplicationFragment" app:destination="@id/navigation_rate" />
    <action android:id="@+id/action_global_to_payoutHistory" app:destination="@id/payoutFragment" />


    <fragment android:id="@+id/payoutFragment"
        android:name="com.app.messej.ui.home.businesstab.operations.tasks.BusinessPayoutHistory"
        android:label="@string/title_sell_flax_history"
        tools:layout="@layout/fragment_payout_history"/>

    <dialog android:id="@+id/taskInformativeMessageDialogFragment"
        android:name="com.app.messej.ui.home.businesstab.operations.tasks.information.TaskInformativeMessageDialogFragment"
        android:label="fragment_task_informative_message_dialog"
        tools:layout="@layout/fragment_task_informative_message_dialog" >
        <argument android:name="activityName" app:argType="com.app.messej.data.model.enums.BusinessTaskActivity" />
    </dialog>


    <navigation android:id="@+id/navigation_rate" app:startDestination="@id/fragmentRateApp">
        <argument android:name="isUpdateMode" app:argType="boolean" />
        <dialog android:id="@+id/fragmentRateApp"
            android:name="com.app.messej.ui.home.businesstab.operations.tasks.BusinessRateAppFragment"
            android:label=""
            tools:layout="@layout/fragment_business_rate_app">
            <action android:id="@+id/action_rateBusinessFragment_to_rateImageUploadFragment" app:destination="@id/fragmentImageUpload" />
        </dialog>


        <dialog android:id="@+id/fragmentImageUpload"
            android:name="com.app.messej.ui.home.businesstab.operations.tasks.BusinessRateUploadFragment"
            android:label=""
            tools:layout="@layout/fragment_business_rate_upload" />
    </navigation>

    <navigation android:id="@+id/navigation_task_one" app:startDestination="@id/taskThreeDialog">

        <dialog android:id="@+id/taskThreeDialog"
            android:name="com.app.messej.ui.home.businesstab.operations.tasks.BusinessOperationTaskOneFragment"
            android:label=""
            tools:layout="@layout/fragment_bussiness_operation_task_one">
            <action android:id="@+id/action_taskThreeDialog_to_fragmentBusinessOtp" app:destination="@id/fragmentBusinessOtp" />
        </dialog>

        <dialog android:id="@+id/fragmentBusinessOtp"
            android:name="com.app.messej.ui.home.businesstab.operations.tasks.BusinessOtpFragment"
            android:label=""
            tools:layout="@layout/fragment_business_otp">
            <argument android:name="otpRequestMode" app:argType="com.app.messej.data.model.enums.OTPRequestMode" />
        </dialog>


    </navigation>

    <fragment android:id="@+id/privateMessagePendingListFragment"
        android:name="com.app.messej.ui.home.privatetab.messages.PrivateMessagePendingListFragment"
        android:label="@string/private_messages_action_restricted"
        tools:layout="@layout/fragment_private_messages_pending" />
    <fragment android:id="@+id/publicStarsAddFragment"
        android:name="com.app.messej.ui.home.publictab.stars.add.PublicStarsAddFragment"
        android:label="@string/add_stars_page_title"
        tools:layout="@layout/fragment_public_stars_add" />
    <fragment android:id="@+id/profileFragment" android:name="com.app.messej.ui.profile.ProfileFragment" android:label="" tools:layout="@layout/fragment_profile">
        <action android:id="@+id/action_profileFragment_to_changeUserNameFragment" app:destination="@id/changeUserNameFragment" />
        <action android:id="@+id/action_profileFragment_to_editPersonalDetailsFragment" app:destination="@id/editPersonalDetailsFragment" />
    </fragment>
    <fragment android:id="@+id/changeUserNameFragment"
        android:name="com.app.messej.ui.profile.ChangeUserNameFragment"
        android:label="" tools:layout="@layout/fragment_change_user_name">
    </fragment>


    <fragment android:id="@+id/createHuddleFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.create.CreateHuddleFragment"
        tools:layout="@layout/fragment_create_huddle">
        <argument android:name="huddleType" app:argType="com.app.messej.data.model.enums.HuddleType" />
        <action android:id="@+id/action_createHuddleFragment_to_addParticipantsFragment"
            app:destination="@id/addParticipantsFragment"
            app:popUpTo="@id/createHuddleFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <navigation android:id="@+id/navigation_forward" app:startDestination="@id/forwardMessageHome">
        <argument android:name="srcType" app:argType="com.app.messej.data.model.enums.ForwardSource" android:defaultValue="EXTERNAL" />
        <argument android:name="messageId" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="mediaType" android:defaultValue="null" app:argType="string" app:nullable="true" />
        <argument android:name="mediaUri" android:defaultValue="null" app:argType="string" app:nullable="true" />
        <argument android:name="textMessage" android:defaultValue="" app:argType="string" app:nullable="true" />

        <fragment android:id="@+id/forwardMessageHome"
            android:name="com.app.messej.ui.home.forward.ForwardHomeFragment"
            android:label="@string/title_forwarded_to"
            tools:layout="@layout/fragment_home_forward">
            <argument android:name="srcType" app:argType="com.app.messej.data.model.enums.ForwardSource" android:defaultValue="EXTERNAL" />
            <argument android:name="messageId" app:argType="string" app:nullable="true" android:defaultValue="" />
            <argument android:name="mediaType" android:defaultValue="null" app:argType="string" app:nullable="true" />
            <argument android:name="mediaUri" android:defaultValue="null" app:argType="string" app:nullable="true" />
            <argument android:name="textMessage" android:defaultValue="" app:argType="string" app:nullable="true" />
            <action android:id="@+id/action_forwardMessageHome_to_forwardSearchFragment" app:destination="@id/forwardSearchFragment" />
        </fragment>

        <fragment android:id="@+id/forwardSearchFragment"
            android:name="com.app.messej.ui.home.forward.search.ForwardSearchFragment"
            tools:layout="@layout/fragment_forward_search">
            <argument android:name="messageId" app:argType="string" app:nullable="true" android:defaultValue="" />
            <argument android:name="forwardedMessage" app:argType="string" app:nullable="true" android:defaultValue="" />
            <argument android:name="tab" app:argType="integer"/>
            <argument android:name="mediaType" android:defaultValue="null" app:argType="string" app:nullable="true" />
            <argument android:name="fileUri" android:defaultValue="null" app:argType="string" app:nullable="true" />
            <argument android:name="textMessage" android:defaultValue="" app:argType="string" app:nullable="true" />
        </fragment>

    </navigation>
    <fragment android:id="@+id/addParticipantsFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.create.AddHuddleParticipantsFragment"
        android:label="@string/add_participants_page_title"
        tools:layout="@layout/fragment_huddle_add_participants">
        <argument android:name="huddleId" app:argType="integer" />
        <argument android:name="showFinish" app:argType="boolean" />
    </fragment>

    <fragment android:id="@+id/postImageEdit"
        android:name="com.app.messej.ui.chat.imageedit.PostImageEditFragment"
        android:label="@string/add_participants_page_title"
        tools:layout="@layout/fragment_post_image_edit">
        <argument android:name="source" app:argType="android.net.Uri" app:nullable="true"/>
        <argument android:name="destination" app:argType="android.net.Uri" app:nullable="true"/>
    </fragment>

    <dialog android:id="@+id/huddleDeleteLeaveConfirmFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleDeleteLeaveConfirmFragment"
        android:label=""
        tools:layout="@layout/fragment_huddle_delete_leave_confirm">
        <argument android:name="isMangerOrAdmin" app:argType="boolean" />
        <argument android:name="huddleType" app:argType="com.app.messej.data.model.enums.HuddleType" />
    </dialog>

    <fragment
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.SellHuddleFragment"
        android:id="@+id/sellHuddleFragment"
        android:label="@string/title_sell_huddle"
        tools:layout="@layout/fragment_sell_huddle">
        <argument android:name="huddleId" app:argType="integer" />
        <argument android:name="isForSale" app:argType="boolean" />
        <argument android:name="huddlePrice" app:argType="string" android:defaultValue="" />
        <action android:id="@+id/action_SellHuddleFragment_to_offerYourHuddleFragment" app:destination="@id/offerYourHuddleFragment"/>
    </fragment>

    <fragment
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.OfferYourHuddleFragment"
        android:id="@+id/offerYourHuddleFragment"
        android:label="@string/title_offer_your_huddle_for_sale"
        tools:layout="@layout/fragment_offer_your_huddle">
    </fragment>

    <navigation android:id="@+id/nav_chat_group" app:startDestination="@id/privateGroupChatFragment">
        <argument android:name="huddleId" app:argType="integer" />
        <!--        <argument android:name="sharedMessage" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
        <!--        <argument android:name="messageType" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
        <fragment android:id="@+id/privateGroupChatFragment" android:name="com.app.messej.ui.chat.GroupChatFragment" android:label="" tools:layout="@layout/fragment_chat_screen">
            <argument android:name="huddleId" app:argType="integer" />
            <!--            <argument android:name="sharedMessage" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
            <!--            <argument android:name="messageType" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
            <action android:id="@+id/action_publicGroupChatFragment_to_publicGroupChatAttachImageFragment" app:destination="@id/publicGroupChatAttachImageFragment" />
            <action android:id="@+id/action_privateGroupChatFragment_to_publicGroupChatAttachVideoFragment" app:destination="@id/publicGroupChatAttachVideoFragment" />
            <action android:id="@+id/action_privateGroupChatFragment_to_groupChatAttachLocationFragment" app:destination="@id/groupChatAttachLocationFragment" />
            <action android:id="@+id/action_privateGroupChatFragment_to_publicGroupChatAttachDocumentFragment" app:destination="@id/publicGroupChatAttachDocumentFragment" />
        </fragment>

        <fragment android:id="@+id/publicGroupChatAttachImageFragment"
            android:name="com.app.messej.ui.chat.GroupChatImageAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_public_huddles_chat_attach_image" >
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>

        <fragment android:id="@+id/groupChatAttachLocationFragment"
            android:name="com.app.messej.ui.chat.GroupChatLocationAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_attach_location" >
        </fragment>


        <fragment android:id="@+id/publicGroupChatAttachVideoFragment"
            android:name="com.app.messej.ui.chat.GroupChatVideoAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_public_huddles_chat_attach_video" >
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>

        <fragment android:id="@+id/publicGroupChatAttachDocumentFragment"
            android:name="com.app.messej.ui.chat.GroupChatDocumentAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_base_document_attach">
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>

        <action android:id="@+id/action_global_addParticipantsFragment" app:destination="@id/addParticipantsFragment" />
        <action android:id="@+id/action_global_nav_huddle_info" app:destination="@id/nav_huddle_info" />
        <action android:id="@+id/action_global_manageRequestInviteFragment" app:destination="@id/manageRequestInviteFragment" />
    </navigation>
    <action android:id="@+id/action_global_navigation_chat_group" app:destination="@id/nav_chat_group" />
    <action android:id="@+id/action_global_addParticipantsFragment" app:destination="@id/addParticipantsFragment" />

    <navigation android:id="@+id/nav_chat_huddle" app:startDestination="@id/publicHuddleChatFragment">
        <argument android:name="huddleId" app:argType="integer"/>
        <argument android:name="messageId" app:argType="string" app:nullable="true" android:defaultValue=" " />
        <!--        <argument android:name="sharedMessage" app:argType="string" app:nullable="true" android:defaultValue="" />-->
        <!--        <argument android:name="messageType" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
        <fragment android:id="@+id/publicHuddleChatFragment" android:name="com.app.messej.ui.home.publictab.huddles.chat.HuddleChatFragment" android:label="" tools:layout="@layout/fragment_huddle_chat">
            <argument android:name="huddleId" app:argType="integer" />
            <argument android:name="messageId" app:argType="string"  app:nullable="true" android:defaultValue=" " />
            <!--            <argument android:name="sharedMessage" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
            <!--            <argument android:name="messageType" app:argType="string" app:nullable="true" android:defaultValue=""/>-->
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddlePostFragment" app:destination="@id/huddlePostFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddlePostCommentsFragment" app:destination="@id/huddlePostCommentsFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddlePrivacy" app:destination="@id/huddlePrivacy" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_create_poll" app:destination="@id/huddleCreatePollFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_schedulePollFragment" app:destination="@id/schedulePollFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddlePollFragment" app:destination="@id/huddlePollFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_sendPollInvitationFragment" app:destination="@id/sendPollInvitationFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_pollResultFragment" app:destination="@id/pollResultFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_removeDearsFragment" app:destination="@id/removeDearsFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddleMessagesSearchFragment" app:destination="@id/huddleMessagesSearchFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddleGiftListFragment" app:destination="@id/huddleGiftListFragment" />
            <action android:id="@+id/action_publicHuddleChatFragment_to_huddleGiftFragment" app:destination="@id/giftFragment"/>

        </fragment>
        <fragment android:id="@+id/huddleMessagesSearchFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.chat.HuddleMessagesSearchFragment"
            android:label="fragment_huddle_messages_search"
            tools:layout="@layout/fragment_huddle_messages_search" >
            <argument android:name="huddleId" app:argType="integer" />
            <action android:id="@+id/action_huddleMessagesSearchFragment_to_huddlePostCommentsFragment" app:destination="@id/huddlePostCommentsFragment" />
        </fragment>
        <fragment android:id="@+id/sendPollInvitationFragment" android:name="com.app.messej.ui.home.publictab.huddles.poll.SendPollInvitationFragment" android:label="@string/title_poll_invite"
            tools:layout="@layout/fragment_send_poll_invitation">
            <argument android:name="pollId" app:argType="integer"  />
            <argument android:name="isTribe" app:argType="boolean" />
        </fragment>

        <fragment android:id="@+id/removeDearsFragment" android:name="com.app.messej.ui.home.publictab.huddles.chat.RemovedDearsFragment" android:label="@string/tribe_title_removed_dears"
            tools:layout="@layout/fragment_removed_dears">
            <argument android:name="huddleId" app:argType="integer"/>
        </fragment>

        <fragment android:id="@+id/schedulePollFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.poll.ScheduledPollsFragment"
            android:label="@string/title_schedule_poll"
            tools:layout="@layout/fragment_schedule_poll" >
            <argument android:name="huddleId" app:argType="integer" />
            <argument android:name="isUserManager" app:argType="boolean"/>
            <action android:id="@+id/action_schedule_poll_to_create_poll" app:destination="@id/huddleCreatePollFragment" />
            <action android:id="@+id/action_schedulePollFragment_to_allSchedulePollFragment" app:destination="@id/allSchedulePollFragment" />
            <argument android:name="pollType" app:argType="com.app.messej.data.model.enums.PollType" />
            <action android:id="@+id/action_schedulePollFragment_to_pollResultFragment" app:destination="@id/pollResultFragment" />
        </fragment>
        <fragment android:id="@+id/allSchedulePollFragment" android:name="com.app.messej.ui.home.publictab.huddles.poll.AllSchedulePollFragment" android:label="AllSchedulePollFragment" >
            <argument android:name="pollId" app:argType="integer"  />
            <argument android:name="pollType" app:argType="com.app.messej.data.model.enums.PollType" />
        </fragment>

        <fragment android:id="@+id/huddlePostCommentsFragment" android:name="com.app.messej.ui.home.publictab.huddles.comments.HuddlePostCommentsFragment" android:label="@string/title_comment_post" tools:layout="@layout/fragment_huddle_post_comments">
            <argument android:name="postId" app:argType="string" />
            <argument android:name="huddleId" app:argType="integer" />
            <action android:id="@+id/action_huddlePostCommentsFragment_to_stickerFragment" app:destination="@id/stickerFragment" />
            <action android:id="@+id/action_huddlePostCommentsFragment_to_publicUserProfileFragment" app:destination="@id/nav_profile" />
        </fragment>

        <fragment android:id="@+id/huddlePollFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.poll.PollHuddleFragment"
            android:label="@string/common_polls"
            tools:layout="@layout/fragment_huddle_post_comments">
            <argument android:name="huddleId" app:argType="integer" />
            <argument android:name="isUserManager" app:argType="boolean" />
            <argument android:name="isEditMode" app:argType="boolean" />
            <argument android:name="selectedIndex" app:argType="integer"/>
            <action android:id="@+id/action_huddlePollFragment_to_pollResultFragment" app:destination="@id/pollResultFragment" />
        </fragment>

        <dialog
            android:id="@+id/stickerFragment"
            tools:layout="@layout/fragment_sticker_select_bottom_sheet"
            android:name="com.app.messej.ui.home.sticker.StickerFragment">
        </dialog>

        <dialog
            android:id="@+id/huddleGiftListFragment"
            tools:layout="@layout/fragment_gift_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.huddles.gift.HuddleGiftFragment">
            <argument android:name="receiverId" app:argType="integer"  />
            <argument android:name="huddleId" app:argType="integer"/>
            <argument android:name="messageId" app:argType="string" app:nullable="true" android:defaultValue=" " />
            <argument android:name="singleTabItem" app:argType="com.app.messej.data.model.enums.GiftContext"/>
            <argument android:name="managerId" app:argType="integer" android:defaultValue="-1"/>
            <action android:id="@+id/action_global_giftLowBalance" app:destination="@id/giftLowBalance" />
        </dialog>



        <action android:id="@+id/action_global_addParticipantsFragment" app:destination="@id/addParticipantsFragment" />
        <action android:id="@+id/action_global_nav_huddle_info" app:destination="@id/nav_huddle_info" />
        <action android:id="@+id/action_global_manageRequestInviteFragment" app:destination="@id/manageRequestInviteFragment" />


        <dialog android:id="@+id/huddlePostFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.chat.HuddlePostFragment"
            android:label="" tools:layout="@layout/fragment_huddle_chat_post_sheet" >
            <action android:id="@+id/action_huddlePostFragment_to_stickerFragment" app:destination="@id/stickerFragment" />
            <action android:id="@+id/action_huddlePostFragment_to_huddleToFlashConfirmFragment" app:destination="@id/huddleToFlashConfirmFragment" />
        </dialog>

        <dialog android:id="@+id/huddleToFlashConfirmFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.chat.PublishInFlashConfirmationFragment"
            android:label="" tools:layout="@layout/fragment_publish_in_flash_confirmation" >
        </dialog>

        <fragment android:id="@+id/huddlePrivacy"
            android:name="com.app.messej.ui.home.publictab.huddles.privacy.HuddlePrivacyFragment"
            android:label="@string/huddle_participation_privacy"
            tools:layout="@layout/fragment_huddle_privacy">
            <argument android:name="huddleId" app:argType="integer" />
            <argument android:name="isTribe" app:argType="boolean" />
        </fragment>

        <fragment android:id="@+id/huddleCreatePollFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.poll.CreatePollFragment"
            android:label="@string/title_create_poll"
            tools:layout="@layout/fragment_create_poll">
            <argument android:name="huddleId" app:argType="integer" />
            <argument android:name="pollId" app:argType="integer" android:defaultValue="-1" />
            <argument android:name="type" app:argType="string" android:defaultValue="-1"/>
            <argument android:name="isUserManager" app:argType="boolean" android:defaultValue="false"/>
        </fragment>

        <fragment android:id="@+id/pollResultFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.poll.PollResultFragment"
            android:label="@string/poll_result"
            tools:layout="@layout/fragment_poll_result">
            <argument android:name="pollId" app:argType="integer"/>
        </fragment>

    </navigation>
    <action android:id="@+id/action_global_nav_chat_huddle" app:destination="@id/nav_chat_huddle"/>
    <action android:id="@+id/action_global_huddle_my_posts" app:destination="@id/huddleMyPostsFragment"/>
    <action android:id="@+id/action_global_tribe_edit" app:destination="@id/TribeEditDialogueFragment" />

    <dialog android:id="@+id/TribeEditDialogueFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.TribeEditDialogueFragment"
        tools:layout="@layout/layout_tribe_edit" >
        <argument android:name="huddleId" app:argType="integer" />
    </dialog>

    <fragment android:id="@+id/huddleMyPostsFragment" android:name="com.app.messej.ui.home.publictab.huddles.chat.HuddleMyPostsFragment" tools:layout="@layout/fragment_huddle_my_posts">
    </fragment>
    <navigation android:id="@+id/nav_huddle_info" app:startDestination="@id/publicHuddleInfoFragment">
        <argument android:name="huddleId" app:argType="integer" />
        <navigation android:id="@+id/navigation_huddle_participants" app:startDestination="@id/publicHuddleParticipantsFragment">
            <argument android:name="huddleId" app:argType="integer" />
            <argument android:name="huddleType" app:argType="com.app.messej.data.model.enums.HuddleType" />
            <fragment android:id="@+id/publicHuddleParticipantsFragment"
                android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleParticipantsFragment"
                tools:layout="@layout/fragment_huddle_participants">
                <argument android:name="huddleId" app:argType="integer" />
                <action android:id="@+id/action_publicHuddleParticipantsFragment_to_huddleParticipantsFilterMenuFragment" app:destination="@id/huddleParticipantsFilterMenuFragment" />
                <argument android:name="huddleType" app:argType="com.app.messej.data.model.enums.HuddleType" />
            </fragment>
            <dialog android:id="@+id/huddleParticipantsFilterMenuFragment"
                android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleParticipantFilterMenuFragment"
                android:label=""
                tools:layout="@layout/fragment_huddle_participant_filter_menu"/>
        </navigation>
        <fragment android:id="@+id/publicEditHuddleDetailsFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.EditHuddleDetailsFragment"
            tools:layout="@layout/fragment_edit_huddle_details" >
            <argument android:name="huddleId" app:argType="integer" />
        </fragment>
        <fragment android:id="@+id/publicHuddleInfoFragment"
            android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleInfoFragment"
            android:label=""
            tools:layout="@layout/fragment_huddle_info">
            <argument android:name="huddleId" app:argType="integer" />
            <action android:id="@+id/action_publicHuddleInfoFragment_to_publicHuddleReportedBaseFragment" app:destination="@id/HuddleReportsFragment" />
            <action android:id="@+id/action_publicHuddleInfoFragment_to_navigation_huddle_participants" app:destination="@id/navigation_huddle_participants" />
            <action android:id="@+id/action_publicHuddleInfoFragment_to_publicEditHuddleDetailsFragment" app:destination="@id/publicEditHuddleDetailsFragment" />
            <action android:id="@+id/action_publicHuddleInfoFragment_to_huddleSellFragment" app:destination="@id/sellHuddleFragment"/>
        </fragment>
        <action android:id="@+id/action_global_manageRequestInviteFragment" app:destination="@id/manageRequestInviteFragment" />
    </navigation>

    <fragment android:id="@+id/manageRequestInviteFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.ManageRequestInviteFragment"
        android:label="@string/title_manage_request"
        tools:layout="@layout/fragment_manage_request_invite">
        <argument android:name="huddleId" app:argType="integer" />
    </fragment>
    <navigation android:id="@+id/nav_chat_private" app:startDestination="@id/privateChatFragment">
        <argument android:name="roomId" app:argType="string" />
        <argument android:name="receiver" app:argType="integer" />
        <fragment android:id="@+id/privateChatFragment" android:name="com.app.messej.ui.chat.PrivateChatFragment" android:label="" tools:layout="@layout/fragment_chat_screen">
            <argument android:name="roomId" app:argType="string" />
            <argument android:name="receiver" app:argType="integer" />
            <action android:id="@+id/action_privateChatFragment_to_privateChatImageAttachFragment" app:destination="@id/privateChatImageAttachFragment" />
            <action android:id="@+id/action_privateChatFragment_to_privateChatVideoAttachFragment" app:destination="@id/privateChatVideoAttachFragment" />
            <action android:id="@+id/action_privateChatFragment_to_privateChatDocumentAttachFragment" app:destination="@id/privateChatDocumentAttachFragment" />
            <action android:id="@+id/action_privateChatFragment_to_privateChatLocationAttachFragment" app:destination="@id/privateChatLocationAttachFragment" />
        </fragment>
        <fragment android:id="@+id/privateChatImageAttachFragment"
            android:name="com.app.messej.ui.chat.PrivateChatImageAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_public_huddles_chat_attach_image" >
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>
        <fragment android:id="@+id/privateChatVideoAttachFragment"
            android:name="com.app.messej.ui.chat.PrivateChatVideoAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_public_huddles_chat_attach_video" >
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>

        <fragment android:id="@+id/privateChatLocationAttachFragment"
            android:name="com.app.messej.ui.chat.PrivateChatLocationAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_attach_location"
            />

        <fragment android:id="@+id/privateChatDocumentAttachFragment"
            android:name="com.app.messej.ui.chat.PrivateChatDocumentAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_base_document_attach">
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>
    </navigation>

    <action android:id="@+id/action_global_navigation_chat_private" app:destination="@id/nav_chat_private" />

    <navigation android:id="@+id/nav_chat_broadcast" app:startDestination="@id/broadcastChatFragment">
        <argument android:name="mode" app:argType="com.app.messej.data.model.enums.BroadcastMode" />
        <argument android:name="chatText" app:argType="string" app:nullable="true" />
        <fragment android:id="@+id/broadcastChatFragment" android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastChatFragment" android:label="" tools:layout="@layout/fragment_chat_screen">
            <argument android:name="mode" app:argType="com.app.messej.data.model.enums.BroadcastMode" />
            <argument android:name="chatText" app:argType="string" app:nullable="true" />
            <action android:id="@+id/action_broadcastChatFragment_to_broadcastChatAttachImageFragment" app:destination="@id/broadcastChatAttachImageFragment" />
            <action android:id="@+id/action_broadcastChatFragment_to_broadcastForwardFragment" app:destination="@id/broadcastForwardFragment" />
            <action android:id="@+id/action_broadcastChatFragment_to_broadcastOptionsMenuFragment" app:destination="@id/broadcastOptionsMenuFragment" />
            <action android:id="@+id/action_broadcastChatFragment_to_starredBroadcastFragment" app:destination="@id/BroadcastStarredFragment" />
            <action android:id="@+id/action_broadcastChatFragment_to_broadcastChatAttachVideoFragment" app:destination="@id/broadcastChatAttachVideoFragment" />
            <action android:id="@+id/action_broadcastChatFragment_to_broadcastChatAttachLocationFragment" app:destination="@id/broadcastChatAttachLocationFragment" />
        </fragment>
        <fragment android:id="@+id/broadcastChatAttachImageFragment"
            android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastChatImageAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_public_huddles_chat_attach_image" >
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>
        <fragment android:id="@+id/broadcastChatAttachLocationFragment"
            android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastChatLocationAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_attach_location" >
        </fragment>
        <fragment android:id="@+id/broadcastChatAttachVideoFragment"
            android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastChatVideoAttachFragment"
            android:label=""
            tools:layout="@layout/fragment_public_huddles_chat_attach_video" >
            <argument android:name="destination" app:argType="string" app:nullable="true" />
        </fragment>
        <fragment android:id="@+id/BroadcastStarredFragment"
            android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastStarredFragment"
            android:label="@string/broadcast_starred_page_title"
            tools:layout="@layout/fragment_broadcast_starred" >
            <argument android:name="mode" app:argType="com.app.messej.data.model.enums.BroadcastMode" />
            <action android:id="@+id/action_starredBroadcastFragment_to_broadcastForwardFragment" app:destination="@id/broadcastForwardFragment" />
        </fragment>

        <dialog android:id="@+id/broadcastOptionsMenuFragment"
            android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastOptionsMenuFragment"
            android:label=""
            tools:layout="@layout/fragment_broadcast_options_bottom_sheet" >
            <argument android:name="showPrivacyOption" app:argType="boolean" />
        </dialog>
        <dialog android:id="@+id/broadcastForwardFragment" android:name="com.app.messej.ui.home.publictab.broadcast.BroadcastForwardFragment" android:label="" tools:layout="@layout/fragment_broadcast_forward_bottom_sheet" />
        <action android:id="@+id/action_navigation_chat_broadcast_self" app:destination="@id/nav_chat_broadcast" app:popUpTo="@id/nav_chat_broadcast" app:popUpToInclusive="true" />
    </navigation>
    <action android:id="@+id/action_global_navigation_chat_broadcast" app:destination="@id/nav_chat_broadcast" />
    <action android:id="@+id/action_global_flax_transfer" app:destination="@id/sendFlaxFragment" />
    <action android:id="@+id/action_global_convertCoinToFlaxFragment" app:destination="@id/coinConvertFragment" />
    <action android:id="@+id/action_homeBusinessFragment_to_restore_rating_fragment" app:destination="@id/restoreRatingBottomSheetFragment" />
    <fragment android:id="@+id/sendFlaxFragment"
        android:name="com.app.messej.ui.home.businesstab.SendFlaxFragment"
        android:label="@string/send_flax"
        tools:layout="@layout/fragment_send_flax">
        <argument android:name="receiverId" app:argType="integer"
            android:defaultValue="-1"/>
    </fragment>

    <fragment android:id="@+id/buyCoinsFragment"
        android:name="com.app.messej.ui.home.businesstab.BuyCoinsFragment"
        android:label="@string/title_buy_coins"
        tools:layout="@layout/fragment_buy_flax" >
        <argument android:name="hideActionBar" app:argType="boolean" android:defaultValue="false" />
        <argument android:name="isBuyCoin" app:argType="boolean" android:defaultValue="false"/>
    </fragment>


    <action android:id="@+id/action_global_transaction_list" app:destination="@id/TransactionListFragment" />

    <fragment android:id="@+id/TransactionListFragment"
        android:name="com.app.messej.ui.home.businesstab.TransactionsFragment"
        android:label="@string/transactions"
        tools:layout="@layout/fragment_transactions" >
        <argument android:name="setDefaultTab" app:argType="com.app.messej.data.model.enums.TransactionTab" />
    </fragment>


    <dialog
        android:id="@+id/restoreRatingBottomSheetFragment"
        tools:layout="@layout/fragment_restore_rating_bottom_sheet_base"
        android:name="com.app.messej.ui.home.businesstab.RestoreRatingBottomSheetFragment">
        <argument android:name="isResident" app:argType="boolean" />
    </dialog>

    <action android:id="@+id/action_global_sent_flax_history" app:destination="@id/SentFlaxHistory" />

    <fragment android:id="@+id/SentFlaxHistory"
        android:name="com.app.messej.ui.home.businesstab.SentFlaxHistoryFragment"
        android:label="@string/sent_flax_records"
        tools:layout="@layout/fragment_sent_flax_history" />

    <action android:id="@+id/action_global_huddle_report_base_fragment" app:destination="@id/HuddleReportsFragment" />

    <fragment android:id="@+id/HuddleReportsFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.HuddleReportsFragment"
        android:label="@string/huddle_info_reported_messages_comments_text"
        tools:layout="@layout/fragment_base_reported">
        <argument android:name="huddleId" app:argType="integer" />
        <argument android:name="huddleType" app:argType="com.app.messej.data.model.enums.HuddleType"/>
        <argument android:name="ReportedTab" app:argType="com.app.messej.data.model.enums.ReportedTab"/>
        <action android:id="@+id/actionReportedMessagesFragment_to_reportedParticipantsFragment" app:destination="@id/ReportedParticipantsFragment" />
    </fragment>

    <fragment android:id="@+id/ReportedParticipantsFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.ReportedParticipantsFragment"
        android:label=""
        tools:layout="@layout/fragment_reported_participants">
        <argument android:name="huddleId" app:argType="integer" />
        <argument android:name="reportId" app:argType="integer" />
        <argument android:name="count" app:argType="integer" />
        <argument android:name="reportType" app:argType="com.app.messej.data.model.enums.ReportToManagerType"/>
    </fragment>

    <fragment android:id="@+id/editPersonalDetailsFragment"
        android:name="com.app.messej.ui.profile.EditPersonalDetailsFragment"
        android:label=""
        tools:layout="@layout/fragment_edit_personal_details" />

    <fragment android:id="@+id/upgradePremiumFragment" android:name="com.app.messej.ui.premium.UpgradePremiumFragment" android:label="" tools:layout="@layout/fragment_upgrade_premium" >
        <action android:id="@+id/action_upgradePremiumFragment_to_alreadySubscribedFragment" app:destination="@id/alreadySubscribedFragment"
            app:popUpTo="@id/upgradePremiumFragment" app:popUpToInclusive="true" />
    </fragment>
    <fragment android:id="@+id/settingsSecurityFragment"
        android:name="com.app.messej.ui.home.settings.security.SettingsSecurityFragment"
        android:label="@string/settings_title_security"
        tools:layout="@layout/fragment_settings_security">
        <action android:id="@+id/action_settingsSecurityFragment_to_changePasswordFragment" app:destination="@id/changePasswordFragment" />
    </fragment>
    <fragment android:id="@+id/changePasswordFragment"
        android:name="com.app.messej.ui.home.settings.security.ChangePasswordFragment"
        tools:layout="@layout/fragment_change_password" />
    <fragment android:id="@+id/settingsLanguagesFragment"
        android:name="com.app.messej.ui.home.settings.languages.SettingsLanguagesFragment"
        android:label="@string/settings_title_languages"
        tools:layout="@layout/fragment_settings_languages" />

    <fragment android:id="@+id/policyFragment" android:name="com.app.messej.ui.common.PolicyDocumentFragment" tools:layout="@layout/fragment_register_terms">
        <argument android:name="document_type" app:argType="com.app.messej.data.model.enums.DocumentType" />
        <argument android:name="isButtonVisible" app:argType="boolean" />
    </fragment>

    <dialog
        android:id="@+id/policyBottomSheetFragment" android:name="com.app.messej.ui.common.PolicyDocumentBottomSheetFragment"
        tools:layout="@layout/fragment_bottom_sheet_policy_document">
        <argument android:name="document_type" app:argType="com.app.messej.data.model.enums.DocumentType" />
    </dialog>

    <action android:id="@+id/action_global_policyBottomSheetFragment" app:destination="@id/policyBottomSheetFragment" />

    <fragment android:id="@+id/settingsAboutFragment"
        android:name="com.app.messej.ui.home.settings.about.SettingsAboutFragment"
        android:label="@string/settings_title_about_amp_help"
        tools:layout="@layout/fragment_settings_about" >
        <action android:id="@+id/action_settingsAboutFragment_to_deleteAccountFragment" app:destination="@id/deleteAccountFragment" />
    </fragment>

    <dialog android:id="@+id/accountVerificationDialogFragment"
        android:name="com.app.messej.ui.account.AccountVerificationDialogFragment"
        android:label=""
        tools:layout="@layout/fragment_account_verification_dialog" />

    <fragment android:id="@+id/changeUserLocationFragment"
        android:name="com.app.messej.ui.home.settings.ChangeUserLocationFragment"
        android:label=""
        tools:layout="@layout/fragment_register_location"
        />

    <fragment android:id="@+id/deleteAccountFragment"
        android:name="com.app.messej.ui.home.deleteaccount.DeleteAccountFragment"
        android:label="@string/delete_account_title"
        tools:layout="@layout/fragment_delete_account" />


    <fragment android:id="@+id/privacyFragment"
        android:name="com.app.messej.ui.home.settings.privacy.PrivacyFragment"
        tools:layout="@layout/fragment_privacy_settings"
        android:label="@string/settings_title_privacy">
        <action android:id="@+id/action_privacyFragment_to_privacySelectionFragment" app:destination="@id/privacySelectionFragment" />

        <action android:id="@+id/action_privacyFragment_to_blockedStars" app:destination="@id/blockedStars" />
        <action android:id="@+id/action_privacyFragment_to_blockedPrivateMessagesFragment" app:destination="@id/blockedPrivateMessagesFragment" />
        <action android:id="@+id/action_privacyFragment_to_blockedHuddlesFragment" app:destination="@id/blockedHuddlesFragment" />
        <action android:id="@+id/action_privacyFragment_to_blockedBroadcast" app:destination="@id/blockedBroadcast" />
        <action android:id="@+id/action_privacyFragment_to_privateMessagesPrivacyFragment" app:destination="@id/privateMessagesPrivacyFragment" />
        <action android:id="@+id/action_privacyFragment_to_podiumPrivacyFragment" app:destination="@id/podiumPrivacyFragment" />
    </fragment>

    <fragment android:id="@+id/privacySelectionFragment"
        android:name="com.app.messej.ui.home.settings.privacy.PrivacySelectionFragment"
        android:label="" tools:layout="@layout/fragment_settings_selection"
        >
        <argument android:name="privacySettingsMode" app:argType="com.app.messej.data.model.enums.PrivacySettingsMode" />
        <argument android:name="privacy" app:argType="string" />
        <argument android:name="audience" app:argType="string[]" />
    </fragment>
    <fragment android:id="@+id/privateMessagesPrivacyFragment"
        android:name="com.app.messej.ui.home.settings.privacy.PrivateMessagesPrivacyFragment"
        android:label="@string/title_privacy_setting_message_privacy" />

    <fragment android:id="@+id/podiumPrivacyFragment"
        android:name="com.app.messej.ui.home.settings.privacy.PodiumPrivacyFragment"
        android:label="@string/title_podium_privacy" />

    <!--    <fragment android:id="@+id/_PrivateMessagesFragment" android:name="com.app.messej.ui.home.settings.privacy.PrivateMessagesFragment"-->
    <!--        android:label="@string/title_privacy_setting_message_privacy" tools:layout="@layout/fragment_private_messages_privacy" />-->
    <fragment android:id="@+id/blockedStars"
        android:name="com.app.messej.ui.home.settings.privacy.blocked.stars.BlockedStarsFragment"
        android:label="@string/title_privacy_setting_bocked_stars" tools:layout="@layout/fragment_blocked_stars">
    </fragment>

    <fragment android:id="@+id/blockedPrivateMessagesFragment"
        android:name="com.app.messej.ui.home.settings.privacy.blocked.messages.BlockedPrivateMessagesFragment" tools:layout="@layout/fragment_blocked_private_messages"
        android:label="@string/title_privacy_settings_blocked_message"/>
    <fragment android:id="@+id/blockedHuddlesFragment" tools:layout="@layout/fragment_blocked_huddles" android:name="com.app.messej.ui.home.settings.privacy.blocked.huddles.BlockedHuddlesFragment" android:label="@string/title_privacy_setting_message_blocked_huddles" />
    <fragment android:id="@+id/blockedBroadcast" tools:layout="@layout/fragment_broadcast_privacy"
        android:name="com.app.messej.ui.home.settings.privacy.blocked.broadcast.BroadcastPrivacyFragment"
        android:label="@string/title_privacy_setting_broadcast_privacy" />

    <dialog android:id="@+id/imageAttachSourceFragment"
        android:name="com.app.messej.ui.chat.ChatAttachSourceFragment"
        android:label=""
        tools:layout="@layout/fragment_attach_source_bottom_sheet" >
        <argument android:name="allowedSources" app:argType="com.app.messej.data.model.enums.AttachmentSource[]" />
    </dialog>
    <dialog android:id="@+id/profileImageAttachSourceFragment"
        android:name="com.app.messej.ui.profile.ProfileImageAttachSourceFragment"
        android:label=""
        tools:layout="@layout/fragment_image_attach_source">
        <argument android:name="isHeaderHidden"  app:argType="boolean" android:defaultValue="false" />
        <argument android:name="allowUseProfilePhoto" app:argType="boolean" android:defaultValue="false" />
        <argument android:name="isPodium" app:argType="boolean" android:defaultValue="false" />

    </dialog>
    <action android:id="@+id/action_global_homePrivateFragment" app:destination="@id/homePrivateFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_policyFragment" app:destination="@id/policyFragment" />

    <fragment android:id="@+id/settingsNotificationFragment" tools:layout="@layout/fragment_settings_notification" android:name="com.app.messej.ui.home.settings.notification.SettingsNotificationFragment" android:label="@string/common_notifications" />
    <navigation android:id="@+id/nav_star_broadcast" app:startDestination="@id/starBroadcastFragment">
        <argument android:name="broadcaster" app:argType="integer" />
        <fragment android:id="@+id/starBroadcastFragment" android:name="com.app.messej.ui.home.publictab.stars.StarBroadcastFragment" tools:layout="@layout/fragment_star_broadcast">
            <argument android:name="broadcaster" app:argType="integer" />
            <action android:id="@+id/action_starBroadcastFragment_to_starBroadcastOptionsMenuFragment" app:destination="@id/starBroadcastOptionsMenuFragment" />
            <action android:id="@+id/action_starBroadcastFragment_to_StarBroadcastStarredFragment" app:destination="@id/StarBroadcastStarredFragment" />
        </fragment>
        <dialog android:id="@+id/starBroadcastOptionsMenuFragment"
            android:name="com.app.messej.ui.home.publictab.stars.StarBroadcastOptionsMenuFragment"
            android:label=""
            tools:layout="@layout/fragment_star_broadcast_options_bottom_sheet" >
        </dialog>

        <fragment android:id="@+id/StarBroadcastStarredFragment"
            android:name="com.app.messej.ui.home.publictab.stars.StarBroadcastStarredFragment"
            android:label="@string/broadcast_starred_page_title"
            tools:layout="@layout/fragment_broadcast_starred" >
            <argument android:name="broadcaster" app:argType="integer" />
        </fragment>
    </navigation>
    <action android:id="@+id/action_global_navigation_star_broadcast" app:destination="@id/nav_star_broadcast" />
    <action android:id="@+id/action_global_locationSearchFragment" app:destination="@id/locationSearchFragment"/>
    <action android:id="@+id/action_global_giftFileFragment" app:destination="@id/giftFragment" />
    <action android:id="@+id/action_global_alreadySubscribedFragment" app:destination="@id/alreadySubscribedFragment" />
    <fragment
        android:id="@+id/notificationFragment"
        android:name="com.app.messej.ui.home.notification.NotificationFragment"
        tools:layout="@layout/fragment_notification"
        android:label="@string/common_notifications" >
    </fragment>

    <fragment
        android:id="@+id/purchaseHistoryFragment"
        android:name="com.app.messej.ui.home.gift.GiftPurchasedHistoryFragment"
        tools:layout="@layout/fragment_gift_purchased_history"
        android:label="@string/gift_title_purchase_history" >
    </fragment>
    <fragment android:id="@+id/giftFragment" android:name="com.app.messej.ui.home.gift.GiftFragment" android:label="@string/bottom_sheet_gift" >
        <argument android:name="tab" app:argType="integer" android:defaultValue="-1" />
        <action android:id="@+id/action_giftFragment_to_pointsToConvertFragment" app:destination="@id/coinConvertFragment" />
        <action android:id="@+id/action_giftFragment_to_purchaseHistoryFragment" app:destination="@id/purchaseHistoryFragment" />
        <action android:id="@+id/action_giftFragment_to_flaxToPointsConvertDialogFragment" app:destination="@id/flaxToPointsConvertDialogFragment" />
        <action android:id="@+id/action_giftFragment_to_receivedGiftHistoryFragment" app:destination="@id/receivedGiftHistoryFragment" />
        <action android:id="@+id/action_global_giftLowBalance" app:destination="@id/giftLowBalance" />
        <action android:id="@+id/action_giftFragment_to_giftInfoBottomSheet" app:destination="@id/giftInfoBottomSheet"/>
    </fragment>
    <dialog android:id="@+id/flaxToPointsConvertDialogFragment"
        android:name="com.app.messej.ui.home.gift.FlaxToPointsConvertDialogFragment"
        android:label="FlaxToPointsConvertDialogFragment"
        tools:layout="@layout/fragment_points_to_flax_dialog" >
        <argument android:name="Point" app:argType="string"/>
        <argument android:name="flax" app:argType="string"/>
    </dialog>
    <fragment android:id="@+id/reportToManagerFragment"
        android:name="com.app.messej.ui.common.ReportToManagerFragment"
        android:label="@string/user_action_report_to_manager" tools:layout="@layout/fragment_report">
        <argument android:name="id" app:argType="integer" />
        <argument android:name="messageId" app:argType="string"/>
        <argument android:name="commentId" app:argType="string" app:nullable="true" />
        <argument android:name="reportType" app:argType="com.app.messej.data.model.enums.ReportToManagerType" />
    </fragment>
    <fragment
        android:id="@+id/coinConvertFragment"
        android:name="com.app.messej.ui.home.gift.GiftConvertFragment"
        tools:layout="@layout/fragment_gift_convert"
        android:label="@string/title_coins_converter" >
        <argument android:name="convertType" app:argType="com.app.messej.data.model.enums.GiftConversion"  />
        <action android:id="@+id/action_coinConvert_to_giftConversionHistory" app:destination="@id/giftConversionHistory" />
        <argument android:name="availableCoins" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="availableFlix" app:argType="string" app:nullable="true" android:defaultValue=""/>
    </fragment>

    <fragment
        android:id="@+id/giftConversionHistory"
        android:name="com.app.messej.ui.home.gift.GiftConversionHistoryFragment"
        tools:layout="@layout/fragment_gift_conversion_history"
        android:label="@string/gift_conversion_history">
    </fragment>

    <fragment
        android:id="@+id/receivedGiftHistoryFragment"
        android:name="com.app.messej.ui.home.gift.ReceivedGiftHistoryFragment"
        tools:layout="@layout/fragment_received_gift_history"
        android:label="@string/title_gift_received_history" >
        <argument android:name="giftId" app:argType="integer"/>
    </fragment>

    <fragment
        android:id="@+id/flashFilterFragment"
        android:name="com.app.messej.ui.home.publictab.flash.create.FlashFilterFragment"
        tools:layout="@layout/fragment_flash_filter"
        android:label="@string/flash_filter_title" >
        <argument android:name="country" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="age" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="gender" app:argType="string" app:nullable="true" android:defaultValue="" />
        <argument android:name="category" app:argType="integer" android:defaultValue="-1" />
    </fragment>
    <action android:id="@+id/action_global_notificationFragment" app:destination="@id/notificationFragment" />
    <action android:id="@+id/action_global_homePublicFragment" app:destination="@id/homePublicFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_homeBusinessFragment" app:destination="@id/homeBusinessFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_upgradePremiumFragment" app:destination="@id/upgradePremiumFragment" />
    <action android:id="@+id/action_global_profileFragment" app:destination="@id/profileFragment" />
    <action android:id="@+id/action_global_forwardHomeFragment" app:destination="@id/navigation_forward" />
    <action android:id="@+id/action_global_blockedPrivateMessagesFragment" app:destination="@id/blockedPrivateMessagesFragment"/>
    <action android:id="@+id/action_global_imageAttachSourceFragment" app:destination="@id/imageAttachSourceFragment"/>

    <action android:id="@+id/action_global_profileImageAttachSourceFragment" app:destination="@id/profileImageAttachSourceFragment"/>
    <dialog android:id="@+id/reportFragment" android:name="com.app.messej.ui.legal.report.ReportFragment" android:label="" tools:layout="@layout/fragment_report">
        <argument android:name="reportPackage" app:argType="string" />
    </dialog>
    <action android:id="@+id/action_global_reportFragment" app:destination="@id/reportFragment" />
    <action android:id="@+id/action_global_publicUserProfileFragment" app:destination="@id/nav_profile" />
    <dialog
        android:id="@+id/incompleteProfileDialogFragment"
        android:name="com.app.messej.ui.home.publictab.IncompleteProfileDialogFragment"
        android:label="fragment_incomplete_profile_dialog"
        tools:layout="@layout/fragment_incomplete_profile_dialog" />
    <action android:id="@+id/action_global_incompleteProfileDialogFragment" app:destination="@id/incompleteProfileDialogFragment" app:launchSingleTop="true" />
    <action android:id="@+id/action_global_blockedBroadcast" app:destination="@id/blockedBroadcast"/>
    <fragment
        android:id="@+id/businessOperationsDearsList"
        android:name="com.app.messej.ui.home.businesstab.operations.tasks.dears.BusinessOperationsDearsList"
        tools:layout="@layout/fragment_business_operations_dears_list"
        android:label="@string/profile_dears" />
    <fragment android:id="@+id/alreadySubscribedFragment" android:name="com.app.messej.ui.premium.AlreadySubscribedFragment" tools:layout="@layout/fragment_subscribe" android:label="@string/title_subscription_details" >
        <argument android:name="isShowWelcomePopup" app:argType="boolean" />
    </fragment>
    <fragment android:id="@+id/publicHuddlesStandaloneFragment"
        android:name="com.app.messej.ui.home.publictab.huddles.PublicHuddlesStandaloneFragment"
        tools:layout="@layout/fragment_home_public_huddles" >
        <action android:id="@+id/action_publicHuddlesStandaloneFragment_to_publicHuddleSearchFragment" app:destination="@id/publicHuddleSearchFragment" />
        <action android:id="@+id/action_publicHuddlesStandaloneFragment_to_publicHuddleSuggestionSearchFragment" app:destination="@id/publicHuddleSuggestionSearchFragment" />
        <argument android:name="subtab" app:argType="integer" android:defaultValue="-1" />
    </fragment>
    <fragment android:id="@+id/publicFlashStandaloneFragment"
        android:name="com.app.messej.ui.home.publictab.flash.PublicFlashStandaloneFragment"
        tools:layout="@layout/fragment_public_flash" >
        <action android:id="@+id/action_publicFlashStandaloneFragment_to_flashFilterFragment" app:destination="@id/flashFilterFragment" />
        <action android:id="@+id/action_publicFlashStandaloneFragment_to_myFlashFragment" app:destination="@id/nav_my_flash" />
        <action android:id="@+id/action_publicFlashStandaloneFragment_to_flashPlayerFragment" app:destination="@id/flashPlayerFragment" />
        <action android:id="@+id/action_publicFlashStandaloneFragment_to_flashSearchFragment" app:destination="@id/nav_flash_search" />
    </fragment>
    <fragment
        android:id="@+id/createPodiumFragment"
        android:name="com.app.messej.ui.home.publictab.podiums.create.CreatePodiumFragment"
        tools:layout="@layout/fragment_create_podium">
        <argument android:name="podiumId" app:argType="string" android:defaultValue="-1"/>
    </fragment>

    <fragment android:id="@+id/publicPodiumStandaloneFragment" android:name="com.app.messej.ui.home.publictab.podiums.PublicPodiumStandaloneFragment"
        tools:layout="@layout/fragment_public_podium">
        <action android:id="@+id/action_publicPodiumStandaloneFragment_to_podiumSearchFragment" app:destination="@id/podiumSearchFragment" />
    </fragment>
    <action android:id="@+id/action_global_publicHuddlesStandaloneFragment" app:destination="@id/publicHuddlesStandaloneFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_publicFlashStandaloneFragment" app:destination="@id/publicFlashStandaloneFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_publicPodiumStandaloneFragment" app:destination="@id/publicPodiumStandaloneFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <action android:id="@+id/action_global_accountVerificationDialogFragment" app:destination="@id/accountVerificationDialogFragment"/>
    <fragment android:id="@+id/homeFragment" android:name="com.app.messej.ui.home.HomeFragment">
        <argument android:name="eventType" app:argType="string" android:defaultValue="" />
        <argument android:name="eventData" app:argType="string" android:defaultValue="" />
    </fragment>
    <action android:id="@+id/action_global_homeFragment" app:destination="@id/homeFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true" />
    <fragment android:id="@+id/publicStarsStandaloneFragment" android:name="com.app.messej.ui.home.publictab.stars.PublicStarsStandaloneFragment" android:label="@string/home_private_tab_following" tools:layout="@layout/fragment_public_stars" >
        <action android:id="@+id/action_publicStarsStandaloneFragment_to_publicStarsSearchFragment" app:destination="@id/publicStarsSearchFragment" />
        <action android:id="@+id/action_publicStarsStandaloneFragment_to_publicStarsAddFragment" app:destination="@id/publicStarsAddFragment" />
    </fragment>
    <fragment android:id="@+id/publicBroadcastStandaloneFragment" android:label="@string/home_private_tab_followers"
        android:name="com.app.messej.ui.home.publictab.broadcast.PublicBroadcastStandaloneFragment" tools:layout="@layout/fragment_public_broadcast" >
        <argument android:name="tab" app:argType="integer" android:defaultValue="-1" />
        <action android:id="@+id/action_publicBroadcastStandaloneFragment_to_homePublicBroadcastSearchFragment" app:destination="@id/homePublicBroadcastSearchFragment" />
        <action android:id="@+id/action_publicBroadcastStandaloneFragment_to_publicStarsAddFragment" app:destination="@id/publicStarsAddFragment" />
    </fragment>
    <dialog
        android:id="@+id/privateMessageRestrictDialogFragment"
        tools:layout="@layout/fragment_private_message_restrict_dialog"
        android:name="com.app.messej.ui.home.privatetab.messages.PrivateMessageRestrictDialogFragment">
    </dialog>
    <action android:id="@+id/action_global_settingsAboutFragment" app:destination="@id/settingsAboutFragment"/>
    <fragment android:id="@+id/huddleRequestListFragment" android:name="com.app.messej.ui.home.publictab.huddles.HuddleRequestListFragment" android:label="@string/huddle_title_requests_and_invites"
        tools:layout="@layout/fragment_huddle_request_list"/>
    <fragment android:id="@+id/sellHuddleListFragment" android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.SellHuddlesListFragment" android:label="@string/title_sell_huddle"
        tools:layout="@layout/fragment_sell_huddles_list">
        <action android:id="@+id/action_SellHuddleListFragment_to_buySellHuddleFragment" app:destination="@id/buySellHuddleFragment"/>
    </fragment>

    <fragment android:id="@+id/buySellHuddleFragment" android:name="com.app.messej.ui.home.publictab.huddles.huddleInfo.BuySellHuddleFragment" android:label=""
        tools:layout="@layout/fragment_buy_sell_huddle">
        <argument android:name="huddleId" app:argType="integer" />
        <action android:id="@+id/action_BuyHuddleFragment_To_GiftConverterFragment" app:destination="@id/coinConvertFragment"></action>
    </fragment>
    <action android:id="@+id/action_global_huddleRequestListFragment" app:destination="@id/huddleRequestListFragment"/><action android:id="@+id/action_global_createHuddleFragment" app:destination="@id/createHuddleFragment"/>
    <action android:id="@+id/action_global_biometricAuthFragment" app:destination="@id/biometricAuthFragment" app:popUpTo="@id/nav_graph_home" app:popUpToInclusive="true"/>
    <fragment android:id="@+id/biometricAuthFragment" android:name="com.app.messej.ui.auth.biometric.BiometricAuthFragment" android:label="fragment_biometric_auth" tools:layout="@layout/fragment_biometric_auth" />
    <action android:id="@+id/action_global_huddleDeleteLeaveConfirmFragment" app:destination="@id/huddleDeleteLeaveConfirmFragment"/>
    <action android:id="@+id/action_global_PostImageEdit" app:destination="@id/postImageEdit"/>
    <navigation android:id="@+id/nav_profile" app:startDestination="@id/publicUserProfileLoaderFragment">
        <argument android:name="id" app:argType="integer" />
        <argument android:name="popOnAction" android:defaultValue="false" app:argType="boolean" />
        <argument android:name="context" android:defaultValue="GENERAL" app:argType="com.app.messej.data.model.enums.UserProfileContext" />
        <argument android:name="isSelf" android:defaultValue="false" app:argType="boolean" />
        <fragment android:id="@+id/publicUserProfileLoaderFragment" android:name="com.app.messej.ui.profile.PublicUserProfileLoaderFragment" tools:layout="@layout/fragment_public_user_profile_loader">
            <argument android:name="id" app:argType="integer" />
            <argument android:name="popOnAction" android:defaultValue="false" app:argType="boolean" />
            <argument android:name="context" android:defaultValue="GENERAL" app:argType="com.app.messej.data.model.enums.UserProfileContext" />
            <argument android:name="isSelf" android:defaultValue="false" app:argType="boolean" />
            <action android:id="@+id/action_publicUserProfileLoaderFragment_to_publicUserProfileFragment"
                app:destination="@id/publicUserProfileFragment"
                app:popUpTo="@id/publicUserProfileLoaderFragment" app:popUpToInclusive="true" />
            <action android:id="@+id/action_publicUserProfileLoaderFragment_to_publicUserIdCard"
                app:destination="@id/publicUserIdCardLatest"
                app:popUpTo="@id/publicUserProfileLoaderFragment"
                app:popUpToInclusive="true" />
        </fragment>
        <fragment android:id="@+id/publicUserProfileFragment" android:name="com.app.messej.ui.profile.PublicUserProfileFragment" tools:layout="@layout/fragment_public_user_profile">
            <argument android:name="id" app:argType="integer" />
            <argument android:name="popOnAction" android:defaultValue="false" app:argType="boolean" />
            <argument android:name="context" android:defaultValue="GENERAL" app:argType="com.app.messej.data.model.enums.UserProfileContext" />
        </fragment>

        <fragment android:id="@+id/publicUserIdCardLatest"
            android:name="com.app.messej.ui.profile.PublicUserIDCardFragment"
            tools:layout="@layout/fragment_public_user_id_card">
            <argument android:name="useId" app:argType="integer" />
            <argument android:name="popOnAction" app:argType="boolean" android:defaultValue="false" />
            <argument android:name="context" app:argType="com.app.messej.data.model.enums.UserProfileContext" android:defaultValue="GENERAL" />
            <action android:id="@+id/action_publicUserIdCardLatest_to_publicTotalDetailsBottomSheetFragment" app:destination="@id/publicTotalDetailsBottomSheetFragment" />
            <action android:id="@+id/action_publicUserIdCardLatest_to_popularityDetailsBottomSheetDialogFragment" app:destination="@id/popularityDetailsBottomSheetDialogFragment" />
        </fragment>
        <dialog android:id="@+id/popularityDetailsBottomSheetDialogFragment"
            android:name="com.app.messej.ui.profile.PopularityDetailsBottomSheetDialogFragment"
            android:label="fragment_popularity_details_bottom_sheet_dialog"
            tools:layout="@layout/fragment_popularity_details_bottom_sheet_dialog" />
        <dialog
            android:id="@+id/publicTotalDetailsBottomSheetFragment"
            tools:layout="@layout/fragment_public_total_details_bottom_sheet"
            android:name="com.app.messej.ui.profile.PublicTotalDetailsBottomSheetFragment">
            <argument android:name="useId" app:argType="integer" />
        </dialog>

        <action android:id="@+id/action_global_privateMessageRestrictDialogFragment" app:destination="@id/privateMessageRestrictDialogFragment" />
    </navigation>
    <dialog android:id="@+id/settingsBottomSheetFragment"
        android:name="com.app.messej.ui.home.settings.SettingsBottomSheetFragment"
        android:label="fragment_settings_bottom_sheet"
        tools:layout="@layout/fragment_settings_bottom_sheet" >
        <action android:id="@+id/action_settingsBottomSheetFragment_to_accountManagementFragment" app:destination="@id/accountManagementFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_privacyFragment" app:destination="@id/privacyFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_settingsSecurityFragment" app:destination="@id/settingsSecurityFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_settingsLanguagesFragment" app:destination="@id/settingsLanguagesFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_settingsAboutFragment" app:destination="@id/settingsAboutFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_settingsNotificationFragment" app:destination="@id/settingsNotificationFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_publicStarsStandaloneFragment" app:destination="@id/publicStarsStandaloneFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_publicBroadcastStandaloneFragment" app:destination="@id/publicBroadcastStandaloneFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_publicStarsStandaloneFragment2" app:destination="@id/publicStarsStandaloneFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_dealsFragment" app:destination="@id/dealsStandAloneFragment" />
        <argument android:name="isResident" app:argType="boolean" android:defaultValue="false" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_businessStatementFreeFragment" app:destination="@id/businessStatementFragmentFree" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_businessWorkStatusStandAloneFragment" app:destination="@id/businessWorkStandAloneFragment" />
        <action android:id="@+id/action_settingsBottomSheetFragment_to_empowermentListsFragment" app:destination="@id/empowermentListsFragment" />
    </dialog>

    <fragment android:id="@+id/accountManagementFragment"
        android:name="com.app.messej.ui.home.settings.accountManagement.AccountManagementFragment"
        android:label=""
        tools:layout="@layout/fragment_account_management">
        <action android:id="@+id/action_accountManagement_to_levelsFragment" app:destination="@id/levelsFragment" />
        <action android:id="@+id/action_accountManagement_to_performanceRatingFragment" app:destination="@id/performanceRatingFragment" />
        <action android:id="@+id/action_accountManagement_to_changeUserLocationFragment" app:destination="@id/changeUserLocationFragment" />
        <action android:id="@+id/action_accountManagement_to_alreadySubscribedFragment" app:destination="@id/alreadySubscribedFragment" />
        <action android:id="@+id/action_accountManagement_to_upgradeSupportFragment" app:destination="@id/upgradeSupportFragment" />
        <action android:id="@+id/action_accountManagementFragment_to_upgradeGoldenFragment" app:destination="@id/upgradeGoldenFragment" />
        <action android:id="@+id/action_accountManagementFragment_to_alreadySubscribedGoldenFragment" app:destination="@id/alreadySubscribedGoldenFragment" />
    </fragment>

    <fragment android:id="@+id/upgradeSupportFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.upgradeSupport.UpgradeSupportFragment"
        android:label=""
        tools:layout="@layout/fragment_upgrade_support">
    </fragment>

    <fragment
        android:id="@+id/socialPolicyDocumentFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.SocialPolicyDocumentFragment"
        tools:layout="@layout/fragment_social_policy_document">
        <argument android:name="document_type" app:argType="com.app.messej.data.model.enums.DocumentType" />
    </fragment>
    <action android:id="@+id/action_global_socialPolicyDocumentFragment" app:destination="@id/socialPolicyDocumentFragment" />

    <fragment android:id="@+id/socialAffairHomeFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.homeScreen.SocialAffairHomeFragment"
        android:label=""
        tools:layout="@layout/fragment_social_affairs_home">
        <action android:id="@+id/action_socialAffairHomeFragment_to_socialAffairActiveCasesFragment" app:destination="@id/socialAffairActiveCasesFragment" />
        <action android:id="@+id/action_socialAffairHomeFragment_to_socialAffairHonoursFragment" app:destination="@id/socialAffairHonoursFragment" />
        <action android:id="@+id/action_socialAffairHomeFragment_to_socialAffairMinisterFragment" app:destination="@id/socialAffairMinisterFragment" />
    </fragment>
    <action android:id="@+id/action_global_socialSupportFragment" app:destination="@id/socialAffairSocialSupportFragment" />

    <fragment android:id="@+id/socialAffairCaseInfoFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.caseInfo.SocialCaseInfoFragment"
        android:label=""
        tools:layout="@layout/fragment_social_case_info">
        <argument android:name="id" app:argType="integer" />
        <argument android:name="isUpgradeSupport" app:argType="boolean" />
        <action android:id="@+id/action_socialAffairCaseInfoFragment_to_socialAffairVotersFragment" app:destination="@id/socialAffairVotersFragment" />
        <action android:id="@+id/action_socialAffairCaseInfoFragment_to_socialAffairAskedQuestionsFragment" app:destination="@id/socialAffairAskedQuestionsFragment" />
    </fragment>
    <action android:id="@+id/action_global_socialCaseInfoFragment" app:destination="@id/socialAffairCaseInfoFragment" />

    <fragment android:id="@+id/socialAffairHonoursFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.honours.SocialHonoursFragment"
        android:label=""
        tools:layout="@layout/fragment_social_honours">
    </fragment>

    <fragment android:id="@+id/socialAffairVotersFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.voters.SocialVotersFragment"
        android:label=""
        tools:layout="@layout/fragment_social_voters">
        <argument android:name="caseId" app:argType="integer" />
    </fragment>

    <fragment android:id="@+id/socialAffairAskedQuestionsFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.questions.SocialAskedQuestionsFragment"
        android:label=""
        tools:layout="@layout/fragment_social_asked_questions">
        <argument android:name="caseId" app:argType="integer" />
    </fragment>

    <fragment android:id="@+id/socialAffairSocialSupportFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.socialSupport.SocialSupportFragment"
        android:label=""
        tools:layout="@layout/fragment_social_support">
    </fragment>
    <action android:id="@+id/action_socialAffairRequestPersonalSupport" app:destination="@id/socialAffairRequestPersonalSupport" />

    <fragment android:id="@+id/socialAffairDraftsFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.drafts.SocialDraftsFragment"
        android:label=""
        tools:layout="@layout/fragment_social_drafts">
    </fragment>
    <action android:id="@+id/action_socialAffairDrafsFragment" app:destination="@id/socialAffairDraftsFragment" />

    <fragment android:id="@+id/socialAffairRequestPersonalSupport"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.requestPersonalSupport.RequestPersonalSupportFragment"
        android:label=""
        tools:layout="@layout/fragment_social_request_support">
        <argument android:name="caseId" app:argType="string" app:nullable="true" />
    </fragment>

    <fragment android:id="@+id/socialAffairMinisterFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.minister.SocialAffairMinisterFragment"
        android:label=""
        tools:layout="@layout/fragment_social_minister">
    </fragment>

    <dialog android:id="@+id/idCardBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.IDCardBottomSheetFragment"
        android:label=""
        tools:layout="@layout/fragment_id_card_bottom_sheet">
        <argument android:name="userId" app:argType="integer" />
    </dialog>
    <action android:id="@+id/action_global_idCardBottomSheetFragment" app:destination="@id/idCardBottomSheetFragment" />

    <dialog android:id="@+id/socialDonateBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.donateBottomSheet.SocialDonateBottomSheetFragment"
        android:label=""
        tools:layout="@layout/fragment_social_donate_bottom_sheet">
        <argument android:name="socialCase" app:argType="string" />
        <argument android:name="isFromCaseInfo" app:argType="boolean" android:defaultValue="false" />
    </dialog>

    <fragment android:id="@+id/socialAffairCommitteeFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.committee.SocialCommitteeFragment"
        android:label=""
        tools:layout="@layout/fragment_social_committee">
    </fragment>
    <action android:id="@+id/action_global_socialAffairCommitteeFragment" app:destination="@id/socialAffairCommitteeFragment" />

    <fragment android:id="@+id/socialAffairActiveCasesFragment"
        android:name="com.app.messej.ui.home.publictab.socialAffairs.activeCases.SocialActiveCasesFragment"
        android:label=""
        tools:layout="@layout/fragment_social_active_cases">
        <argument android:name="tab" app:argType="com.app.messej.data.model.enums.SocialActiveCaseMainTab" android:defaultValue="Donate" />
    </fragment>
    <action android:id="@+id/action_socialDonateBottomSheetFragment" app:destination="@id/socialDonateBottomSheetFragment" />

    <fragment android:id="@+id/levelsFragment"
        android:name="com.app.messej.ui.home.settings.levels.LevelsFragment"
        android:label=""
        tools:layout="@layout/fragment_levels">
        <action android:id="@+id/action_levelsFragment_to_upgradeUserLevelFragment" app:destination="@id/upgradeUserLevelFragment" />
    </fragment>

    <fragment android:id="@+id/performanceRatingFragment"
        android:name="com.app.messej.ui.home.settings.rating.RatingFragment"
        android:label=""
        tools:layout="@layout/fragment_performance_rating">
        <action android:id="@+id/rating_fragment_to_aboutRatingFragment" app:destination="@id/aboutRatingFragment" />
    </fragment>

    <dialog
        android:id="@+id/aboutRatingFragment"
        android:name="com.app.messej.ui.home.settings.rating.AboutRatingBottomSheetFragment"
        tools:layout="@layout/fragment_about_rating_bottom_sheet"
        android:label=""
        />

    <fragment android:id="@+id/restoreRatingFragment"
        android:name="com.app.messej.ui.home.settings.restoreRating.RestoreRatingFragment"
        android:label=""
        tools:layout="@layout/fragment_restore_rating">
    </fragment>
    <action android:id="@+id/action_global_restoreRatingFragment" app:destination="@id/restoreRatingFragment" />
    <action android:id="@+id/action_global_performanceRatingFragment" app:destination="@id/performanceRatingFragment" />

    <fragment android:id="@+id/myETribeFragment"
        android:name="com.app.messej.ui.home.publictab.myETribe.MyETribeFragment"
        android:label=""
        tools:layout="@layout/fragment_my_e_tribe">
        <action android:id="@+id/action_myETribeFragment_to_contactETribeFragment" app:destination="@id/contactETribeFragment"/>
    </fragment>
    <action android:id="@+id/action_global_myETribeFragment" app:destination="@id/myETribeFragment"/>

    <fragment android:id="@+id/contactETribeFragment"
        android:name="com.app.messej.ui.home.publictab.myETribe.contactETribe.ContactETribeFragment"
        android:label=""
        tools:layout="@layout/fragment_contact_e_tribe">
        <argument android:name="allUsersCount" app:argType="integer" android:defaultValue="0" />
        <argument android:name="activeUsersCount" app:argType="integer" android:defaultValue="0" />
        <argument android:name="inactiveUsersCount" app:argType="integer" android:defaultValue="0" />
    </fragment>

    <fragment android:id="@+id/businessWorkStandAloneFragment"
        android:name="com.app.messej.ui.home.businesstab.operations.status.BusinessWorkStatusStandAloneFragment"
        android:label=""
        tools:layout="@layout/fragment_business_work_status_stand_alone">
    </fragment>

    <dialog
        android:id="@+id/receivedFlaxBottomSheet"
        android:name="com.app.messej.ui.home.gift.bottomSheet.ReceivedFlaxBottomSheet"
        android:label="fragment_received_flax_bottom_sheet"
        tools:layout="@layout/fragment_received_flax_bottom_sheet">
    </dialog>

    <dialog
        android:id="@+id/giftInfoBottomSheet"
        android:name="com.app.messej.ui.home.gift.bottomSheet.GiftInfoBottomSheet"
        android:label="fragment_gift_info_bottom_sheet"
        tools:layout="@layout/fragment_gift_info_bottomsheet">
    </dialog>

    <dialog android:id="@+id/giftAlertBottomSheetFragment"
        android:name="com.app.messej.ui.home.gift.GiftAlertBottomSheetFragment"
        android:label="fragment_gift_notification_bottom_sheet"
        tools:layout="@layout/fragment_gift_alert_bottom_sheet" >
        <argument android:name="giftId" app:argType="integer" />
        <argument android:name="senderId" app:argType="integer" />
        <argument android:name="fromSocket" app:argType="boolean" />
        <argument android:name="senderName" app:argType="string" app:nullable="true" android:defaultValue=""
            />
    </dialog>

    <dialog android:id="@+id/BirthdayAlertBottomSheetFragment"
        android:name="com.app.messej.ui.home.gift.bottomSheet.BirthdayAlertBottomSheetFragment"
        android:label="fragment_birthday_bottom_sheet"
        tools:layout="@layout/fragment_birthday_alert_bottom_sheet">
        <argument android:name="currentBirthday" app:argType="boolean"/>
        <action android:id="@+id/action_BirthdayAlertBottomSheetFragment_to_todaysBirthdayFragment" app:destination="@id/todaysBirthdayFragment" />
    </dialog>

    <dialog android:id="@+id/PresidentAppointmentBottomSheetFragment"
        android:name="com.app.messej.ui.home.gift.bottomSheet.PresidentAppointmentAlertBottomSheetFragment"
        android:label="fragment_birthday_bottom_sheet"
        tools:layout="@layout/fragment_president_appointement_alert_bottom_sheet">
        <argument android:name="fromSocket" app:argType="boolean" android:defaultValue="false"/>
    </dialog>

    <dialog android:id="@+id/BirthdayNotificationBottomSheetFragment"
        android:name="com.app.messej.ui.home.gift.bottomSheet.BirthdayNotificationBottomSheetFragment"
        android:label="fragment_birthday_bottom_sheet"
        tools:layout="@layout/fragment_birthday_alert_bottom_sheet">
        <argument android:name="userId" app:argType="integer" android:defaultValue="-1"/>
        <argument android:name="currentBirthday" app:argType="boolean"/>
    </dialog>

    <dialog
        android:id="@+id/internalShareFragment"
        tools:layout="@layout/fragment_podium_share_bottom_sheet"
        android:name="com.app.messej.ui.home.publictab.common.InternalShareBottomSheet">
        <argument android:name="message" app:argType="string" />
        <argument android:name="source" app:argType="com.app.messej.data.model.enums.ForwardSource" />
    </dialog>

    <fragment
        android:id="@+id/todaysBirthdayFragment"
        android:name="com.app.messej.ui.home.gift.TodaysBirthdayFragment"
        tools:layout="@layout/fragment_todays_birthday"
        android:label="Today's Birthdays">
    </fragment>

    <action android:id="@+id/action_global_settingsBottomSheetFragment" app:destination="@id/settingsBottomSheetFragment" app:launchSingleTop="true" />
    <action android:id="@+id/action_global_receivedFlaxBottomSheetFragment" app:destination="@id/receivedFlaxBottomSheet" app:launchSingleTop="true" />

    <action android:id="@+id/action_global_notificationLottieBottomSheetFragment" app:destination="@id/giftAlertBottomSheetFragment" app:launchSingleTop="true" />
    <action android:id="@+id/action_global_presidentBottomSheetFragment" app:destination="@id/PresidentAppointmentBottomSheetFragment" app:launchSingleTop="true" />



    <navigation android:id="@+id/nav_flash_record" app:startDestination="@id/flashRecordFragment">
        <argument android:name="drafId" app:argType="string" app:nullable="true" />
        <argument android:name="flashDuration" app:argType="integer" android:defaultValue="0" />
        <fragment android:id="@+id/flashRecordFragment" tools:layout="@layout/fragment_flash_record" android:name="com.app.messej.ui.home.publictab.flash.create.FlashRecordFragment" android:label="" >
            <action android:id="@+id/action_flashRecordFragment_to_flashRecordEditFragment" app:destination="@id/flashRecordEditFragment" />
            <action android:id="@+id/action_flashRecordFragment_to_flashRecordFinalizeFragment"
                app:destination="@id/flashRecordFinalizeFragment"
                app:popUpTo="@id/flashRecordFragment"
                app:popUpToInclusive="true" />
            <argument android:name="drafId" app:argType="string" app:nullable="true" />
            <argument android:name="flashDuration" app:argType="integer" android:defaultValue="0" />
        </fragment>
        <fragment android:id="@+id/flashRecordEditFragment" tools:layout="@layout/fragment_flash_record_edit" android:name="com.app.messej.ui.home.publictab.flash.create.FlashRecordEditFragment" android:label="" >
            <action android:id="@+id/action_flashRecordEditFragment_to_flashRecordFinalizeFragment" app:destination="@id/flashRecordFinalizeFragment" />
        </fragment>
        <fragment android:id="@+id/flashRecordFinalizeFragment" tools:layout="@layout/fragment_flash_record_finalize" android:name="com.app.messej.ui.home.publictab.flash.create.FlashRecordFinalizeFragment" android:label="" />
        <action android:id="@+id/action_global_myFlashFragment" app:destination="@id/nav_my_flash" app:popUpTo="@id/nav_flash_record" app:popUpToInclusive="true" app:launchSingleTop="true" />
    </navigation>
    <action android:id="@+id/action_global_flashRecordFragment" app:destination="@id/nav_flash_record" app:enterAnim="@anim/slide_bottom_up" app:popExitAnim="@anim/slide_bottom_down" />
    <fragment android:id="@+id/flashPlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.PublicFlashPlayerFragment" android:label="" tools:layout="@layout/layout_flash_player_item" >
        <action android:id="@+id/action_flashPlayerFragment_to_nav_my_flash" app:destination="@id/nav_my_flash" />
        <argument android:name="tab" app:argType="com.app.messej.data.model.enums.FlashTab" />
        <action android:id="@+id/action_flashPlayerFragment_self" app:destination="@id/flashPlayerFragment" app:popUpTo="@id/flashPlayerFragment" app:popUpToInclusive="true" />
    </fragment>
    <navigation android:id="@+id/nav_my_flash" app:startDestination="@id/myFlashFragment">
        <fragment android:id="@+id/myFlashFragment"
            android:name="com.app.messej.ui.home.publictab.flash.myflash.MyFlashFragment"
            android:label="@string/flash_menu_my_flash"
            tools:layout="@layout/fragment_flash_mine">
            <action android:id="@+id/action_myFlashFragment_to_myFlashPlayerFragment" app:destination="@id/myFlashPlayerFragment" />
        </fragment>
        <fragment android:id="@+id/myFlashPlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.myflash.MyFlashPlayerFragment" android:label="" tools:layout="@layout/layout_flash_player_item" />
        <action android:id="@+id/action_global_nav_flash_archive" app:destination="@id/nav_flash_archive" />
        <action android:id="@+id/action_global_nav_flash_saved" app:destination="@id/nav_flash_saved" />
        <action android:id="@+id/action_global_myFlashDraftFragment" app:destination="@id/myFlashDraftFragment" />
        <action android:id="@+id/action_global_reportedCommentsFragment" app:destination="@id/reportedCommentsFragment" />


    </navigation>
    <navigation android:id="@+id/nav_flash_archive" app:startDestination="@id/myFlashArchiveFragment">
        <fragment android:id="@+id/myFlashArchiveFragment"
            android:name="com.app.messej.ui.home.publictab.flash.myflash.MyFlashArchiveFragment"
            android:label="@string/flash_archived_title"
            tools:layout="@layout/fragment_flash_inner">
            <action android:id="@+id/action_myFlashArchiveFragment_to_myFlashArchivePlayerFragment" app:destination="@id/myFlashArchivePlayerFragment" />
        </fragment>
        <fragment android:id="@+id/myFlashArchivePlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.myflash.MyFlashArchivePlayerFragment" android:label="" tools:layout="@layout/layout_flash_player_item" />
    </navigation>
    <navigation android:id="@+id/nav_flash_saved" app:startDestination="@id/savedFlashFragment">
        <fragment android:id="@+id/savedFlashFragment"
            android:name="com.app.messej.ui.home.publictab.flash.myflash.SavedFlashFragment"
            android:label="@string/flash_saved"
            tools:layout="@layout/fragment_flash_inner">
            <action android:id="@+id/action_savedFlashFragment_to_savedFlashPlayerFragment" app:destination="@id/savedFlashPlayerFragment" />
        </fragment>
        <fragment android:id="@+id/savedFlashPlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.myflash.SavedFlashPlayerFragment" android:label="" tools:layout="@layout/layout_flash_player_item" />
    </navigation>
    <fragment android:id="@+id/myFlashDraftFragment" android:name="com.app.messej.ui.home.publictab.flash.myflash.MyFlashDraftFragment" android:label="@string/flash_drafts" tools:layout="@layout/fragment_flash_inner" />
    <navigation android:id="@+id/nav_flash_user" app:startDestination="@id/userFlashFragment">
        <fragment android:id="@+id/userFlashFragment"
            android:name="com.app.messej.ui.home.publictab.flash.userflash.UserFlashFragment"
            android:label="">
            <argument android:name="userId" app:argType="integer" />
            <action android:id="@+id/action_userFlashFragment_to_userFlashPlayerFragment" app:destination="@id/userFlashPlayerFragment" />
        </fragment>
        <fragment android:id="@+id/userFlashPlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.userflash.UserFlashPlayerFragment" android:label="" tools:layout="@layout/layout_flash_player_item" />
        <argument android:name="userId" app:argType="integer" />
    </navigation>
    <action android:id="@+id/action_global_nav_flash_user" app:destination="@id/nav_flash_user" />
    <dialog
        android:id="@+id/flashCommentFragment"
        tools:layout="@layout/fragment_flash_comment_bottom_sheet"
        android:name="com.app.messej.ui.home.publictab.flash.comments.FlashCommentBottomSheetFragment">
        <argument android:name="flashId" app:argType="string" />
        <argument android:name="flashOwnerId" app:argType="integer" />
        <argument android:name="commentsEnabled" app:argType="boolean" android:defaultValue="true" />
    </dialog>
    <action android:id="@+id/action_global_nav_flash_comment" app:destination="@id/flashCommentFragment" />
    <fragment android:id="@+id/reportedCommentsFragment" android:name="com.app.messej.ui.home.publictab.flash.myflash.ReportedCommentsFragment" android:label="@string/flash_reported_comments" tools:layout="@layout/fragment_flash_reported_comments" />
    <navigation android:id="@+id/nav_flash_search" app:startDestination="@id/flashSearchFragment">
        <fragment android:id="@+id/flashSearchFragment" android:name="com.app.messej.ui.home.publictab.flash.search.FlashSearchFragment" android:label="" >
            <action android:id="@+id/action_flashSearchFragment_to_flashSearchPlayerFragment" app:destination="@id/flashSearchPlayerFragment" />
        </fragment>
        <fragment android:id="@+id/flashSearchPlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.search.FlashSearchPlayerFragment" android:label="" tools:layout="@layout/layout_flash_player_item" />
    </navigation>
    <fragment android:id="@+id/flashSinglePlayerFragment" android:name="com.app.messej.ui.home.publictab.flash.player.FlashSinglePlayerFragment" android:label="FlashSinglePlayerFragment" >
        <argument android:name="flashId" app:argType="string" />
        <argument android:name="commentId" app:argType="string" app:nullable="true" android:defaultValue="@null" />
    </fragment>
    <action android:id="@+id/action_global_flashSinglePlayerFragment" app:destination="@id/flashSinglePlayerFragment" />

    <navigation android:id="@+id/nav_live_podium" app:startDestination="@id/podiumLiveLoaderFragment">
        <argument android:name="podiumId" app:argType="string" />
        <argument android:name="kind" app:argType="integer" android:defaultValue="-1" />
        <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
        <fragment android:id="@+id/podiumLiveLectureFragment" android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveLectureFragment" android:label="" tools:layout="@layout/fragment_podium_live_lecture" >
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumWaitListActionsBottomSheetFragment" app:destination="@id/podiumWaitListActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumSpeakerActionsBottomSheetFragment" app:destination="@id/podiumSpeakerActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumLiveChatActionsBottomSheetFragment" app:destination="@id/podiumLiveChatActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumWaitingListAllFragment" app:destination="@id/podiumWaitingListAllFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumInviteBottomSheetFragment" app:destination="@id/podiumInviteBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumBlockedUsersBottomSheetFragment" app:destination="@id/podiumBlockedUsersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumLiveUsersListBottomSheetFragment" app:destination="@id/podiumLiveUsersListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumGiftChallengeSupportersBottomSheetFragment" app:destination="@id/podiumGiftChallengeSupportersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_challengeFeeStatusBottomSheetFragment" app:destination="@id/challengeFeeStatusBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumChallengeInfoFragment" app:destination="@id/podiumChallengeInfoFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_conFourParticipantStatusBottomSheetFragment" app:destination="@id/conFourParticipantStatusBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_buyCameraBottomSheetFragment" app:destination="@id/podiumBuyCameraBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveLectureFragment_to_yallaGuysListBottomSheetFragment" app:destination="@id/yallaGuysListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveLectureFragment_to_knowledgeRaceSettingsBottomSheetFragment" app:destination="@id/knowledgeRaceSettingsBottomSheetFragment" />

        </fragment>

        <fragment android:id="@+id/chooseMoreContributorsFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.ChooseMoreContributorsFragment"
            android:label="ChooseMoreContributorsFragment"
            tools:layout="@layout/fragment_choose_more_contributors" >
        </fragment>

        <dialog
            android:id="@+id/podiumLiveUsersListBottomSheetFragment"
            tools:layout="@layout/fragment_podium_live_users_list_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumLiveUsersListBottomSheetFragment">
        </dialog>

        <dialog
            android:id="@+id/podiumInviteBottomSheetFragment"
            tools:layout="@layout/fragment_podium_invite"
            android:name="com.app.messej.ui.home.publictab.podiums.create.PodiumInviteBottomSheetFragment">
            <argument android:name="podiumId" app:argType="string" />
            <action android:id="@+id/action_podiumInviteBottomSheetFragment_self" app:destination="@id/podiumInviteBottomSheetFragment" />
        </dialog>

        <dialog
            android:id="@+id/podiumBlockedUsersBottomSheetFragment"
            tools:layout="@layout/fragment_podium_blocked_users"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumRestrictedUsersBottomSheetFragment">
        </dialog>
        <fragment android:id="@+id/podiumWaitingListAllFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumWaitingListAllFragment"
            android:label="@string/podium_waiting_list_title"
            tools:layout="@layout/fragment_podium_waiting_list_all">
        </fragment>
        <fragment android:id="@+id/podiumChallengeInfoFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.ChallengeInfoFragment"
            android:label="@string/podium_challenge_info"
            tools:layout="@layout/fragment_challenge_info">
            <action android:id="@+id/action_podiumChallengeInfoFragment_to_challengeFeeStatusBottomSheetFragment" app:destination="@id/challengeFeeStatusBottomSheetFragment" />
        </fragment>
        <dialog
            android:id="@+id/podiumWaitListActionsBottomSheetFragment"
            tools:layout="@layout/fragment_podium_waitlist_actions_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumWaitListActionsBottomSheetFragment">
            <argument android:name="userId" app:argType="integer" />
            <action android:id="@+id/action_podiumWaitListActionsBottomSheetFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />
        </dialog>
        <dialog
            android:id="@+id/podiumSpeakerActionsBottomSheetFragment"
            tools:layout="@layout/fragment_podium_speaker_actions_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumSpeakerActionsBottomSheetFragment">
            <argument android:name="userId" app:argType="integer" />
            <action android:id="@+id/action_podiumSpeakerActionsBottomSheetFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />
        </dialog>
        <dialog
            android:id="@+id/podiumAdminsBottomSheetFragment"
            tools:layout="@layout/fragment_podium_admins_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumAdminsBottomSheetFragment">
        </dialog>
        <dialog
            android:id="@+id/podiumLiveChatActionsBottomSheetFragment"
            tools:layout="@layout/fragment_podium_live_chat_actions_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumLiveChatActionsBottomSheetFragment">
            <argument android:name="userId" app:argType="integer" />
        </dialog>

        <dialog
            android:id="@+id/challengeFeeStatusBottomSheetFragment"
            tools:layout="@layout/fragment_challenge_fee_status_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.ChallengeFeeStatusBottomSheetFragment">
            <action android:id="@+id/action_challengeFeeStatusBottomSheetFragment_to_chooseMoreContributorsFragment" app:destination="@id/chooseMoreContributorsFragment" />
        </dialog>
        <dialog
            android:id="@+id/podiumGiftChallengeSupportersBottomSheetFragment"
            tools:layout="@layout/fragment_podium_gift_challenge_supportes_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.gift.PodiumGiftChallengesSupportersBottomSheetFragment">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="challengeId" app:argType="string" />
        </dialog>

        <dialog android:id="@+id/topSupportersBottomSheetFragment"
            android:name="com.app.messej.ui.home.publictab.maidan.TopSupportersBottomSheetFragment"
            tools:layout="@layout/fragment_top_supporters_bottom_sheet"
            android:label="">
            <argument android:name="podium_id" app:argType="string" />
            <argument android:name="challenge_id" app:argType="string" />
        </dialog>
        <dialog
            android:id="@+id/conFourParticipantStatusBottomSheetFragment"
            tools:layout="@layout/fragment_challenge_confour_participant_status_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumChallengeParticipantStatusBottomSheetFragment">
        </dialog>
        <dialog
            android:id="@+id/podiumBuyCameraBottomSheetFragment"
            tools:layout="@layout/fragment_podium_buy_camera_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumBuyCameraBottomSheetFragment">
            <argument android:name="buyAction" app:argType="boolean" />
        </dialog>
        <fragment android:id="@+id/podiumLiveLoaderFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveLoaderFragment"
            android:label=""
            tools:layout="@layout/fragment_podium_live_loader">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="kind" app:argType="integer" android:defaultValue="-1" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveLoaderFragment_to_podiumLiveAloneFragment" app:destination="@id/podiumLiveAloneFragment" />
            <action android:id="@+id/action_podiumLiveLoaderFragment_to_podiumLiveInterviewFragment" app:destination="@id/podiumLiveInterviewFragment" />
            <action android:id="@+id/action_podiumLiveLoaderFragment_to_podiumLiveMaidanFragment" app:destination="@id/podiumLiveMaidanFragment" />
            <action android:id="@+id/action_podiumLiveLoaderFragment_to_podiumLiveAssemblyFragment" app:destination="@id/podiumLiveAssemblyFragment" />
            <action android:id="@+id/action_podiumLiveLoaderFragment_to_podiumLiveLectureFragment" app:destination="@id/podiumLiveLectureFragment" />
            <action android:id="@+id/action_podiumLiveLoaderFragment_to_podiumLiveTheaterFragment" app:destination="@id/podiumLiveTheaterFragment" />

        </fragment>
        <fragment android:id="@+id/podiumLiveAloneFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveAloneFragment"
            android:label="PodiumLiveAloneFragment"
            tools:layout="@layout/fragment_podium_live_alone">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveAloneFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAloneFragment_to_podiumBlockedUsersBottomSheetFragment" app:destination="@id/podiumBlockedUsersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAloneFragment_to_podiumLiveChatActionsBottomSheetFragment" app:destination="@id/podiumLiveChatActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAloneFragment_to_podiumBuyCameraBottomSheetFragment" app:destination="@id/podiumBuyCameraBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAloneFragment_to_podiumLiveUsersListBottomSheetFragment" app:destination="@id/podiumLiveUsersListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAloneFragment_to_podiumSpeakerActionsBottomSheetFragment" app:destination="@id/podiumSpeakerActionsBottomSheetFragment" />
        </fragment>

        <fragment android:id="@+id/podiumLiveAssemblyFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveAssemblyFragment"
            android:label="PodiumLiveAssemblyFragment"
            tools:layout="@layout/fragment_podium_live_assembly">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumSpeakerActionsBottomSheetFragment" app:destination="@id/podiumSpeakerActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumLiveChatActionsBottomSheetFragment" app:destination="@id/podiumLiveChatActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumLiveUsersListBottomSheetFragment" app:destination="@id/podiumLiveUsersListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumBlockedUsersBottomSheetFragment" app:destination="@id/podiumBlockedUsersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumInviteBottomSheetFragment" app:destination="@id/podiumInviteBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveAssemblyFragment_to_podiumBuyCameraBottomSheetFragment" app:destination="@id/podiumBuyCameraBottomSheetFragment" />
        </fragment>

        <fragment android:id="@+id/podiumLiveInterviewFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveInterviewFragment"
            android:label="PodiumLiveInterviewFragment"
            tools:layout="@layout/fragment_podium_live_interview">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumBlockedUsersBottomSheetFragment" app:destination="@id/podiumBlockedUsersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumLiveChatActionsBottomSheetFragment" app:destination="@id/podiumLiveChatActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumBuyCameraBottomSheetFragment" app:destination="@id/podiumBuyCameraBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumLiveUsersListBottomSheetFragment" app:destination="@id/podiumLiveUsersListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumSpeakerActionsBottomSheetFragment" app:destination="@id/podiumSpeakerActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveInterviewFragment_to_podiumInviteBottomSheetFragment" app:destination="@id/podiumInviteBottomSheetFragment" />
        </fragment>
        <fragment android:id="@+id/podiumLiveMaidanFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveMaidanFragment"
            tools:layout="@layout/fragment_podium_live_maidan"
            android:label="PodiumLiveMaidanFragment" >
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_topSupportersBottomSheetFragment" app:destination="@id/topSupportersBottomSheetFragment"/>
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_podiumMaidanSpeakerActionsBottomSheetFragment" app:destination="@id/podiumMaidanSpeakerActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_podiumLiveUsersListBottomSheetFragment" app:destination="@id/podiumLiveUsersListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_podiumLiveChatActionsBottomSheetFragment" app:destination="@id/podiumLiveChatActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_podiumBuyCameraBottomSheetFragment" app:destination="@id/podiumBuyCameraBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_podiumBlockedUsersBottomSheetFragment" app:destination="@id/podiumBlockedUsersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveMaidanFragment_to_maidanInviteBottomSheetFragment" app:destination="@id/maidanInviteBottomSheetFragment" />
        </fragment>


        <dialog android:id="@+id/podiumMaidanSpeakerActionsBottomSheetFragment"
            tools:layout="@layout/fragment_podium_maidan_speaker_actions_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumMaidanSpeakerActionsBottomSheetFragment"
            android:label="PodiumMaidanSpeakerActionsBottomSheetFragment">
            <argument android:name="userId" app:argType="integer" />
        </dialog>
        <dialog android:id="@+id/maidanInviteBottomSheetFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.MaidanInviteBottomSheetFragment"
            android:label="MaidanInviteBottomSheetFragment"
            tools:layout="@layout/fragment_create_maidan"/>
        <fragment android:id="@+id/podiumLiveTheaterFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveTheaterFragment"
            android:label="">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="enableScrollForTab" app:argType="integer" android:defaultValue="-1" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumSpeakerActionsBottomSheetFragment" app:destination="@id/podiumSpeakerActionsBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumBuyCameraBottomSheetFragment" app:destination="@id/podiumBuyCameraBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumLiveUsersListBottomSheetFragment" app:destination="@id/podiumLiveUsersListBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumInviteBottomSheetFragment" app:destination="@id/podiumInviteBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumBlockedUsersBottomSheetFragment" app:destination="@id/podiumBlockedUsersBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumTheaterLikesBottomSheetFragment" app:destination="@id/podiumTheaterLikesBottomSheetFragment" />
            <action android:id="@+id/action_podiumLiveTheaterFragment_to_podiumAdminsBottomSheetFragment" app:destination="@id/podiumAdminsBottomSheetFragment" />

        </fragment>
        <fragment android:id="@+id/advancedSettingsFragment" android:name="com.app.messej.ui.home.publictab.podiums.create.AdvancedSettingsFragment"
            android:label="@string/podium_title_advanced_settings">
            <argument
                android:name="podiumId"
                app:argType="string" />
        </fragment>


        <dialog android:id="@+id/yallaGuysListBottomSheetFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.yalla.YallaGuysListBottomSheetFragment"
            tools:layout="@layout/fragment_podium_yalla_bottom_sheet" >
            <argument
                android:name="podiumId"
                app:argType="string" />
        </dialog>
        <action android:id="@+id/action_global_advancedSettingsFragment" app:destination="@id/advancedSettingsFragment" />
        <action android:id="@+id/action_global_podiumChargesBottomSheetFragment" app:destination="@id/podiumTheaterChargesBottomSheetFragment"/>
        <dialog
            android:id="@+id/knowledgeRaceSettingsBottomSheetFragment"
            tools:layout="@layout/fragment_knowledge_race_settings_bottom_sheet"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.knowledgerace.KnowledgeRaceSettingsBottomSheetFragment">
        </dialog>
    </navigation>

    <fragment android:id="@+id/YallaGuysCreateFragment"
        android:name="com.app.messej.ui.home.publictab.podiums.yalla.YallaGuysCreateFragment"
        android:label="@string/podium_yalla_guys_create_new">
        <argument android:name="podiumId" app:argType="string" />
        <argument android:name="fromLive" app:argType="boolean" android:defaultValue="true" />
        <argument android:name="joiningFee" app:argType="integer" android:defaultValue="0" />
        <argument android:name="podiumRating" app:argType="integer" android:defaultValue="0" />
        <argument android:name="isAdmin" app:argType="boolean" android:defaultValue="false"/>
    </fragment>

    <action android:id="@+id/action_global_nav_challenge_setup" app:destination="@id/nav_challenge_setup"/>

    <navigation android:id="@+id/nav_challenge_setup" app:startDestination="@id/createChallengeLoadingFragment">
        <argument android:name="podiumId" app:argType="string" />
        <argument android:name="challengeId" app:argType="string" app:nullable="true" />
        <argument android:name="invitedParticipants" app:argType="string" app:nullable="true" android:defaultValue="null" />

        <fragment
            android:id="@+id/createChallengeLoadingFragment"
            tools:layout="@layout/fragment_create_challenge_loading"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.CreateChallengeLoadingFragment">
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="challengeId" app:argType="string" app:nullable="true" />
            <argument android:name="invitedParticipants" app:argType="string" app:nullable="true" />
            <action android:id="@+id/action_createChallengeLoadingFragment_to_challengeFacilitatorListFragment" app:destination="@id/challengeFacilitatorListFragment" />
            <action android:id="@+id/action_createChallengeLoadingFragment_to_challengeTimerFragment" app:destination="@id/challengeTimerFragment" />
            <action android:id="@+id/action_createChallengeLoadingFragment_to_podiumChallengeListFragment" app:destination="@id/podiumChallengeListFragment" />
            <action android:id="@+id/action_createChallengeLoadingFragment_to_podiumChallengePrizeContributorFragment" app:destination="@id/podiumChallengePrizeContributorFragment" />
            <action android:id="@+id/action_createChallengeLoadingFragment_to_podiumConFourParticipants" app:destination="@id/podiumConFourChallengeParticipantFragment" />
        </fragment>

        <fragment
            android:id="@+id/podiumChallengeListFragment"
            tools:layout="@layout/fragment_podium_challenges_list"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumChallengesListFragment">
            <action android:id="@+id/action_podiumChallengeListFragment_to_challengeFacilitatorListFragment" app:destination="@id/challengeFacilitatorListFragment" />
            <action android:id="@+id/action_podiumChallengeListFragment_to_podiumChallengePrizeContributorFragment" app:destination="@id/podiumChallengePrizeContributorFragment" />
            <action android:id="@+id/action_podiumChallengeListFragment_to_challengeTimerFragment" app:destination="@id/challengeTimerFragment" />
            <action android:id="@+id/action_createChallengeLoadingFragment_to_podiumChallengeDetailsBottomSheetFragment" app:destination="@id/podiumChallengeDetailsBottomSheetFragment" />
        </fragment>

        <fragment
            android:id="@+id/podiumChallengePrizeContributorFragment"
            tools:layout="@layout/fragment_podium_prize_contribution"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.ChallengePrizeContributionFragment">
        </fragment>
        <fragment android:id="@+id/challengeFacilitatorListFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.ChallengeFacilitatorListFragment"
            android:label="@string/podium_challenge_facilitator_title"
            tools:layout="@layout/fragment_challenge_facilitator_list">
            <action android:id="@+id/action_podiumLiveFragment_to_challengeTimerFragment" app:destination="@id/challengeTimerFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumChallengePrizeContributorFragment" app:destination="@id/podiumChallengePrizeContributorFragment" />
            <action android:id="@+id/action_podiumLiveFragment_to_podiumConFourChallengeParticipantFragment" app:destination="@id/podiumConFourChallengeParticipantFragment" />
        </fragment>
        <fragment android:id="@+id/challengeTimerFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.ChallengeTimerFragment"
            android:label="@string/podium_challenge_timer_title"
            tools:layout="@layout/fragment_challenge_timer">
            <action android:id="@+id/action_challengeTimerFragment_to_podiumChallengePrizeContributorFragment" app:destination="@id/podiumChallengePrizeContributorFragment" />
        </fragment>
        <dialog android:id="@+id/podiumChallengeDetailsBottomSheetFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumChallengeDetailsBottomSheetFragment"
            tools:layout="@layout/fragment_podium_challenge_details">
            <argument android:name="document_type" app:argType="com.app.messej.data.model.enums.DocumentType" />
        </dialog>
        <fragment android:id="@+id/podiumConFourChallengeParticipantFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumChallengeParticipantFragment"
            android:label="PodiumConFourChooseMoreParticipantFragment"
            tools:layout="@layout/fragment_choose_more_contributors" >
            <action android:id="@+id/action_podiumConFourChallengeParticipantFragment_to_chooseMoreParticipantFragment" app:destination="@id/chooseMoreConFourFragment" />
        </fragment>
        <fragment android:id="@+id/chooseMoreConFourFragment"
            android:name="com.app.messej.ui.home.publictab.podiums.challenges.create.PodiumChallengeChooseMoreParticipantFragment"
            android:label="ChooseMoreContributorsFragment"
            tools:layout="@layout/fragment_choose_more_contributors" >
            <argument android:name="podiumId" app:argType="string" />
            <argument android:name="challengeId" app:argType="string" app:nullable="true" />
            <argument android:name="startUpFlow" app:argType="boolean" android:defaultValue="true" />
        </fragment>
    </navigation>

    <dialog
        android:id="@+id/podiumAboutBottomSheetFragment"
        tools:layout="@layout/fragment_podium_about_bottom_sheet"
        android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumAboutBottomSheetFragment">
        <argument android:name="podiumId" app:argType="string" />
        <argument android:name="elevated" app:argType="boolean" android:defaultValue="false" />
        <argument android:name="compacted" app:argType="boolean" android:defaultValue="false" />
    </dialog>

    <dialog
        android:id="@+id/podiumPublicAdminBottomSheetFragment"
        tools:layout="@layout/fragment_podium_admins_bottom_sheet"
        android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumAdminsPublicBottomSheetFragment">
        <argument android:name="podiumId" app:argType="string" />
        <argument android:name="kebabAction" app:argType="boolean" android:defaultValue="false"/>
    </dialog>
    <dialog
        android:id="@+id/podiumPublicLiveUsersListBottomSheetFragment"
        tools:layout="@layout/fragment_podium_live_users_list_bottom_sheet"
        android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumLiveUsersListPublicBottomSheetFragment">
        <argument android:name="podiumId" app:argType="string" />
    </dialog>



    <action android:id="@+id/action_global_nav_about_podium" app:destination="@id/podiumAboutBottomSheetFragment" />
    <action android:id="@+id/action_global_public_admin_bottom_sheet" app:destination="@id/podiumPublicAdminBottomSheetFragment" />
    <action android:id="@+id/action_global_public_live_users_list_bottom_sheet" app:destination="@id/podiumPublicLiveUsersListBottomSheetFragment" />
    <action android:id="@+id/action_global_nav_live_podium" app:destination="@id/nav_live_podium"/>
    <action android:id="@+id/action_global_createPodiumFragment" app:destination="@id/createPodiumFragment"/>
    <fragment android:id="@+id/podiumSearchFragment" android:name="com.app.messej.ui.home.publictab.podiums.PodiumSearchFragment" tools:layout="@layout/fragment_podium_search" >
        <argument android:name="tab" app:argType="com.app.messej.data.model.enums.PodiumTab" />
    </fragment>

    <fragment
        android:id="@+id/dealsStandAloneFragment"
        android:name="com.app.messej.ui.home.businesstab.BusinessDealsStandaloneFragment"
        tools:layout="@layout/fragment_business_deals_standalone">
        <argument android:name="isResident" app:argType="boolean" android:defaultValue="false" />
        <argument android:name="isVisitor" app:argType="boolean" android:defaultValue="false" />
    </fragment>

    <fragment
        android:id="@+id/businessStatementFragmentFree"
        android:name="com.app.messej.ui.home.businesstab.BusinessStatementFreeFragment"
        tools:layout="@layout/fragment_business_statement_free">
        <argument android:name="isPremium" app:argType="boolean" android:defaultValue="false" />
    </fragment>
    <action android:id="@+id/action_globalFlaxPurchaseHistoryFragment" app:destination="@id/flaxPurchaseHistoryFragment" />
    <fragment android:id="@+id/flaxPurchaseHistoryFragment"
        android:name="com.app.messej.ui.home.gift.CoinPurchaseHistoryFragment"
        android:label="@string/title_flax_purchase_history"
        tools:layout="@layout/fragment_flax_purchase_history">
        <argument android:name="purchasetype" app:argType="com.app.messej.data.model.enums.PurchaseItem" />
    </fragment>
    <action android:id="@+id/action_globalFlixPurchaseHistoryFragment" app:destination="@id/flixPurchaseHistoryFragment" />
    <fragment android:id="@+id/flixPurchaseHistoryFragment"
        android:name="com.app.messej.ui.home.gift.FlixPurchaseHistoryFragment"
        android:label="@string/title_flax_purchase_history"
        tools:layout="@layout/fragment_flix_purchase_history">
        <argument android:name="purchasetype" app:argType="com.app.messej.data.model.enums.PurchaseItem" />
    </fragment>
    <fragment android:id="@+id/businessCustomerInformationFragment"
        android:name="com.app.messej.ui.home.businesstab.BusinessCustomerInformationFragment"
        android:label="BusinessCustomerInformationFragment" >
        <action android:id="@+id/action_businessCustomerInformationFragment_to_payoutOtpFragment" app:destination="@id/payoutOtpFragment" />
        <action android:id="@+id/action_businessCustomerInformationFragment_to_paymentMethodsFragment" app:destination="@id/paymentMethodsFragment" />
    </fragment>
    <action android:id="@+id/action_global_businessCustomerInformationFragment" app:destination="@id/businessCustomerInformationFragment" />
    <dialog android:id="@+id/businessPointsReviewDialogFragment"
        android:name="com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessPointsReviewDialogFragment"
        android:label="BusinessPointsReviewDialogFragment" >
        <action android:id="@+id/action_businessOperationDialogueToWithDrawFragment" app:destination="@id/businessPointsWithDrawFragment" />
        <action android:id="@+id/action_businessPointsReviewDialogFragment_to_businessWorkStatusStandAloneFragment" app:destination="@id/businessWorkStandAloneFragment" />
    </dialog>
    <dialog android:id="@+id/giftAlertVideoBottomSheetFragment" android:name="com.app.messej.ui.home.gift.GiftAlertVideoBottomSheetFragment" android:label="GiftAlertVideoBottomSheetFragment" /><action android:id="@+id/action_global_giftAlertVideoBottomSheetFragment" app:destination="@id/giftAlertVideoBottomSheetFragment"/>

    <fragment android:id="@+id/businessPointsWithDrawFragment"
        android:name="com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawFlaxFragment"
        android:label="BusinessPointsReviewFragment" />
    <action android:id="@+id/action_global_businessPointsReviewDialogFragment" app:destination="@id/businessPointsReviewDialogFragment" />
    <dialog android:id="@+id/payoutOtpFragment" android:name="com.app.messej.ui.home.businesstab.PayoutOtpFragment" android:label="PayoutOtpFragment" >
        <action android:id="@+id/action_payoutOtpFragment_to_paymentMethodsFragment" app:destination="@id/paymentMethodsFragment" />
    </dialog>
    <fragment android:id="@+id/dynamicFormFragment" android:name="com.app.messej.ui.home.businesstab.DynamicFormFragment" android:label="DynamicFormFragment" >
        <argument android:name="payment_method" app:argType="string" app:nullable="true" />
        <argument android:name="payment_method_id" app:argType="integer" />
        <argument
            android:name="isEgyptianPaymentMethod"
            app:argType="boolean"
            android:defaultValue="false" />
        <action android:id="@+id/action_dynamicFormFragment_to_dealsStandAloneFragment" app:destination="@id/dealsStandAloneFragment" />
        <action android:id="@+id/action_dynamicFormFragment_to_successSellFlixAlertFragment" app:destination="@id/successSellFlixAlertFragment" />
    </fragment>
    <fragment android:id="@+id/paymentMethodsFragment"
        android:name="com.app.messej.ui.home.businesstab.operations.PaymentMethodsFragment"
        android:label="fragment_payment_methods"
        tools:layout="@layout/fragment_payment_methods" >
        <action android:id="@+id/action_paymentMethodsFragment_to_dynamicFormFragment" app:destination="@id/dynamicFormFragment" />
    </fragment>
    <fragment android:id="@+id/flaxSellingConfirmationFragment" android:name="com.app.messej.ui.home.businesstab.FlaxSellingConfirmationFragment" android:label="FlaxSellingConfirmationFragment" />
    <action android:id="@+id/action_global_flaxSellingConfirmationFragment" app:destination="@id/flaxSellingConfirmationFragment" />
    <dialog android:id="@+id/payoutCancelDialogFragment" android:name="com.app.messej.ui.home.businesstab.PayoutCancelDialogFragment" android:label="PayoutCancelDialogFragment" /><action android:id="@+id/action_global_payoutCancelDialogFragment" app:destination="@id/payoutCancelDialogFragment"/>
    <dialog android:id="@+id/successSellFlixAlertFragment"
        android:name="com.app.messej.ui.home.businesstab.SuccessSellFlixAlertFragment"
        android:label="fragment_success_sell_flix_alert"
        tools:layout="@layout/fragment_success_sell_flix_alert" >
        <argument android:name="payoutDate" app:argType="string" />
        <argument android:name="paymentMethod" app:argType="string" />
        <action android:id="@+id/action_successSellFlixAlertFragment_to_dealsStandAloneFragment" app:destination="@id/dealsStandAloneFragment" />
    </dialog>
    <dialog android:id="@+id/postatCommentBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.postat.comments.PostatCommentBottomSheetFragment"
        android:label="PostatCommentBottomSheetFragment" >
        <argument android:name="post_id" app:argType="string" />
        <argument android:name="post_owner_id" app:argType="integer" />
    </dialog>
    <action android:id="@+id/action_global_postatCommentBottomSheetFragment" app:destination="@id/postatCommentBottomSheetFragment"/>


    <navigation android:id="@+id/nav_create_postat" app:startDestination="@id/postatMediaPickerFragment">
        <argument android:name="postat_id" app:argType="string" app:nullable="true"/>
        <fragment android:id="@+id/postatMediaPickerFragment" android:name="com.app.messej.ui.home.publictab.postat.create.PostatMediaPickerFragment"
            tools:layout="@layout/fragment_postat_media_picker">
            <argument android:name="postat_id" app:argType="string" app:nullable="true"/>
            <action android:id="@+id/action_postatMediaPickerFragment_to_createPostatFragment" app:destination="@id/createPostatFragment" />
        </fragment>
        <fragment android:id="@+id/createPostatFragment" android:name="com.app.messej.ui.home.publictab.postat.create.CreatePostatFragment"
            tools:layout="@layout/fragment_create_postat">
            <action android:id="@+id/action_createPostatFragment_to_postatAudioListFragment" app:destination="@id/postatAudioListFragment" />
            <action android:id="@+id/action_createPostatFragment_to_postatMentionListFragment" app:destination="@id/postatMentionListFragment" />
        </fragment>
        <fragment android:id="@+id/postatAudioListFragment" android:name="com.app.messej.ui.home.publictab.postat.create.PostatAudioListFragment"
            tools:layout="@layout/fragment_postat_audio_list">
        </fragment>
        <fragment android:id="@+id/postatMentionListFragment"
            android:name="com.app.messej.ui.home.publictab.postat.create.PostatMentionListFragment"
            android:label="PostatMentionListFragment"
            tools:layout="@layout/fragment_postat_audio_list"/>
        <action android:id="@+id/action_global_createPostatFragment" app:destination="@id/createPostatFragment"/>
    </navigation>
    <fragment android:id="@+id/postatIgnoredUsersFragment" android:name="com.app.messej.ui.home.publictab.podiums.PostatIgnoredUsersFragment" android:label="PostatIgnoredUsersFragment" />
    <fragment android:id="@+id/myPostatFeedFragment"
        android:name="com.app.messej.ui.home.publictab.postat.mypostat.MyPostatFeedFragment"
        android:label="@string/title_my_postats" />
    <action android:id="@+id/action_global_create_postat" app:destination="@id/nav_create_postat"/>
    <fragment android:id="@+id/publicPostatStandaloneFragment" android:name="com.app.messej.ui.home.publictab.postat.PublicPostatStandaloneFragment" android:label="PublicPostatStandaloneFragment" >
        <action android:id="@+id/action_publicPostatStandaloneFragment_to_myPostatFeedFragment" app:destination="@id/myPostatFeedFragment" />
        <action android:id="@+id/action_publicPostatStandaloneFragment_to_userPostatFeedFragment" app:destination="@id/userPostatFeedFragment" />
    </fragment><action android:id="@+id/action_global_publicPostatStandaloneFragment" app:destination="@id/publicPostatStandaloneFragment"/>
    <dialog android:id="@+id/postatGiftSendFragment" android:name="com.app.messej.ui.home.publictab.postat.PostatGiftSendFragment" android:label="PostatGiftSendFragment">
        <argument android:name="receiverId" app:argType="integer"  />
        <argument android:name="singleTabItem" app:argType="com.app.messej.data.model.enums.GiftContext"/>
        <argument android:name="messageId" app:argType="string" app:nullable="true" android:defaultValue=" " />
    </dialog><action android:id="@+id/action_global_postatGiftSendFragment" app:destination="@id/postatGiftSendFragment"/>
    <action android:id="@+id/action_global_BirthdayNotificationBottomSheetFragment" app:destination="@id/BirthdayNotificationBottomSheetFragment" />
    <action android:id="@+id/action_global_BirthdayAlertBottomSheetFragment" app:destination="@id/BirthdayAlertBottomSheetFragment" />
    <fragment android:id="@+id/userPostatFeedFragment" android:name="com.app.messej.ui.home.publictab.postat.UserPostatFeedFragment" android:label="PublicPostatUserListFragment" >
        <argument android:name="userId" app:argType="integer" />
        <action android:id="@+id/action_userPostatFeedFragment_to_postatIgnoredUsersFragment" app:destination="@id/postatIgnoredUsersFragment" />
    </fragment>
    <action android:id="@+id/action_global_internalShareFragment" app:destination="@id/internalShareFragment"/>
    <dialog android:id="@+id/levelUpgradationBottomSheetFragment"
        android:name="com.app.messej.ui.home.gift.bottomSheet.LevelUpgradationBottomSheetFragment"
        android:label="LevelUpgradationBottomSheetFragment" >
        <argument android:name="userId" app:argType="integer" />
        <action android:id="@+id/action_userLevelUpgradeFragment_to_giftListFragment" app:destination="@id/giftListFragment"/>
    </dialog><action android:id="@+id/action_global_levelUpgradationBottomSheetFragment" app:destination="@id/levelUpgradationBottomSheetFragment"/>
    <fragment android:id="@+id/publicMaidanFragment" android:name="com.app.messej.ui.home.publictab.maidan.PublicMaidanFragment" android:label="@string/podium_challenge_maidan" >
        <action android:id="@+id/action_publicMaidanFragment_to_podiumMaidanChallengeHistoryFragment" app:destination="@id/podiumMaidanChallengeHistoryFragment" />
        <action android:id="@+id/action_publicMaidanFragment_to_podiumMaidanCompetitorStatsFragment" app:destination="@id/podiumMaidanCompetitorStatsFragment" />

    </fragment><action android:id="@+id/action_global_publicMaidanFragment" app:destination="@id/publicMaidanFragment"/>
    <dialog android:id="@+id/podiumMaidanChallengeBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.maidan.PodiumMaidanChallengeBottomSheetFragment"
        android:label="PodiumMaidanChallengeBottomSheetFragment" >
        <argument android:name="podium_id" app:argType="string" />
        <argument android:name="fromPodium" app:argType="boolean" android:defaultValue="false" />
    </dialog><action android:id="@+id/action_global_flaxToPointsConvertDialogFragment" app:destination="@id/flaxToPointsConvertDialogFragment"/>
    <dialog android:id="@+id/podiumMaidanSupportBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.maidan.PodiumMaidanSupportBottomSheetFragment"
        android:label="PodiumMaidanSupportBottomSheetFragment" >
        <argument android:name="userId" app:argType="integer" />
        <argument android:name="name" app:argType="string" />
        <argument android:name="podiumId" app:argType="string" />
    </dialog>

    <fragment android:id="@+id/podiumMaidanChallengeHistoryFragment"
        android:name="com.app.messej.ui.home.publictab.maidan.PodiumMaidanChallengeHistoryFragment"
        android:label="@string/podium_maidan_challenge_history" />
    <fragment android:id="@+id/podiumMaidanCompetitorStatsFragment"
        android:name="com.app.messej.ui.home.publictab.maidan.PodiumMaidanCompetitorStatsFragment"
        android:label="@string/podium_maidan_competitor_stats" />
    />
    <action android:id="@+id/action_global_podiumMaidanSupportBottomSheetFragment" app:destination="@id/podiumMaidanSupportBottomSheetFragment" app:launchSingleTop="true" />

    <dialog
        android:id="@+id/SelectSubscribedTypeFragment"
        android:name="com.app.messej.ui.premium.SelectSubscriptionTypeFragment"
        android:label="fragment_select_subscription_type"
        tools:layout="@layout/fragment_select_subscription_type">
        <action android:id="@+id/action_SelectSubscribedTypeFragment_to_alreadySubscribedFragment" app:destination="@id/alreadySubscribedFragment"
            app:popUpTo="@id/upgradePremiumFragment" app:popUpToInclusive="true" />
    </dialog>
    <action android:id="@+id/action_global_podiumMaidanChallengeBottomSheetFragment" app:destination="@id/podiumMaidanChallengeBottomSheetFragment"/>
    <action android:id="@+id/action_global_SelectSubcriptionTypeFragment" app:destination="@id/SelectSubscribedTypeFragment" />
    <fragment android:id="@+id/empowermentListsFragment" android:name="com.app.messej.ui.home.settings.empowerments.EmpowermentListsFragment" android:label="EmpowermentListsFragment" >
        <action android:id="@+id/action_empowermentListsFragment_to_empowerBlockedListFragment" app:destination="@id/empowerBlockedListFragment" />
    </fragment>
    <fragment android:id="@+id/empowerBlockedListFragment" android:name="com.app.messej.ui.home.settings.empowerments.EmpowerBlockedListFragment" android:label="EmpowerBlockedListFragment" >
        <argument android:name="blocked_item" app:argType="com.app.messej.data.model.enums.EmpoweredBlockListType" />
    </fragment>
    <dialog android:id="@+id/podiumTheaterChargesBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumTheaterChargesBottomSheetFragment"
        android:label="PodiumTheaterChargesBottomSheetFragment" >
        <argument android:name="podiumId" app:argType="string"/>
        <argument android:name="chargeType" app:argType="com.app.messej.data.model.enums.TheaterCharge"/>

    </dialog>

    <dialog android:id="@+id/podiumTheaterLikesBottomSheetFragment"
        android:name="com.app.messej.ui.home.publictab.podiums.manage.PodiumTheaterLikesBottomSheetFragment"
        android:label="fragment_podium_theater_likes_bottom_sheet"
        tools:layout="@layout/fragment_podium_theater_likes_bottom_sheet">
        <argument android:name="podiumId" app:argType="string" />
    </dialog>
    <action android:id="@+id/action_global_reportToManagerFragment" app:destination="@id/reportToManagerFragment"/>

    <fragment
        android:id="@+id/authoritiesPremiumFragment"
        android:name="com.app.messej.ui.home.publictab.authorities.AuthoritiesPremiumFragment"
        android:label="">
        <action android:id="@+id/action_authoritiesPremiumFragment_to_socialHomeFragment" app:destination="@id/socialAffairHomeFragment" />
<!--        <action android:id="@+id/action_authoritiesPremiumFragment_to_nav_graph_legal_affairs" app:destination="@id/legalAffairsFragment" />-->
    </fragment>

    <fragment
        android:id="@+id/authoritiesStandAloneFragment"
        android:name="com.app.messej.ui.home.publictab.authorities.AuthoritiesStandAloneFragment"
        android:label="">
<!--        <action android:id="@+id/action_authoritiesStandAloneFragment_to_nav_graph_legal_affairs" app:destination="@id/legalAffairsFragment" />-->
    </fragment>

    <action android:id="@+id/action_global_legalAffairsFragment" app:destination="@id/legalAffairsFragment"/>

    <fragment
        android:id="@+id/legalAffairsFragment"
        android:name="com.app.messej.ui.home.publictab.authorities.legalAffairs.LegalAffairsFragment"
        android:label="">
        <action android:id="@+id/action_legalAffairsFragment_to_caseDetailBottomSheet" app:destination="@id/caseDetailBottomSheet" />
        <action android:id="@+id/action_legalAffairsFragment_to_legalAffairsFileCaseFragment" app:destination="@id/legalAffairsFileCaseFragment" />
        <argument android:name="defaultMainTab" app:argType="com.app.messej.data.model.enums.LegalAffairsMainTab" android:defaultValue="MyLegalRecords" />
        <argument android:name="defaultViolationSubTab" app:argType="com.app.messej.data.model.enums.LegalAffairsViolationSubTab" android:defaultValue="Open" />
        <argument android:name="defaultAdvocateFilter" app:argType="com.app.messej.data.model.enums.AdvocatesUnionFilter" android:defaultValue="InJury" />
        <argument android:name="defaultMyLegalRecordTab" app:argType="com.app.messej.data.model.enums.LegalAffairTabs" android:defaultValue="Violations" />
    </fragment>

    <fragment
        android:id="@+id/defendCaseFragment"
        android:name="com.app.messej.ui.home.publictab.authorities.legalAffairs.defendCase.DefendCaseFragment"
        android:label="">
        <argument android:name="caseId" app:argType="integer"/>
    </fragment>

    <fragment
        android:id="@+id/legalAffairsFileCaseFragment"
        android:name="com.app.messej.ui.home.publictab.authorities.legalAffairs.fileCase.FileCaseFragment"
        android:label=""/>

    <dialog android:id="@+id/caseDetailBottomSheet"
        android:name="com.app.messej.ui.home.publictab.authorities.legalAffairs.CaseDetailsBottomSheet"
        android:label=""
        tools:layout="@layout/fragment_case_details_bottom_sheet">
        <argument android:name="caseID" app:argType="integer" />
        <action android:id="@+id/action_caseDetailBottomSheet_to_defendCaseFragment" app:destination="@id/defendCaseFragment" />
    </dialog><action android:id="@+id/action_global_caseDetailBottomSheet" app:destination="@id/caseDetailBottomSheet"/><action android:id="@+id/action_global_YallaGuysCreateFragment" app:destination="@id/YallaGuysCreateFragment"/><action android:id="@+id/action_global_myPostatFeedFragment" app:destination="@id/myPostatFeedFragment"/>
    <dialog android:id="@+id/upgradeUserLevelFragment" android:name="com.app.messej.ui.home.settings.levels.upgradeLevel.UpgradeUserLevelFragment" android:label="UpgradeUserLevelFragment" />
    <fragment android:id="@+id/upgradeGoldenFragment" android:name="com.app.messej.ui.premium.UpgradeGoldenFragment" android:label="UpgradeGoldenFragment" >
        <action android:id="@+id/action_upgradeGoldenFragment_to_subscribeGoldenBottomSheetFragment" app:destination="@id/subscribeGoldenBottomSheetFragment" />
        <argument android:name="isRenew" app:argType="boolean" android:defaultValue="false" />
    </fragment>
    <fragment android:id="@+id/alreadySubscribedGoldenFragment" android:name="com.app.messej.ui.premium.AlreadySubscribedGoldenFragment" android:label="AlreadySubscribedGoldenFragment">
        <action android:id="@+id/action_alreadySubscribedGoldenFragment_to_subscribeGoldenBottomSheetFragment" app:destination="@id/subscribeGoldenBottomSheetFragment" />
        <argument android:name="isRenew" app:argType="boolean" android:defaultValue="false" />
    </fragment>
    <dialog android:id="@+id/subscribeGoldenBottomSheetFragment" android:name="com.app.messej.ui.premium.SubscribeGoldenBottomSheetFragment" android:label="SubscribeGoldenBottomSheetFragment">
        <argument android:name="isRenew" app:argType="boolean" android:defaultValue="false" />
    </dialog>

</navigation>