<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!--  Text Button  -->
    <style name="Widget.Flashat.TextButton" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Button</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
        <item name="iconSize">16dp</item>
    </style>
    <style name="Widget.Flashat.TextButton.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.TextButton.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.TextButton.Small">
        <item name="android:minHeight">36dp</item>
    </style>
    <style name="Widget.Flashat.TextButton.Small.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.TextButton.Negative.Small">
        <item name="android:minHeight">36dp</item>
    </style>

    <style name="Widget.Flashat.TextButton.Link">
        <item name="android:paddingHorizontal">4dp</item>
        <item name="textAllCaps">false</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.TextButton.Link</item>
    </style>

    <style name="TextAppearance.Flashat.TextButton.Link" parent="TextAppearance.Flashat.Body2">
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Widget.Flashat.TextButton.Link.Small">
        <item name="android:minHeight">36dp</item>
    </style>


    <!--  Large Rounded Button  -->

    <style name="Widget.Flashat.LargeRoundedButton" parent="Widget.MaterialComponents.Button">
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.LargeRoundedButton</item>
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">64dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
    </style>

    <style name="ShapeAppearance.Flashat.LargeRoundButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">32dp</item>
    </style>

    <style name="Widget.Flashat.LargeRoundButton" parent="Widget.Flashat.LargeRoundedButton">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.LargeRoundButton</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.LargeRoundButton.Primary" parent="Widget.Flashat.LargeRoundedButton.Primary">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.LargeRoundButton</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Secondary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <style name="Widget.Flashat.LargeRoundButton.Secondary" parent="Widget.Flashat.LargeRoundedButton.Secondary">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.LargeRoundButton</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Outline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.LargeRoundedButton</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">64dp</item>
        <item name="strokeColor">@color/outlined_button_stroke_selector</item>
        <item name="strokeWidth">1dp</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Outline.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Outline.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Outline.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.LargeRoundedButton.Outline.Inverse.OnPrimary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse.OnPrimary</item>
    </style>

    <!-- Medium Rounded Button  -->

    <style name="Widget.Flashat.MediumRoundedButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Button</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingBottom">6dp</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="iconSize">22dp</item>
        <item name="iconGravity">textStart</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
    </style>

    <style name="Widget.Flashat.MediumRoundedButton.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.MediumRoundedButton.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.MediumRoundedButton.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.MediumRoundedButton.Gray">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Gray</item>
    </style>

    <style name="Widget.Flashat.MediumRoundedButton.Secondary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <!--  Small Rounded Button  -->

    <style name="Widget.Flashat.SmallRoundedButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Button</item>
        <item name="iconSize">18dp</item>
        <item name="iconGravity">textStart</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
    </style>

    <style name="ShapeAppearance.Flashat.SmallRoundButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">32dp</item>
    </style>

    <style name="Widget.Flashat.SmallRoundButton" parent="Widget.Flashat.SmallRoundedButton">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.SmallRoundButton</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.SmallText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Caption</item>
        <item name="iconSize">16dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.SmallRoundButton.Primary" parent="Widget.Flashat.SmallRoundedButton.Primary">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.SmallRoundButton</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Secondary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <style name="Widget.Flashat.SmallRoundButton.Secondary" parent="Widget.Flashat.SmallRoundedButton.Secondary">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.SmallRoundButton</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.SmallRoundButton.Negative" parent="Widget.Flashat.SmallRoundedButton.Negative">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.SmallRoundButton</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Inverse.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse.Negative</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Outline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/outlined_button_stroke_selector</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
        <item name="strokeWidth">1dp</item>
        <item name="iconSize">18dp</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Outline.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>


    <style name="Widget.Flashat.SmallRoundedButton.Outline.Gray">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Gray</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.LowerCase">
        <item name="textAllCaps">false</item>
    </style>

    <style name="Widget.Flashat.SmallRoundedButton.Inverse.Lowercase">
        <item name="textAllCaps">false</item>
    </style>

    <!--  Mini Rounded Button  -->

    <style name="Widget.Flashat.MiniRoundedButton" parent="Widget.MaterialComponents.Button">
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:paddingHorizontal">12dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.MiniRoundedButton</item>
        <item name="iconSize">14dp</item>
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">42dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Outline.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Outline.Grey">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Gray</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Outline.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Outline.Inverse.Secondary" parent="Widget.Flashat.MiniRoundedButton.Outline">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse.Secondary</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.CustomBG">
        <item name="backgroundTint">@null</item>
        <item name="android:minHeight">32dp</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Outline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
        <item name="strokeColor">@color/outlined_button_stroke_selector</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:paddingHorizontal">12dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.MiniRoundedButton</item>
        <item name="iconSize">14dp</item>
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">42dp</item>
    </style>

    <style name="Widget.Flashat.MiniRoundedButton.Outline.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <!-- Tiny Button -->
    <style name="Widget.Flashat.TinyRoundedButton" parent="Widget.MaterialComponents.Button">
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:paddingHorizontal">6dp</item>
        <item name="iconPadding">2dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.TinyRoundedButton</item>
        <item name="iconSize">16dp</item>
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">38dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
    </style>

    <style name="Widget.Flashat.TinyRoundedButton.Outline" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/outlined_button_stroke_selector</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:paddingHorizontal">6dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.TinyRoundedButton</item>
        <item name="iconSize">16dp</item>
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">38dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>

    </style>

    <style name="Widget.Flashat.TinyRoundedButton.Outline.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.TinyRoundedButton.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.TinyRoundedButton.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>
    <style name="Widget.Flashat.TinyRoundedButton.CustomBG">
        <item name="backgroundTint">@null</item>
    </style>

    <!--  Icon Only Button  -->
    <style name="Widget.Flashat.Button.IconOnly" parent="Widget.MaterialComponents.Button">
        <item name="iconPadding">0dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="iconSize">24dp</item>
    </style>

    <style name="Widget.Flashat.Button.IconOnly.Small">
        <item name="android:minWidth">40dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:padding">10dp</item>
        <item name="iconSize">20dp</item>
    </style>

    <style name="Widget.Flashat.Button.IconOnly.Small.Round">
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.SmallRoundButton</item>
    </style>

    <style name="Widget.Flashat.Button.IconOnly.Mini">
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:padding">6dp</item>
        <item name="iconSize">20dp</item>
    </style>

    <style name="Widget.Flashat.Button.IconOnly.Tiny">
        <item name="android:minWidth">24dp</item>
        <item name="android:minHeight">24dp</item>
        <item name="android:padding">2dp</item>
        <item name="iconSize">20dp</item>
    </style>

    <style name="Widget.Flashat.Button.IconOnly.Tiny.Secondary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="iconPadding">0dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:padding">12dp</item>
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="iconSize">24dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Text</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.Negative">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Negative</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.OnPrimary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse.OnPrimary</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.Small">
        <item name="android:minWidth">40dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:padding">10dp</item>
        <item name="iconSize">20dp</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.Small.Primary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Primary</item>
    </style>

    <style name="Widget.Flashat.Button.TextButton.IconOnly.Tiny">
        <item name="android:minWidth">24dp</item>
        <item name="android:minHeight">24dp</item>
        <item name="android:padding">2dp</item>
        <item name="iconSize">20dp</item>
        <item name="iconTint">@null</item>
    </style>

    <style name="Widget.Flashat.Button.VerticalIconButton" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="iconPadding">0dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:paddingLeft">6dp</item>
        <item name="android:paddingRight">6dp</item>
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="iconSize">24dp</item>
        <item name="iconGravity">top</item>
        <item name="textAllCaps">false</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Body2</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
    </style>

    <style name="Widget.Flashat.Button.VerticalIconButton.OnPrimary">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Inverse.OnPrimary</item>
    </style>

    <style name="Widget.Flashat.Button.VerticalIconButton.SecondaryText">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <style name="Widget.Flashat.Button.VerticalIconButton.Secondary" parent="Widget.MaterialComponents.Button">
        <item name="iconPadding">0dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:paddingLeft">6dp</item>
        <item name="android:paddingRight">6dp</item>
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="iconSize">24dp</item>
        <item name="iconGravity">top</item>
        <item name="textAllCaps">false</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.Body2</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button.Secondary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button" parent="">
        <item name="colorPrimary">?attr/buttonBaseColor</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">?attr/buttonBaseColor</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Primary" parent="">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorPrimary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Gray" parent="">
        <item name="colorPrimary">@color/textColorSecondaryLight</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/textColorSecondaryLight</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Text" parent="">
        <item name="colorPrimary">@color/textColorSecondary</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/textColorSecondary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Secondary" parent="">
        <item name="colorPrimary">@color/colorSecondary</item>
        <item name="colorOnPrimary">@color/textColorOnSecondary</item>
        <item name="colorOnSurface">@color/colorSecondary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Negative" parent="">
        <item name="colorPrimary">@color/colorError</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorError</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Success" parent="">
        <item name="colorPrimary">@color/colorPass</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorPass</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Orange" parent="">
        <item name="colorPrimary">@color/colorOrange</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorOrange</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Inverse.Secondary" parent="">
        <item name="colorPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorSecondary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Inverse" parent="">
        <item name="colorPrimary">@color/colorSurface</item>
        <item name="colorOnPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.InverseDark" parent="">
        <item name="colorPrimary">@color/colorSurfaceSecondaryDark</item>
        <item name="colorOnPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Inverse.OnPrimary" parent="">
        <item name="colorPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnPrimary">@color/textColorSecondary</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Inverse.Negative" parent="">
        <item name="colorPrimary">@color/colorSurface</item>
        <item name="colorOnPrimary">@color/colorError</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <style name="TextAppearance.Flashat.LargeRoundedButton" parent="TextAppearance.Flashat.Button">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="TextAppearance.Flashat.MiniRoundedButton" parent="TextAppearance.Flashat.Button">
        <item name="android:textSize">13sp</item>
    </style>

    <style name="TextAppearance.Flashat.TinyRoundedButton" parent="TextAppearance.Flashat.Button">
        <item name="android:textSize">10sp</item>
    </style>

    <!--  Floating Action Button  -->

    <style name="Widget.Flashat.FAB" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.FloatingActionButton</item>
    </style>

    <style name="Widget.Flashat.FAB.Secondary" parent="Widget.MaterialComponents.FloatingActionButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.FloatingActionButton.Secondary</item>
    </style>

    <style name="Widget.Flashat.FAB.Inverse">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.FloatingActionButton.Inverse</item>
    </style>

    <style name="ThemeOverlay.Flashat.FloatingActionButton" parent="">
        <item name="colorSecondary">@color/colorPrimary</item>
        <item name="colorOnSecondary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorPrimaryLighter</item>
    </style>

    <style name="ThemeOverlay.Flashat.FloatingActionButton.Secondary">
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorOnSecondary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/textColorOnPrimary</item>
    </style>

    <style name="ThemeOverlay.Flashat.FloatingActionButton.Inverse" parent="">
        <item name="colorSecondary">@color/colorSurfaceSecondary</item>
        <item name="colorOnSecondary">@color/textColorSecondary</item>
    </style>

    <!--  Custom Buttons  -->

    <style name="Widget.Flashat.HomeInnerTabButton" parent="Widget.Flashat.SmallRoundedButton.SmallText">
        <item name="backgroundTint">@drawable/sel_home_inner_tab_background</item>
        <item name="android:textColor">@drawable/sel_home_inner_tab_text_color</item>
        <item name="iconTint">@null</item>
        <item name="android:paddingStart">@dimen/activity_margin</item>
        <item name="android:paddingEnd">@dimen/activity_margin</item>
        <item name="android:minWidth">70dp</item>
    </style>

    <style name="Widget.Flashat.HomeInnerPostatMediaTabButton" parent="Widget.Flashat.SmallRoundedButton.SmallText">
        <item name="backgroundTint">@drawable/sel_postat_media_selection</item>
        <item name="android:textColor">@color/white</item>
        <item name="iconTint">@null</item>
        <item name="android:paddingStart">@dimen/activity_margin</item>
        <item name="android:paddingEnd">@dimen/activity_margin</item>
        <item name="android:minWidth">70dp</item>
    </style>

    <style name="Widget.Flashat.HomeInnerFlashTabButton" parent="Widget.Flashat.SmallRoundedButton.SmallText">
        <item name="backgroundTint">@drawable/sel_flash_search_type_background</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="iconTint">@null</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingStart">@dimen/activity_margin</item>
        <item name="android:paddingEnd">@dimen/activity_margin</item>
        <item name="android:minWidth">70dp</item>
    </style>

    <style name="Widget.Flashat.pollButton.outline" parent="Widget.Flashat.LargeRoundedButton.Outline">
        <item name="backgroundTint">@drawable/sel_home_inner_tab_background</item>
        <item name="android:textColor">@drawable/sel_huddle_polls_text_color</item>
        <item name="iconTint">@null</item>
        <item name="android:paddingStart">@dimen/activity_margin</item>
        <item name="android:paddingEnd">@dimen/activity_margin</item>
        <item name="android:minWidth">70dp</item>
    </style>


    <style name="Widget.Flashat.BroadcastTabButton" parent="Widget.Flashat.SmallRoundedButton">
        <item name="backgroundTint">@drawable/sel_broadcast_group_background</item>
        <item name="android:textColor">@drawable/sel_broadcast_group_text</item>
        <item name="iconTint">@drawable/sel_broadcast_group_text</item>
        <item name="iconSize">24dp</item>
    </style>

    <style name="ThemeOverlay.Flashat.Button.Positive" parent="">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorOnPrimary">@color/textColorOnPrimary</item>
        <item name="colorOnSurface">@color/colorPrimary</item>
    </style>

    <style name="Widget.Flashat.ReportedTabButton" parent="Widget.Flashat.HomeInnerTabButton">
        <item name="android:paddingStart">48dp</item>
        <item name="android:paddingEnd">48dp</item>
        <item name="shapeAppearance">@style/ShapeAppearance.Flashat.ReportedTabButton</item>
    </style>

    <style name="ShapeAppearance.Flashat.ReportedTabButton" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <!--    SWITCH   -->

    <style name="Widget.Flashat.Switch" parent="Widget.Material3.CompoundButton.MaterialSwitch">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Switch</item>
    </style>

    <style name="ThemeOverlay.Flashat.Switch" parent="">
        <item name="colorOutline">@color/colorSurface</item>
        <item name="colorOnPrimary">@color/colorSurface</item>
        <item name="colorSurfaceContainerHighest">@color/colorSurfaceSecondaryDarker</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="trackDecoration">@null</item>
    </style>

    <!--    Buttons for ID card -->
    <style name="Widget.Flashat.IDCard.NickNameButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.Button</item>
        <item name="strokeColor">@color/outlined_button_stroke_selector</item>
        <item name="strokeWidth">1dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
        <item name="android:paddingHorizontal">8dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Flashat.IDCard.NickNameButton</item>
        <item name="iconSize">12dp</item>
        <item name="iconGravity">end</item>
        <item name="android:minWidth">32dp</item>
        <item name="android:minHeight">32dp</item>
    </style>

    <style name="TextAppearance.Flashat.IDCard.NickNameButton" parent="TextAppearance.Flashat.Button">
        <item name="android:textSize">12sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Widget.Flashat.ProfileHeaderButton" parent="Widget.Flashat.Button.VerticalIconButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.ProfileHeaderButton</item>
    </style>


    <style name="ThemeOverlay.Flashat.ProfileHeaderButton" parent="">
        <item name="colorPrimary">?attr/toolbarTextColor</item>
        <item name="colorOnSurface">?attr/toolbarTextColor</item>
    </style>

    <style name="Widget.Flashat.DrawerHeaderButton" parent="Widget.Flashat.SmallRoundedButton">
        <item name="materialThemeOverlay">@style/ThemeOverlay.Flashat.DrawerHeaderButton</item>
    </style>

    <style name="ThemeOverlay.Flashat.DrawerHeaderButton" parent="">
        <item name="colorPrimary">@color/colorSurfaceSecondaryDark</item>
        <item name="colorOnPrimary">@color/textColorSecondaryLight</item>
    </style>

    <style name="Widget.Flashat.Followers_tab_button" parent="Widget.Flashat.HomeInnerTabButton">
        <item name="android:textColor">@drawable/sel_followers_tab_text_color</item>
    </style>

</resources>