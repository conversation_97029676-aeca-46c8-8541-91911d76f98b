<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable name="locationItem" type="com.app.messej.data.model.api.PlacesAutoCompleteResponse.Predictions"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/location_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="@dimen/double_margin"
            android:layout_marginStart="@dimen/double_margin"
            android:layout_marginBottom="@dimen/double_margin"
            android:src="@drawable/ic_search_location_pin"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            app:layout_constraintStart_toEndOf="@id/location_image"
            app:layout_constraintTop_toTopOf="@id/location_image"
            app:layout_constraintBottom_toTopOf="@id/sub_location"
            tools:text="Kochi"
            android:text="@{locationItem.StructuredFormatting.mainText}"
            style="@style/TextAppearance.Flashat.Subtitle1"
            android:textSize="18sp"
            />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/sub_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_margin"
            app:layout_constraintStart_toEndOf="@id/location_image"
            app:layout_constraintTop_toBottomOf="@id/location"
            app:layout_constraintBottom_toBottomOf="@id/location_image"
            tools:text="Kochi"
            android:text="@{locationItem.StructuredFormatting.secondaryText}"
            style="@style/TextAppearance.Flashat.Body1"
            android:textSize="14sp"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
