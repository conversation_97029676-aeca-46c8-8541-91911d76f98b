<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="com.app.messej.ui.home.businesstab.BusinessDealsListViewModel" />
        <variable name="isResident" type="Boolean" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/double_margin">

        <include
            android:id="@+id/layout_list_loading"
            layout="@layout/layout_restore_rating_loading"
            app:actionLoading="@{viewModel.getRestoreRatingShimmerLoading}" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_close"
            style="@style/Widget.Flashat.Button.TextButton.IconOnly"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/element_spacing"
            android:layout_marginEnd="@dimen/activity_margin"
            app:icon="@drawable/ic_close"
            app:iconTint="@color/textColorSecondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_header_restore_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            style="@style/TextAppearance.Flashat.Headline5"
            android:text="@string/restore_rating_header"
            android:textColor="@color/textColorSecondary"
            app:layout_constraintTop_toTopOf="@id/btn_close"
            app:layout_constraintBottom_toBottomOf="@id/btn_close"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"/>


        <com.google.android.material.button.MaterialButton
            android:id="@+id/about_info"
            style="@style/Widget.Flashat.Button.TextButton.IconOnly"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/element_spacing"
            android:layout_marginEnd="@dimen/activity_margin"
            app:icon="@drawable/ic_restore_info"
            app:iconTint="@color/textColorSecondary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btn_close"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"/>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView_rating"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/activity_margin"
            app:cardBackgroundColor="@{isResident?@color/colorBusinessGrey:@color/colorPrimary}"
            app:cardCornerRadius="10dp"
            app:cardElevation="5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/about_info"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/activity_margin"
                android:background="@{isResident?@color/colorSurfaceSecondaryDarker:@color/colorPrimary}">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_your_rating_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/restore_rating_your_rating_title"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    tools:textColor="@color/black"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_your_rating"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="90%"
                    android:text="@{viewModel.restoreRatingDetails.convertedRating}"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    tools:textColor="@color/black"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textAlignment="textEnd"
                    app:layout_constraintStart_toEndOf="@id/text_your_rating_title"
                    app:layout_constraintTop_toTopOf="@id/text_your_rating_title"
                    app:layout_constraintBottom_toBottomOf="@id/text_your_rating_title"/>

                <View
                    android:id="@+id/balance_bottom_divider_rating"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginVertical="@dimen/line_spacing"
                    android:background="@drawable/bg_business_horizontal_dotted_line"
                    android:backgroundTint="@{isResident?@color/black:@color/white}"
                    tools:backgroundTint="@color/colorBusinessGrey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_your_rating_title" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_restore_rating_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/restore_rating_cost_to_restore_rating_title"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    tools:textColor="@color/black"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/balance_bottom_divider_rating"
                    android:layout_marginTop="@dimen/element_spacing"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_restore_rating"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="FLiX 1000"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    tools:textColor="@color/black"
                    app:flaxValue="@{viewModel.restoreRatingDetails.restoratingFlix}"
                    app:flaxColor="@{isResident?@color/colorPrimary:@color/colorSecondary}"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textAlignment="textEnd"
                    app:layout_constraintStart_toEndOf="@id/text_restore_rating_title"
                    app:layout_constraintTop_toTopOf="@id/text_restore_rating_title"
                    app:layout_constraintBottom_toBottomOf="@id/text_restore_rating_title"/>



            </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardView_main"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/activity_margin"
            app:cardBackgroundColor="@{isResident?@color/colorBusinessGrey:@color/colorPrimary}"
            app:cardCornerRadius="10dp"
            app:cardElevation="5dp"
            android:layout_marginTop="@dimen/activity_margin"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardView_rating"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="@dimen/activity_margin"
                android:background="@{isResident?@color/colorSurfaceSecondaryDarker:@color/colorPrimary}">




            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_flix_balance_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/restore_rating_your_flix_balance_title"
                android:textColor="@{isResident?@color/black:@color/white}"
                style="@style/TextAppearance.Flashat.Body2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:textColor="@color/black"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/text_flix_balance"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                tools:text="FLiX 100"
                android:textColor="@{isResident?@color/black:@color/white}"
                tools:textColor="@color/black"
                app:flaxValue="@{viewModel.restoreRatingDetails.flixBalance}"
                app:flaxColor="@{isResident?@color/colorPrimary:@color/colorSecondary}"
                style="@style/TextAppearance.Flashat.Body2"
                app:layout_constraintEnd_toEndOf="parent"
                android:textAlignment="textEnd"
                app:layout_constraintStart_toEndOf="@id/text_flix_balance_title"
                app:layout_constraintTop_toTopOf="@id/text_flix_balance_title"
                app:layout_constraintBottom_toBottomOf="@id/text_flix_balance_title"/>

                <View
                    android:id="@+id/balance_bottom_divider_flix"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginVertical="@dimen/line_spacing"
                    android:background="@drawable/bg_business_horizontal_dotted_line"
                    android:backgroundTint="@{isResident?@color/black:@color/white}"
                    tools:backgroundTint="@color/colorBusinessGrey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_flix_balance_title" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_coin_balance_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/restore_rating_your_coins_balance_title"
                    tools:textColor="@color/black"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginTop="@dimen/element_spacing"
                    app:layout_constraintTop_toBottomOf="@id/balance_bottom_divider_flix"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_coin_balance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="COiNS  20"
                    android:text="@{@string/restore_rating_coins(viewModel.restoreRatingDetails.coinBalance.toString())}"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    tools:textColor="@color/black"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textAlignment="textEnd"
                    app:layout_constraintStart_toEndOf="@id/text_coin_balance_title"
                    app:layout_constraintTop_toTopOf="@id/text_coin_balance_title"
                    app:layout_constraintBottom_toBottomOf="@id/text_coin_balance_title"/>

                <View
                    android:id="@+id/balance_bottom_divider_coin"
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginVertical="@dimen/line_spacing"
                    android:background="@drawable/bg_business_horizontal_dotted_line"
                    android:backgroundTint="@{isResident?@color/black:@color/white}"
                    tools:backgroundTint="@color/colorBusinessGrey"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_coin_balance_title" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_effective_flix_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@string/restore_rating_effective_flix_title"
                    tools:textColor="@color/black"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_marginTop="@dimen/element_spacing"
                    app:layout_constraintTop_toBottomOf="@id/balance_bottom_divider_coin"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/text_effective_flix_balance"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    tools:text="FLiX 200"
                    app:flaxValue="@{viewModel.restoreRatingDetails.effectiveBalance}"
                    app:flaxColor="@{isResident?@color/colorPrimary:@color/colorSecondary}"
                    android:textColor="@{isResident?@color/black:@color/white}"
                    tools:textColor="@color/black"
                    style="@style/TextAppearance.Flashat.Body2"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:textAlignment="textEnd"
                    app:layout_constraintStart_toEndOf="@id/text_effective_flix_title"
                    app:layout_constraintTop_toTopOf="@id/text_effective_flix_title"
                    app:layout_constraintBottom_toBottomOf="@id/text_effective_flix_title"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/text_note"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/TextAppearance.Flashat.Label.Small"
            app:layout_constraintStart_toStartOf="@id/cardView_main"
            app:layout_constraintEnd_toEndOf="@id/cardView_main"
            app:layout_constraintTop_toBottomOf="@id/cardView_main"
            android:layout_marginTop="@dimen/element_spacing"
            android:visibility="gone"
            goneIfNot="@{!viewModel.restoreRatingDetails.enableRestoreButton}"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"
            android:text="@string/restore_rating_inSufficient_balance"
            android:textColor="@color/colorError"/>


        <Button
            android:id="@+id/restore_rating_button"
            style="@style/Widget.Flashat.LargeRoundedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/double_margin"
            android:layout_marginVertical="@dimen/double_margin"
            android:text="@string/common_accept"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_note"
            android:enabled="@{viewModel.restoreRatingDetails.enableRestoreButton}"
            goneIf="@{!viewModel.getRestoreRatingShimmerLoading}"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>