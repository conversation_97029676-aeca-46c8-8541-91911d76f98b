<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.app.messej.ui.utils.DataFormatHelper" />

        <import type="com.app.messej.data.model.enums.FlaxType" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.businesstab.BusinessStatementViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating"
            tools:visibility="gone" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fillViewport="true"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                >

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    app:goneIfNot="@{viewModel.statementLoading}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_business"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/text_statement_points"
                        style="@style/TextAppearance.Flashat.Subtitle2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/activity_margin"
                        android:text="@string/title_business_statement_performance_points"
                        android:textColor="@color/colorPrimary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/layout_performance_point"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/text_statement_points">

                        <include
                            layout="@layout/layout_business_statements_1"
                            app:flaxColor="@{@color/colorPrimary}"
                            app:itemTextColor="@{@color/textColorSecondary}"
                            app:statement="@{viewModel.businessStatement}" />
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/layout_pp_statement"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_performance_point">

                        <include
                            layout="@layout/layout_business_statement_2"
                            app:flaxColor="@{@color/colorPrimary}"
                            app:headerTextColor="@{@color/colorPrimary}"
                            app:itemTextColor="@{@color/textColorSecondary}"
                            app:statement="@{viewModel.businessStatement}" />
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/layout_pp_rate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:layout_marginTop="@dimen/element_spacing"
                        android:visibility="visible"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layout_pp_statement">

                        <include
                            layout="@layout/layout_business_statement_3"
                            app:flaxColor="@{@color/colorPrimary}"
                            app:headerTextColor="@{@color/colorPrimary}"
                            app:itemTextColor="@{@color/textColorSecondary}"
                            app:statement="@{viewModel.businessStatement}" />
                    </com.google.android.material.card.MaterialCardView>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/title_end_statement"
                        style="@style/TextAppearance.Flashat.Subtitle2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginVertical="@dimen/activity_margin"
                        android:layout_marginEnd="@dimen/element_spacing"
                        android:text="@string/title_business_end_of_statement"
                        android:textColor="@color/textColorBusinessSecondary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/layout_pp_rate"
                        tools:text="@string/title_business_end_of_statement" />
                    <Button
                        android:id="@+id/button_business_sell_flax"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/double_margin"
                        android:textAllCaps="false"
                        goneIf="@{viewModel.isVisitor}"
                        android:layout_marginEnd="@dimen/double_margin"
                        android:text="@string/title_withdraw_your_flax"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/title_end_statement" />


                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
