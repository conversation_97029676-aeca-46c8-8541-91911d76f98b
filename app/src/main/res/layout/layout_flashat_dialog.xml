<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="colorIcon" type="Boolean" />
        <variable name="headerText" type="String" />
        <variable name="alertIcon" type="android.graphics.drawable.Drawable" />
        <variable name="isCheckBoxSelected" type="boolean"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        tools:background="@color/textColorSecondary"
        android:minWidth="200dp">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/extra_margin"
            android:layout_marginVertical="@dimen/element_spacing"
            app:cardBackgroundColor="@color/colorSurface"
            app:cardCornerRadius="20dp"
            app:cardElevation="1dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="48dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/icon"
                    goneIfNull="@{alertIcon}"
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:layout_marginTop="@dimen/extra_margin"
                    android:adjustViewBounds="true"
                    android:src="@{alertIcon}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:src="@drawable/ic_rating_less" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:textAlignment="center"
                    android:textAppearance="@style/TextAppearance.Flashat.Headline5"
                    android:textColor="@color/textColorSecondary"
                    android:visibility="gone"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/icon"
                    app:layout_goneMarginTop="@dimen/extra_margin"
                    tools:text="Delete Challenge?"
                    tools:visibility="visible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/message"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/extra_margin"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:textAlignment="center"
                    android:textAppearance="@style/TextAppearance.Flashat.Body2"
                    android:textColor="@color/textColorSecondary"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title"
                    app:layout_goneMarginTop="@dimen/extra_margin"
                    tools:text="@string/podium_yalla_guys_join_confirm" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/checkBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/element_spacing"
                    android:paddingEnd="@dimen/activity_margin"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/message">

                    <com.google.android.material.checkbox.MaterialCheckBox
                        android:id="@+id/checkBox_control"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="@{isCheckBoxSelected}"
                        app:buttonTint="@color/textColorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:checked="true" />


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/checkBox_title"
                        style="@style/TextAppearance.Flashat.Label.Bold"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constrainedWidth="true"
                        tools:text="@string/podium_paid_like_check_box_validation"
                        android:textColor="@color/textColorPrimary"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/checkBox_control"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <app.juky.squircleview.views.SquircleConstraintLayout
            android:id="@+id/close_button"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:padding="16dp"
            app:layout_constraintBottom_toBottomOf="@+id/card"
            app:layout_constraintEnd_toStartOf="@+id/neutral_button"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@+id/card"
            app:layout_constraintTop_toBottomOf="@+id/card"
            app:squircle_background_color="@color/colorSurface"
            app:squircle_border_color="@color/colorError"
            app:squircle_border_width="6dp"
            app:squircle_shadow_elevation="2dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/close_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:adjustViewBounds="true"
                android:tint="@color/colorError"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_close" />

        </app.juky.squircleview.views.SquircleConstraintLayout>

        <app.juky.squircleview.views.SquircleConstraintLayout
            android:id="@+id/neutral_button"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/card"
            app:layout_constraintEnd_toStartOf="@+id/confirm_button"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@+id/close_button"
            app:layout_constraintTop_toBottomOf="@+id/card"
            app:squircle_background_color="@color/colorSurface"
            app:squircle_border_color="@color/colorPrimaryDark"
            app:squircle_border_width="6dp"
            app:squircle_shadow_elevation="2dp"
            tools:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/neutral_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:adjustViewBounds="true"
                android:tint="@color/colorPrimaryDark"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_plus" />

        </app.juky.squircleview.views.SquircleConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/neutral_button_text"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            tools:text="Neutral"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:layout_marginTop="@dimen/line_spacing"
            android:visibility="gone"
            tools:visibility="visible"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller.Bold"
            android:textColor="@color/textColorOnPrimary"
            app:layout_constrainedWidth="true"
            app:layout_constraintTop_toBottomOf="@id/neutral_button"
            app:layout_constraintStart_toStartOf="@id/neutral_button"
            app:layout_constraintEnd_toEndOf="@id/neutral_button"/>

        <app.juky.squircleview.views.SquircleConstraintLayout
            android:id="@+id/confirm_button"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            app:layout_constraintBottom_toBottomOf="@+id/card"
            app:layout_constraintEnd_toEndOf="@+id/card"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/neutral_button"
            app:layout_constraintTop_toBottomOf="@+id/card"
            app:squircle_background_gradient_end_color="@color/colorPrimaryDark"
            app:squircle_background_gradient_start_color="@color/colorPrimary"
            app:squircle_shadow_elevation="2dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/confirm_icon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:adjustViewBounds="true"
                app:srcCompat="@drawable/ic_chat_liked"
                android:tint="@color/colorSecondary"
                tools:padding="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_yalla_gamepad_square" />

        </app.juky.squircleview.views.SquircleConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/close_text"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:text="@string/common_close"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:layout_marginTop="@dimen/line_spacing"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller.Bold"
            android:textColor="@color/textColorOnPrimary"
            app:layout_constrainedWidth="true"
            app:layout_constraintTop_toBottomOf="@id/close_button"
            app:layout_constraintStart_toStartOf="@id/close_button"
            app:layout_constraintEnd_toEndOf="@id/close_button"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/confirm_text"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            tools:text="Create new Challenge"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:layout_marginTop="@dimen/line_spacing"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Smaller.Bold"
            android:textColor="@color/textColorOnPrimary"
            app:layout_constrainedWidth="true"
            app:layout_constraintTop_toBottomOf="@id/confirm_button"
            app:layout_constraintStart_toStartOf="@id/confirm_button"
            app:layout_constraintEnd_toEndOf="@id/confirm_button"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>