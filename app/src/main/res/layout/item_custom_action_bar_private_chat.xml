<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="androidx.lifecycle.LiveData"/>
        <variable name="chat" type="com.app.messej.data.model.entity.PrivateChat" />
        <variable name="typingInfo" type="com.app.messej.data.model.socket.HuddleTypingInfo" />
        <variable name="lastSeen" type="com.app.messej.data.model.socket.UserLastSeen" />
    </data>
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        app:liftOnScroll="false"
        android:elevation="6dp"
        android:background="?attr/toolbarColor"
        android:fitsSystemWindows="true"
        tools:showIn="@layout/fragment_chat_screen">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentScrim="@color/transparent"
            app:titleEnabled="false"
            android:fitsSystemWindows="true"
            app:layout_scrollFlags="noScroll">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:adjustViewBounds="true"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginTop="@dimen/element_spacing"
                    app:srcCompat="@drawable/bg_toolbar_circle_bottom_left" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:adjustViewBounds="true"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="@dimen/element_spacing"
                    app:srcCompat="@drawable/bg_toolbar_circle_top_right" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                style="@style/Widget.Flashat.Toolbar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/customActionBarHeight"
                android:elevation="0dp"
                android:clickable="@{!chat.receiverDetails.deletedAccount}"
                app:layout_collapseMode="pin"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cslUser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:animateLayoutChanges="false">

                    <com.makeramen.roundedimageview.RoundedImageView
                        android:id="@+id/chat_dp"
                        android:layout_width="@dimen/message_list_dp_size"
                        android:layout_height="@dimen/message_list_dp_size"
                        app:riv_corner_radius="@dimen/message_list_dp_size"
                        android:layout_marginVertical="@dimen/element_spacing"
                        android:layout_marginStart="2dp"
                        android:elevation="2dp"
                        android:scaleType="centerCrop"
                        app:imageUrl="@{chat.receiverDetails.thumbnail}"
                        app:placeholder="@{@drawable/im_user_placeholder_opaque}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:src="@drawable/im_user_placeholder_opaque" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/chat_name"
                        style="@style/TextAppearance.Flashat.Subtitle2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_margin"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{chat.receiverDetails.name}"
                        android:textColor="?attr/toolbarTextColor"
                        app:layout_constraintBottom_toTopOf="@id/chat_last_seen"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/chat_dp"
                        app:layout_constraintTop_toTopOf="@id/chat_dp"
                        app:layout_constraintVertical_chainStyle="packed"
                        tools:text="John Doe" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/superstar_premium_badge"
                        android:layout_width="@dimen/premium_badge_size"
                        android:layout_height="@dimen/premium_badge_size"
                        android:elevation="4dp"
                        android:translationZ="90dp"
                        app:layout_constraintStart_toStartOf="@id/chat_dp"
                        app:layout_constraintTop_toTopOf="@id/chat_dp"
                        app:userBadge="@{chat.receiverDetails.userBadge}"
                        tools:src="@drawable/ic_user_badge_premium" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/chat_online_icon"
                        android:layout_width="wrap_content"
                        android:layout_height="8dp"
                        android:adjustViewBounds="true"
                        android:tint="@color/colorPass"
                        app:goneIfNot="@{lastSeen.online &amp;&amp; !lastSeen.hideOnlineStatus}"
                        app:layout_constraintBottom_toBottomOf="@+id/chat_last_seen"
                        app:layout_constraintStart_toStartOf="@+id/chat_name"
                        app:layout_constraintTop_toBottomOf="@+id/chat_name"
                        app:srcCompat="@drawable/ic_dot" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/chat_seen_icon"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:adjustViewBounds="true"
                        android:tint="@color/colorBusinessYellow"
                        app:goneIf="@{lastSeen == null || lastSeen.online}"
                        tools:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@+id/chat_last_seen"
                        app:layout_constraintTop_toBottomOf="@+id/chat_name"
                        app:layout_constraintStart_toStartOf="@+id/chat_name"
                        app:srcCompat="@drawable/ic_eye_visible_white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/chat_last_seen"
                        style="@style/TextAppearance.Flashat.Caption"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/colorBusinessYellow"
                        app:goneIf="@{lastSeen==null}"
                        app:lastSeen="@{lastSeen}"
                        app:layout_constraintBottom_toBottomOf="@+id/chat_dp"
                        app:layout_constraintStart_toStartOf="@id/chat_name"
                        app:layout_constraintTop_toBottomOf="@+id/chat_name"
                        app:layout_goneMarginStart="0dp"
                        tools:text="Online" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/subtitle_typing"
                        style="@style/TextAppearance.Flashat.TypingInfo"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/line_spacing"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@{@string/chat_typing_info(typingInfo.senderName)}"
                        android:textColor="?attr/toolbarTextColor"
                        app:goneIf="@{typingInfo==null}"
                        app:layout_constraintBottom_toBottomOf="@+id/chat_last_seen"
                        app:layout_constraintEnd_toEndOf="@+id/chat_last_seen"
                        app:layout_constraintStart_toStartOf="@+id/chat_last_seen"
                        app:layout_constraintTop_toTopOf="@id/chat_last_seen"
                        app:layout_goneMarginEnd="@dimen/activity_margin"
                        tools:text="martin2 is typing..."
                        tools:visibility="gone" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </com.google.android.material.appbar.MaterialToolbar>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

</layout>
