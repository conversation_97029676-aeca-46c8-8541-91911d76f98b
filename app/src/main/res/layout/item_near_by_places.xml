<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="locationItem"
            type="com.app.messej.data.model.api.NearbyPlacesCompleteResponse.Results" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/location_icon_mcv"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginVertical="@dimen/element_spacing"
            android:layout_marginStart="@dimen/activity_margin"
            app:cardBackgroundColor="@color/colorPrimaryLighter"
            app:cardCornerRadius="@dimen/superstar_stat_card_dp_size"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:imageUrl="@{locationItem.icon}"
                    tools:src="@drawable/ic_chat_like"
                    tools:tint="@color/colorPrimary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.google.android.material.card.MaterialCardView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/location"
            style="@style/TextAppearance.Flashat.Subtitle1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="@{locationItem.name}"
            app:layout_constraintBottom_toBottomOf="@+id/location_icon_mcv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/location_icon_mcv"
            app:layout_constraintTop_toTopOf="@+id/location_icon_mcv"
            tools:text="Kochi" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
