<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="statement" type="com.app.messej.data.model.entity.BusinessStatement.SocialTransactions" />
        <variable name="itemTextColor" type="Integer" />
        <variable name="headerTextColor" type="Integer" />
        <variable name="flaxColor" type="Integer" />
    </data>

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_left_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent=".08" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/bg_business_card_gradient" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_pp_statement"
        style="@style/TextAppearance.Flashat.Subtitle2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/activity_margin"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_social_affairs_transactions"
        android:textAllCaps="true"
        android:textColor="@{headerTextColor}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_sent_flax"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/statement_social_donations_received"
        android:textAllCaps="true"
        tools:textColor="@color/textColorPrimary"
        android:textColor="@{itemTextColor}"
        app:layout_constraintEnd_toStartOf="@id/sent_flax"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toEndOf="@id/guideline_left_1"
        app:layout_constraintTop_toBottomOf="@id/text_pp_statement" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/sent_flax"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.donationsReceived}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_sent_flax"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_sent_flax"
        app:layout_constraintTop_toTopOf="@+id/title_sent_flax"
        tools:text="@tools:sample/us_zipcodes" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_gifts_purchase"
        style="@style/TextAppearance.Flashat.Business.Caption"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/statement_social_withdrawn"
        android:textColor="@{itemTextColor}"
        android:textAllCaps="true"
        tools:textColor="@color/textColorPrimary"
        app:layout_constraintEnd_toStartOf="@id/gift_purchase_points"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="@id/title_sent_flax"
        app:layout_constraintTop_toBottomOf="@id/title_sent_flax" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/gift_purchase_points"
        style="@style/TextAppearance.Flashat.Business.Label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@color/textColorBusinessRed"
        app:flaxValue="@{statement.withdrawn}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_gifts_purchase"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_gifts_purchase"
        app:layout_constraintTop_toTopOf="@+id/title_gifts_purchase"
        tools:text="@tools:sample/us_zipcodes" />

    <View
        android:id="@+id/balance_top_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/activity_margin"
        android:background="@drawable/bg_business_horizontal_dotted_line"
        android:backgroundTint="@{itemTextColor}"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gift_purchase_points" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title_available_balance"
        style="@style/TextAppearance.Flashat.Subtitle2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/activity_margin"
        android:layout_marginTop="@dimen/activity_margin"
        android:text="@string/title_business_available_balance"
        android:textColor="@{headerTextColor}"
        app:layout_constraintHorizontal_chainStyle="spread_inside"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/balance_top_divider" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/available_balance"
        style="@style/TextAppearance.Flashat.Subtitle1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_margin"
        android:textAlignment="textEnd"
        android:textColor="@{itemTextColor}"
        app:flaxValue="@{statement.availableBalance}"
        app:flaxColor="@{flaxColor}"
        app:layout_constraintBottom_toBottomOf="@+id/title_available_balance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="spread"
        app:layout_constraintStart_toEndOf="@+id/title_available_balance"
        app:layout_constraintTop_toTopOf="@+id/title_available_balance"
        tools:text="@tools:sample/us_zipcodes" />

    <View
        android:id="@+id/balance_bottom_divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginVertical="@dimen/activity_margin"
        android:background="@drawable/bg_business_horizontal_dotted_line"
        android:backgroundTint="@{itemTextColor}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title_available_balance" />

</androidx.constraintlayout.widget.ConstraintLayout>

</layout>