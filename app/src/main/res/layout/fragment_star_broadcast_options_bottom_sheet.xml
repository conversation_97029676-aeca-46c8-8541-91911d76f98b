<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="viewModel"
            type="com.app.messej.ui.home.publictab.stars.StarBroadcastViewModel" />
    </data>
    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/extra_margin"
        android:paddingBottom="@dimen/extra_margin"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/starred_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            app:goneIf="@{viewModel.starUser.reported}"
            android:foreground="?attr/selectableItemBackground"
            android:paddingHorizontal="@dimen/activity_margin"
            android:paddingVertical="@dimen/activity_margin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/starred_icon"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_star"
                app:tint="@color/textColorSecondaryLight" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:text="@string/broadcast_starred_title"
                app:layout_constraintBottom_toBottomOf="@+id/starred_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/starred_icon"
                app:layout_constraintTop_toTopOf="@+id/starred_icon" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/unfollow_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            app:goneIf="@{viewModel.starUser.superStar}"
            android:foreground="?attr/selectableItemBackground"
            android:paddingHorizontal="@dimen/activity_margin"
            android:paddingVertical="@dimen/activity_margin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/unfollow_icon"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_user_unfollow"
                app:tint="@color/textColorSecondaryLight" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:text="@string/user_action_unfollow"
                app:layout_constraintBottom_toBottomOf="@+id/unfollow_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/unfollow_icon"
                app:layout_constraintTop_toTopOf="@+id/unfollow_icon" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/hide_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            app:goneIfNot="@{viewModel.starUser.superStar}"
            android:foreground="?attr/selectableItemBackground"
            android:paddingHorizontal="@dimen/activity_margin"
            android:paddingVertical="@dimen/activity_margin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/hide_icon"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_eye_invisible_white"
                app:tint="@color/textColorSecondaryLight" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:text="@string/common_hide"
                app:layout_constraintBottom_toBottomOf="@+id/hide_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/hide_icon"
                app:layout_constraintTop_toTopOf="@+id/hide_icon" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/report_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            app:goneIfNot="@{viewModel.canReport}"
            android:foreground="?attr/selectableItemBackground"
            android:paddingHorizontal="@dimen/activity_margin"
            android:paddingVertical="@dimen/activity_margin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/report_icon"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/colorError"
                app:srcCompat="@drawable/ic_report_circle" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="@color/colorError"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:text="@string/user_action_report"
                app:layout_constraintBottom_toBottomOf="@+id/report_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/report_icon"
                app:layout_constraintTop_toTopOf="@+id/report_icon" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/block_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            app:goneIf="@{viewModel.starUser.superStar}"
            android:foreground="?attr/selectableItemBackground"
            android:paddingHorizontal="@dimen/activity_margin"
            android:paddingVertical="@dimen/activity_margin">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/block_icon"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_user_block"
                app:tint="@color/textColorSecondaryLight" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextAppearance.Flashat.Subtitle1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/element_spacing"
                android:text="@string/user_action_block"
                app:layout_constraintBottom_toBottomOf="@+id/block_icon"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/block_icon"
                app:layout_constraintTop_toTopOf="@+id/block_icon" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>
</layout>