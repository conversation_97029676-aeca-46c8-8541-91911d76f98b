<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <data>
        <variable
            name="chatModel"
            type="com.app.messej.data.model.socket.PodiumLiveChat" />
        <variable
            name="dayNight"
            type="Boolean" />
        <import type="com.app.messej.data.model.enums.PodiumLiveChatType"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/user_dp"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            android:layout_marginVertical="@dimen/element_spacing"
            android:scaleType="centerCrop"
            app:imageUrl="@{chatModel.senderDetails.thumbnail}"
            app:layout_constraintBaseline_toBottomOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            app:placeholder="@{@drawable/im_user_placeholder_square}"
            app:riv_oval="true"
            tools:src="@drawable/im_user_placeholder_square" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:elevation="4dp"
            app:layout_constraintStart_toStartOf="@id/user_dp"
            app:layout_constraintTop_toTopOf="@id/user_dp"
            app:userBadgeOnPrimary="@{chatModel.senderDetails.userBadge}"
            tools:src="@drawable/ic_user_badge_premium" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/element_spacing"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/user_dp"
            app:layout_constraintTop_toTopOf="@id/user_dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/username"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constrainedWidth="true"
                android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
                tools:textColor="@color/colorLiveChatNewUser"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constraintEnd_toStartOf="@+id/flag"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="John Snow" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/flag"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginStart="@dimen/element_spacing"
                android:layout_marginEnd="@dimen/activity_margin"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/username"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:srcCompat="@drawable/flag_india" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/element_spacing"
            android:layout_marginEnd="@dimen/element_spacing"
            android:autoLink="web"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
            android:textColor="@{dayNight?@color/textColorSecondary:@color/textColorOnPrimaryLight}"
            app:layout_constrainedWidth="true"
            app:layout_constraintStart_toEndOf="@+id/user_dp"
            app:layout_constraintEnd_toStartOf="@id/paid_like"
            app:layout_constraintTop_toBottomOf="@id/header"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintHorizontal_chainStyle="packed"
            tools:text="This is a dummy Chat Message"
            tools:textColor="@color/textColorOnPrimaryLight" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/paid_like"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="@dimen/activity_margin"
            app:goneIfNot="@{chatModel.chatType==PodiumLiveChatType.PAID_LIKE}"
            app:srcCompat="@drawable/ic_podium_like"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintStart_toEndOf="@+id/message"
            app:layout_constraintTop_toTopOf="@+id/message"
            app:layout_constraintBottom_toBottomOf="@+id/message" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>