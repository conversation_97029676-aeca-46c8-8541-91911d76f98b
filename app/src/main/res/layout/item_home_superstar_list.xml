<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View"/>
        <variable name="superstar" type="com.app.messej.data.model.entity.UserStar"/>
        <variable name="clickable" type="Boolean"/>
        <variable name="badge" type="String"/>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/superstar_status_layout"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_marginTop="@dimen/line_spacing"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:background="@drawable/bg_superstar_status"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/my_superstar_text"
                style="@style/TextAppearance.Flashat.Subtitle2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/activity_margin"
                android:text="@string/stars_list_my_superstar_text"
                android:textColor="@color/textColorOnPrimary"
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/superstar_visibility_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/element_spacing"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:src="@drawable/ic_eye_invisible_white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/superstar_mute_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/element_spacing"
                tools:src="@drawable/ic_mute"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/superstar_visibility_icon"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/user_layout"
            layout="@layout/item_user_with_action_stats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:user="@{superstar}"
            app:goneIf="@{superstar.hidden}"
            app:action="@{null}"
            app:clickable="@{clickable}"
            app:badge="@{badge}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/superstar_status_layout" />

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/element_spacing"
            android:background="@color/colorDividerLight"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/user_layout" />


    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>