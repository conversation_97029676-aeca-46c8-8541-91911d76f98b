<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />
        <variable
            name="loaderLiveData"
            type="Boolean" />

        <variable
            name="viewModel"
            type="com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/custom_action_bar"
            layout="@layout/item_custom_action_bar_rating" />
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="16dp">

                    <com.google.android.material.progressindicator.CircularProgressIndicator
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:elevation="3dp"
                        android:indeterminate="true"
                        app:goneIfNot="@{loaderLiveData}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- Header Section -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/headerLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/textView3"
                                style="@style/TextAppearance.Flashat.Subtitle2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/business_sell_flax_already_applied"
                                android:textColor="@color/colorPrimary"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/imageView2" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/imageView2"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:src="@drawable/ic_tick_business"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/textView4"
                                style="@style/TextAppearance.Flashat.Label.Small"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="8dp"
                                android:layout_marginEnd="16dp"
                                android:layout_marginBottom="8dp"
                                android:text="@string/business_sell_flax_application_warning"
                                android:textColor="@color/textColorPrimary"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@+id/textView3" />
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </com.google.android.material.card.MaterialCardView>


                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/cardView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="60dp"
                        app:cardBackgroundColor="@color/colorSurfaceSecondary"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="4dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/headerLayout">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_payout_already_applied">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_redeem_flax_amount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="16dp"
                                android:layout_marginTop="16dp"
                                android:text="@string/already_redeemed_amount_text"
                                android:textColor="@color/white"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_redeem_flax_amount_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:text="@string/common_flix"
                                android:textColor="@color/colorSecondary"
                                android:textStyle="italic"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_redeem_flax_amount"
                                app:layout_constraintEnd_toStartOf="@id/txt_redeem_flax_amount_value"
                                app:layout_constraintTop_toTopOf="@+id/txt_redeem_flax_amount" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_redeem_flax_amount_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="16dp"
                                android:text="@{String.valueOf(viewModel.payoutEligibility.payoutData.requestedPointsForReview)}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_redeem_flax_amount"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_redeem_flax_amount" />

                            <View
                                android:id="@+id/divider"
                                android:layout_width="0dp"
                                android:layout_height="2dp"
                                android:alpha=".6"
                                android:background="@drawable/bg_business_horizontal_dotted_line"
                                app:layout_constraintEnd_toEndOf="@+id/txt_redeem_flax_amount_value"
                                app:layout_constraintStart_toStartOf="@+id/txt_redeem_flax_amount"
                                app:layout_constraintTop_toBottomOf="@+id/txt_redeem_flax_amount" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_processing_fees"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/already_processing_fees"
                                android:textColor="@color/white"
                                app:layout_constraintStart_toStartOf="@+id/txt_redeem_flax_amount"
                                app:layout_constraintTop_toBottomOf="@+id/divider" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_processing_fees_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:text="@string/common_flix"
                                android:textColor="@color/colorSecondary"
                                android:textStyle="italic"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_processing_fees"
                                app:layout_constraintEnd_toStartOf="@id/txt_processing_fees_value"
                                app:layout_constraintTop_toTopOf="@+id/txt_processing_fees" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_processing_fees_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:text="@{String.valueOf(viewModel.payoutEligibility.payoutData.processingFee)}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_processing_fees"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_processing_fees" />

                            <View
                                android:id="@+id/divider1"
                                android:layout_width="0dp"
                                android:layout_height="2dp"
                                android:alpha=".6"
                                android:background="@drawable/bg_business_horizontal_dotted_line"
                                app:layout_constraintEnd_toEndOf="@+id/txt_processing_fees_value"
                                app:layout_constraintStart_toStartOf="@+id/txt_processing_fees"
                                app:layout_constraintTop_toBottomOf="@+id/txt_processing_fees" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_transfer_fees"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/already_transfer_fees"
                                android:textColor="@color/white"
                                app:layout_constraintStart_toStartOf="@+id/txt_processing_fees"
                                app:layout_constraintTop_toBottomOf="@+id/divider1" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_transfer_amount_label"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:text="@string/common_flix"
                                android:textColor="@color/colorSecondary"
                                android:textStyle="italic"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_transfer_fees"
                                app:layout_constraintEnd_toStartOf="@id/txt_transfer_fees_value"
                                app:layout_constraintTop_toTopOf="@+id/txt_transfer_fees" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_transfer_fees_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:text="@{String.valueOf(viewModel.payoutEligibility.payoutData.transferFeeRounded)}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_transfer_fees"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_transfer_fees" />

                            <View
                                android:id="@+id/divider_transfer_fees"
                                android:layout_width="0dp"
                                android:layout_height="2dp"
                                android:alpha=".6"
                                android:background="@drawable/bg_business_horizontal_dotted_line"
                                app:layout_constraintEnd_toEndOf="@+id/txt_transfer_fees_value"
                                app:layout_constraintStart_toStartOf="@+id/txt_transfer_fees"
                                app:layout_constraintTop_toBottomOf="@+id/txt_transfer_fees" />

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/mena_fees_layout"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                app:goneIfNot="@{viewModel.payoutEligibility.payoutData.isPaymentTypeMENA}"
                                app:layout_constraintTop_toBottomOf="@+id/divider_transfer_fees"
                                app:layout_constraintStart_toStartOf="@+id/txt_transfer_fees"
                                app:layout_constraintEnd_toEndOf="@+id/txt_transfer_fees_value">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/txt_mena_processing_fees"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="8dp"
                                    android:text="@string/business_sell_flax__mena_fees"
                                    android:textColor="@color/white"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/txt_mena_amount_label"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    android:text="@string/common_flix"
                                    android:textColor="@color/colorSecondary"
                                    android:textStyle="italic"
                                    app:layout_constraintBottom_toBottomOf="@+id/txt_mena_processing_fees"
                                    app:layout_constraintEnd_toStartOf="@id/txt_mena_fees_value"
                                    app:layout_constraintTop_toTopOf="@+id/txt_mena_processing_fees" />


                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/txt_mena_fees_value"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="10dp"
                                    android:text="@{String.valueOf(viewModel.payoutEligibility.payoutData.menaFeesRounded)}"
                                    android:textColor="@color/white"
                                    app:layout_constraintBottom_toBottomOf="@+id/txt_mena_amount_label"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintTop_toTopOf="@+id/txt_mena_amount_label" />

                                <View
                                    android:id="@+id/divider_mena_processing_fees"
                                    android:layout_width="0dp"
                                    android:layout_height="2dp"
                                    android:alpha=".6"
                                    android:background="@drawable/bg_business_horizontal_dotted_line"
                                    app:layout_constraintEnd_toEndOf="@+id/txt_mena_fees_value"
                                    app:layout_constraintHorizontal_bias="0.0"
                                    app:layout_constraintStart_toStartOf="@+id/txt_mena_processing_fees"
                                    app:layout_constraintTop_toBottomOf="@+id/txt_mena_processing_fees" />

                            </androidx.constraintlayout.widget.ConstraintLayout>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_your_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/business_sell_flax__rating_when_applied"
                                android:textColor="@color/white"
                                app:layout_constraintStart_toStartOf="@+id/txt_processing_fees"
                                app:layout_constraintTop_toBottomOf="@+id/mena_fees_layout" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_your_rating_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:text="@{@string/title_flax_rate_today_value(String.valueOf(viewModel.flaxRateToday))}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_your_rating"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_your_rating" />

                            <View
                                android:id="@+id/divider2"
                                android:layout_width="0dp"
                                android:layout_height="2dp"
                                android:alpha=".6"
                                android:background="@drawable/bg_business_horizontal_dotted_line"
                                app:layout_constraintEnd_toEndOf="@+id/txt_your_rating_value"
                                app:layout_constraintStart_toStartOf="@+id/txt_your_rating"
                                app:layout_constraintTop_toBottomOf="@+id/txt_your_rating" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_receivable"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/business_sell_flax_receivable"
                                android:textColor="@color/white"
                                app:layout_constraintStart_toStartOf="@+id/txt_your_rating"
                                app:layout_constraintTop_toBottomOf="@+id/divider2" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_receivable_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:text="@{String.valueOf(viewModel.payoutEligibility.payoutData.receivableRounded)}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_receivable"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_receivable" />

                            <View
                                android:id="@+id/divider4"
                                android:layout_width="0dp"
                                android:layout_height="2dp"
                                android:alpha=".6"
                                android:background="@drawable/bg_business_horizontal_dotted_line"
                                app:layout_constraintEnd_toEndOf="@+id/txt_receivable_value"
                                app:layout_constraintStart_toStartOf="@+id/txt_receivable"
                                app:layout_constraintTop_toBottomOf="@+id/txt_receivable" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_date_applied"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:text="@string/business_sell_flax_date_applied"
                                android:textColor="@color/white"
                                app:layout_constraintStart_toStartOf="@+id/txt_receivable"
                                app:layout_constraintTop_toBottomOf="@+id/divider4" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_date_applied_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:text="@{String.valueOf(viewModel.payoutEligibility.payoutData.requestedDateFormatted)}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_date_applied"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_date_applied" />

                            <View
                                android:id="@+id/divider5"
                                android:layout_width="0dp"
                                android:layout_height="2dp"
                                android:alpha=".6"
                                android:background="@drawable/bg_business_horizontal_dotted_line"
                                app:layout_constraintEnd_toEndOf="@+id/txt_date_applied_value"
                                app:layout_constraintStart_toStartOf="@+id/txt_date_applied"
                                app:layout_constraintTop_toBottomOf="@+id/txt_date_applied" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:layout_marginBottom="8dp"
                                android:text="@string/business_sell_flax_status"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="@+id/txt_date_applied"
                                app:layout_constraintTop_toBottomOf="@+id/divider5" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/txt_status_value"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:text="@{viewModel.payoutEligibility.payoutData.payoutStatus}"
                                android:textColor="@color/white"
                                app:layout_constraintBottom_toBottomOf="@+id/txt_status"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="@+id/txt_status" />

                        </androidx.constraintlayout.widget.ConstraintLayout>


                    </com.google.android.material.card.MaterialCardView>

                    <Button
                        android:id="@+id/cancelButton"
                        style="@style/Widget.Flashat.LargeRoundedButton.Outline.Negative"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:enabled="@{viewModel.payoutEligibility.payoutData.payoutCancellable}"
                        android:text="@string/business_cancel_application"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/cardView"
                        tools:enabled="false" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>