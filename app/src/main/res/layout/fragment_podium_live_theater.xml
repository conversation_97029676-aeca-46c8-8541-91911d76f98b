<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable name="viewModel" type="com.app.messej.ui.home.publictab.podiums.live.PodiumLiveViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/main_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/colorSurface"
                app:applySystemBarInsets="@{`ime`}">

                <include layout="@layout/layout_podium_header"
                    android:id="@+id/header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:viewModel="@{viewModel}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.compose.ui.platform.ComposeView
                    android:id="@+id/ticker"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:composableName="com.app.messej.ui.home.promobar.PromoBarKt.PromoBarPreviewSingle"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/header"/>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/action_decor_holder_top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ticker">

                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/speaker_section"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/like_container"
                    app:layout_constraintTop_toBottomOf="@+id/action_decor_holder_top">

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/speaker_split_line"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.5" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/speaker_split_line_horz"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layout_constraintGuide_percent="0.5" />
                    
                    <View
                        android:id="@+id/main_screen_ideal"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintDimensionRatio="9:16"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/speaker_split_line" />

                    <View
                        android:id="@+id/main_screen_ideal_line_horz"
                        android:layout_width="1dp"
                        android:layout_height="1dp"
                        app:layout_constraintTop_toBottomOf="@id/main_screen_ideal"
                        app:layout_constraintStart_toStartOf="parent"/>
                    
                    <androidx.constraintlayout.widget.Barrier
                        android:id="@+id/main_screen_barrier"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:barrierDirection="top"
                        app:constraint_referenced_ids="speaker_split_line_horz,main_screen_ideal_line_horz"/>

                    <include
                        android:id="@+id/main_screen"
                        layout="@layout/item_podium_speaker_main"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        tools:visibility="visible"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/main_screen_barrier"
                        app:layout_constraintEnd_toStartOf="@+id/speaker_split_line" />

                    <include
                        android:id="@+id/main_screen_two"
                        layout="@layout/item_podium_speaker_main"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        tools:visibility="visible"
                        app:iAmManager="@{viewModel.iAmManager}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@id/main_screen_barrier"
                        app:layout_constraintStart_toEndOf="@+id/speaker_split_line" />

                    <com.kennyc.view.MultiStateView
                        android:id="@+id/speaker_list_multiStateView"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/main_screen_barrier"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:msv_emptyView="@layout/layout_list_state_empty"
                        app:msv_errorView="@layout/layout_list_state_error"
                        app:msv_loadingView="@layout/layout_eds_state_loading_podium_speakers"
                        app:msv_viewState="content">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/speaker_list"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:itemCount="16"
                            tools:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            tools:listitem="@layout/item_podium_speaker"
                            tools:spanCount="4" />

                    </com.kennyc.view.MultiStateView>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <include
                    android:id="@+id/likes_container"
                    layout="@layout/item_podium_likes_container" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/anthem_overlay_holder"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/speaker_section"
                    android:background="@color/colorPodiumAnthemBG"
                    app:layout_constraintBottom_toBottomOf="@id/speaker_section">

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/anthem_split_line_horz"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        app:layout_constraintGuide_percent="0.5" />

                    <androidx.compose.ui.platform.ComposeView
                        android:id="@+id/anthem_overlay"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toTopOf="@+id/anthem_split_line_horz"
                        tools:visibility="visible" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/chat_holder"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:visibility="visible"
                    tools:visibility="visible"
                    app:goneIf="@{viewModel.showTheaterChatInput}"
                    app:layout_constraintTop_toTopOf="@id/like_container"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/like_container"
                    app:layout_constraintStart_toStartOf="parent"
                    app:applySystemBarInsets="@{`bottom`}"
                    app:msv_emptyView="@layout/layout_podium_chat_paused_empty"
                    app:msv_errorView="@layout/layout_list_state_error"
                    app:msv_viewState="content">

                    <com.google.android.material.card.MaterialCardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/activity_margin"
                        android:layout_marginEnd="@dimen/element_spacing"
                        app:cardBackgroundColor="@color/colorPrimaryLightest"
                        app:cardCornerRadius="24dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/add_chat_button"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/appCompatImageView17"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_margin="@dimen/line_spacing"
                                android:background="@drawable/ic_dot"
                                android:backgroundTint="@color/colorSurface"
                                android:padding="5dp"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent"
                                app:srcCompat="@drawable/ic_notification_bell"
                                app:tint="@color/colorPrimary" />

                            <androidx.compose.ui.platform.ComposeView
                                android:id="@+id/welcome_holder"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/element_spacing"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toEndOf="@+id/appCompatImageView17"
                                app:layout_constraintTop_toTopOf="parent"/>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/add_chat_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Widget.Flashat.Button.TextButton.IconOnly.Primary"
                        android:visibility="gone"
                        tools:visibility="visible"
                        app:goneIfNot="@{viewModel.iAmElevated}"
                        app:icon="@drawable/ic_comment"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/chat_input_holder"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    tools:visibility="gone"
                    android:background="@color/colorSurface"
                    app:goneIfNot="@{viewModel.showTheaterChatInput}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/like_container"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/like_container">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/chat_input"
                        style="@style/Widget.Flashat.GreyTextInput"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/element_spacing"
                        android:layout_marginEnd="@dimen/line_spacing"
                        app:counterEnabled="true"
                        app:counterMaxLength="150"
                        app:hintAnimationEnabled="false"
                        app:hintEnabled="false"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/chat_cancel_button"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/input_comment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/chat_input_hint"
                            android:inputType="textShortMessage"
                            android:longClickable="false"
                            android:maxLength="150"
                            android:maxLines="1"
                            android:text="@={viewModel.chatText}"
                            android:textIsSelectable="false" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/chat_send_button"
                        style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        app:goneIfNot="@{viewModel.chatText.trim().length>0}"
                        app:icon="@drawable/ic_chat_send"
                        app:iconTint="@color/colorPrimary"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/chat_input" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/chat_cancel_button"
                        style="@style/Widget.Flashat.Button.TextButton.IconOnly"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:visibility="visible"
                        app:goneIf="@{viewModel.chatText.trim().length>0}"
                        app:icon="@drawable/ic_close"
                        app:iconTint="@color/colorPrimary"
                        app:layout_constraintEnd_toStartOf="@id/chat_send_button"
                        app:layout_constraintTop_toTopOf="@id/chat_input" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/like_container"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:goneIfNot="@{viewModel.showLikeAction}"
                    android:layout_marginBottom="@dimen/element_spacing"
                    android:layout_marginEnd="@dimen/line_spacing"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:applySystemBarInsets="@{`bottom`}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/action_like"
                        style="@style/Widget.Flashat.PaidLikeButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:icon="@{viewModel.podium.likesDisabled?@drawable/ic_podium_like_disable:@drawable/ic_podium_like}"
                        tools:icon="@drawable/ic_podium_like"
                        android:enabled="@{!viewModel.likeButtonEnabled}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</layout>
