<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >
    <data>
        <variable
            name="chatModel"
            type="com.app.messej.data.model.socket.PodiumMaidanLikePayload" />
        <variable
            name="dayNight"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/like_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginHorizontal="@dimen/activity_margin"
            app:layout_constraintBottom_toBottomOf="@+id/user_dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/user_dp"
            tools:srcCompat="@drawable/ic_maidan_heart_yellow" />

        <com.makeramen.roundedimageview.RoundedImageView
            android:id="@+id/user_dp"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_marginHorizontal="@dimen/element_spacing"
            android:layout_marginVertical="@dimen/element_spacing"
            android:scaleType="centerCrop"
            app:imageUrl="@{chatModel.user.thumbnail}"
            app:layout_constraintBaseline_toBottomOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/like_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            app:placeholder="@{@drawable/im_user_placeholder_square}"
            app:riv_oval="true"
            tools:src="@drawable/im_user_placeholder_square" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/superstar_premium_badge"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:elevation="4dp"
            app:layout_constraintStart_toStartOf="@id/user_dp"
            app:layout_constraintTop_toTopOf="@id/user_dp"
            app:userBadgeOnPrimary="@{chatModel.user.userBadge}"
            tools:src="@drawable/ic_user_badge_premium" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_margin"
            android:layout_marginStart="@dimen/element_spacing"
            android:layout_marginVertical="@dimen/element_spacing"
            android:textAppearance="@style/TextAppearance.Flashat.Label.Small"
            android:textColor="@{dayNight?@color/textColorSecondary:@color/textColorOnPrimaryLight}"
            tools:textColor="@color/textColorOnPrimaryLight"
            android:autoLink="web"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/user_dp"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="This is a dummy Chat Message" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>